## 1 系统简介绍
信飞Java技术栈标准示范系统，3个用途：
- 技术中心内网服务端，我们可以在这个工程上尝试各种新的技术、新的想法
- 新建Java系统时直接参考本系统，不允许使用不同的中间件、不允许使用不同的工程结构、不运行使用不同的规范
- 技术中心新员工入职，必须在该系统上完成一个功能的完整研发上线流程后，才允许改动生产代码

## 2 系统工程结构说明
- 底层依赖  
  底层依赖于信飞自己封装的jar包(xfframework)，该jar包封装了限流、配置中心（apollo）、服务调用（feign）等相关配置。
- 内部工程说明  
  biz：业务逻辑实现，工程的配置都在这里。实现前端页面需要的接口，调用dal的接口访问存储，调用itl的接口访问外部服务。  
  dal：存储访问层。所有的DB操作全部封装在这个工程。  
  itl：服务集成层。所有外部服务的访问必须封装在这个工程。

## 3 本地编译及启动
### 3.1 前置准备
请根据新人入职手册，把本地环境搭建好，包含Jdk、Git、Maven、IDEA。  
并且把IDEA代码生成模版配置好，这个很重要，不要写出三无代码（无文件头注释、无类头注释、无方法头注释）来。  
三无代码显得很业余，一点都不尊重计算机。  
详情请参考[新人入职手册](https://confluence.joyborrow.com/pages/viewpage.action?pageId=30482387)。

### 3.2 本地编译
- 拉代码  
  在自己的电脑上，建一个专门用来放代码的文件夹，这个文件夹是很神圣的，取个带劲一点的名称，最好全路径上不要有空格和非字母等奇怪字符。  
  命令行进入这个神圣的文件夹后，就可以拉代码了。
```
<NAME_EMAIL>:infrastructure/vocmng.git vocmng
```
- 本地编译
```
// 进入工程的根目录
cd vocmng

// 编译一把，把依赖的jar都down到本地
mvn clean compile
```
编译好以后就可以把工程导入到自己的IED里面。

### 3.3 本地启动
- 命令行启动
```
// 生成Jar包：在工程的根目录执行
mvn clean package
// 启动工程
java -jar ./biz/target/vocmng.jar
// 通过链接（http://localhost:8080/swagger-ui/index.html）可以看到本工程暴露出来的所有http服务
// 通过链接（https://apollo.xinyongfei.cn/config.html?#/appid=vocmng）可以看到apollo的配置
```
- IDE内启动
```
// 在IDE内运行如下代码就可以了
com.xinfei.vocmng.biz.Application
```

## 4 启动验证
### 4.1 日志说明
日志目录默认在工程根目录的logs文件夹里面，自己可以在application-local.properties改成自己习惯的路径。  
无论IDE启动还是命令行启动，都会在日志目录下面生成5个日志文件。
- 业务处理过程日志  
biz-service.7dt.log, 代码里面显示输出方便问题排查的日志，一般在Servce实现类里面输出请求和结果，无格式要求  
- 默认日志  
common-default.log，没有明确指定日志文件的日志都会输出到改文件，无格式要求
- 错误日志  
common-error.log，所有ERROR级别的日志都会输出到该文件，用于监控和问题排查，无格式要求  
- 查询类业务摘要日志  
query-digest.14dt.log，遵行统一格式，方便SLS解析  
- 交易类业务摘要日志  
trade-digest.14dt.log，遵行统一格式，方便SLS解析  

### 4.2 启动日志
启动日志在common-default.log文件，看到类似的日志，就说明启动成功了。
```
[2023-08-29 12:12:12,077] [main] INFO  com.xinfei.vocmng.biz.Application - Started Application in 2.314 seconds (JVM running for 2.607)
```

## 5 系统服务验证
### 5.1 请求处理验证
通过浏览器，找个接口，触发一下看看系统能不能正常处理请求。
- 找到需要测试的接口  
http://localhost:8080/swagger-ui/index.html#/team-facade-impl/queryByCodeUsingPOST
- 发送请求  
点击Try it Out按钮，填入参数，点Execute，就能看到请求结果了。
- 请求处理日志  
根据请求类型，找到进入摘要日志文件（查询类日志query-digest.14dt.log）。  
看到如下输出，说明请求正常处理。
```
[2023-08-29 12:42:29,506] [http-nio-8082-exec-4] - [(TeamFacadeImpl.queryByCode,Y,EXECUTE_SUCCESS,EXECUTE_SUCCESS,7ms)(string,)(-,-,-))] 
```

### 5.2 集成测试
```
com.xinfei.vocmng.biz.api.TeamFacadeDevTest
```
随便找个集成测试的类，比如上面这个，点右键执行，执行通过说明本地的各项配置正常。  
- 测试模式配置
```
# 注意是在test文件夹，不是mian文件夹
配置文件：vocmng/biz/src/test/resources/application-test.properties
本地模式：test.mode = local
远程模式：test.mode = remote 
```
- 本地模式  
测试类调用本地提供的http服务，可以在IDE里面打断点，可以Debug。  
- 远程模式  
通过测试类验证远程服务器是否配置正确，只有远程模式测试通过后才能交付质量同学进行功能测试。  
远程模式下，测试类调用的是远程服务器提供的Http服务，服务地址可以在下面的配置文件里面修改。  
```
# 注意是在mian文件夹，不是test文件夹
配置文件：vocmng/biz/src/main/resources/application-local.properties 
// 把localhost改成部署了vocmng的远程服务器
远程服务器地址：xf.vocmng.url=http://localhost:8080
```

- 发布
注意main分支代码不要做任何的修改，如果需要修改的话，请先基于main分支创建一个新的分支，注意代码不要合并到main分支。
在新的分支代码编写完成后，在公司的dev上发布。
地址：https://qa1-devops.testxinfei.cn/devk8s/appRelease/releaseDetail?service_config_id=991&env=devk8s&service_name=vocmng&deploy_mode=ecs&project_type=java
点击构建按钮，选择branch，main分支即可进行构建发布。如果是发布自己的分支，选择正确的分支即可。
