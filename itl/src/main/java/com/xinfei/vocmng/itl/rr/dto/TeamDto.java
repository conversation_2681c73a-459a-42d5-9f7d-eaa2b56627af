/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import com.xinfei.xfframework.common.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 团队信息DTO
 *
 * <AUTHOR>
 * @version $ TeamDto, v 0.1 2023/8/28 12:04 <PERSON><PERSON>.Huang Exp $
 */
@Getter
@Setter
public class TeamDto extends BaseDto {

    @ApiModelProperty(value = "团队代码")
    private String teamCode;

    @ApiModelProperty(value = "团队名称")
    private String teamName;
}
