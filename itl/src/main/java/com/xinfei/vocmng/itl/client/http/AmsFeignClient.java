/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import io.kyoto.pillar.ams.rest.dto.falcon.req.AccountAmtLogRequest;
import io.kyoto.pillar.ams.rest.dto.falcon.resp.AccountAmountLogResponse;
import io.kyoto.sole.api.domain.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ AmsFeignClient, v 0.1 2024/8/12 11:28 you.zhang Exp $
 */
@FeignClient(name = FeignConstants.AMS, contextId = FeignConstants.AMS, path = "/")
public interface AmsFeignClient {

    @PostMapping("/ams/account/amount-log")
    Response<List<AccountAmountLogResponse>> accountAmtLog(AccountAmtLogRequest req);

}
