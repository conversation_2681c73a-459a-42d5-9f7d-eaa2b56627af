/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.psenginecore.facade.OuterApiFacade;
import com.xinfei.psenginecore.facade.rr.dto.AdvanceOrderListOuterDTO;
import com.xinfei.psenginecore.facade.rr.dto.CnlBizDataDTO;
import com.xinfei.psenginecore.facade.rr.request.AdvanceOrderListRequest;
import com.xinfei.psenginecore.facade.rr.response.AdvanceOrderListOuterResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.OuterApiFacadeClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ RepayFacadeClientImpl, v 0.1 2023-12-18 14:20 junjie.yan Exp $
 */

@Component
@Slf4j
public class OuterApiFacadeClientImpl implements OuterApiFacadeClient {
    @Resource
    private OuterApiFacade outerApiFacade;

    @Override
    public List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO> queryAdvanceOrderList(String userNo) {
        AdvanceOrderListOuterResponse response = null;
        AdvanceOrderListRequest request = new AdvanceOrderListRequest();
        CnlBizDataDTO cnlBizData = new CnlBizDataDTO();
        cnlBizData.setCnlPdCode("CSC001000070");
        request.setUserNo(Long.parseLong(userNo));
        String msg = "OuterApiFacade.queryAdvanceOrderList:";
        try {
            response = outerApiFacade.queryAdvanceOrderList(request);
            log.info(LogUtil.clientLog(" OuterApiFacade", "queryAdvanceOrderList", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog(" OuterApiFacade", "queryAdvanceOrderList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData().getOrderList();
    }


}