package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @version $ PayTypeEnum, v 0.1 2023/12/25 21:19 qu.lu Exp $
 */
@Getter
public enum PayTypeRenewEnum {
    WECHAT_PAY(1,"支付宝"),
    ALI_PAY(2,"银行卡"),
    CARD_PAY(3,"微信"),
    ;

    PayTypeRenewEnum(Integer payType, String desc){
        this.payType = payType;
        this.desc = desc;
    }

    /** 支付类型 */
    private Integer payType;
    /** 状态描述 */
    private String desc;


    /**
     * 根据支付类型获取支付类型描述
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code){
        if(code == null){
            return null;
        }

        for (PayTypeRenewEnum status : PayTypeRenewEnum.values()){
            if(status.payType.equals(code)){
                return status.getDesc();
            }
        }

        return null;
    }
}
