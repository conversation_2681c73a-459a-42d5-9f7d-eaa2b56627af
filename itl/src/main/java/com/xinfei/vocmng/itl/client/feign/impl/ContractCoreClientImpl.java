/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.common.lang.error.CommonError;
import com.xinfei.common.lang.error.ErrorContext;
import com.xinfei.contractcore.common.service.facade.api.ContractOpsFacade;
import com.xinfei.contractcore.common.service.facade.api.ContractQueryFacade;
import com.xinfei.contractcore.common.service.facade.request.contract.ContractProofApplyRequest;
import com.xinfei.contractcore.common.service.facade.request.query.ContractQueryRequest;
import com.xinfei.contractcore.common.service.facade.response.BaseResponse;
import com.xinfei.contractcore.common.service.facade.response.DataPageQueryResponse;
import com.xinfei.contractcore.common.service.facade.response.DataResponse;
import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.ContractCoreClient;
import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ ContractCoreClientImpl, v 0.1 2024/11/25 17:14 wancheng.qu Exp $
 */
@Slf4j
@Component
public class ContractCoreClientImpl implements ContractCoreClient {
    public static final String PD_CODE = "CSC001000070";
    public static final String EV_CODE = "СТ00200045";

    @Resource
    private ContractQueryFacade contractQueryFacade;
    @Resource
    private ContractOpsFacade contractOpsFacade;


    @Override
    public String applyProof(ContractProofApplyRequest request) {
        String msg = "ContractCoreClient.applyProof:";
        DataResponse<String>  response = null;
        try {
            response = contractOpsFacade.applyProof(request);
            log.info(LogUtil.clientLog("ContractCoreClientImpl", "applyProof", request, response));
            if (response == null ) {
                msg += "response is null";
                throw new Exception(msg);
            }
            if (!response.isSuccess()) {
                String errorMsg = extractErrorMsg(response);
                throw new Exception(errorMsg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ContractCoreClientImpl", "applyProof", request, response, msg), e);
            throw new ClientException(e.getMessage());
        }
        return response.getData();
    }

    @Override
    public List<ContractVO> listQuery(ContractQueryRequest request) {
        String baseMsg = "ContractCoreClient.listQuery";
        DataPageQueryResponse<ContractVO> response = null;
        try {
            initRequest(request);
            response = contractQueryFacade.listQuery(request);
            log.info(LogUtil.clientLog("ContractCoreClientImpl", "listQuery", request, response));
            if (response == null) {
                return Collections.emptyList();
            }
            if (!response.isSuccess()) {
                String errorMsg = extractErrorMsg(response);
                throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, errorMsg, ErrorLevelsEnum.WARN);
            }
            return Optional.ofNullable(response.getData()).orElse(Collections.emptyList());
        } catch (Exception e) {
            String errMsg = response == null ? baseMsg + ": response is null" : baseMsg + ": request error";
            log.error(LogUtil.clientErrorLog("ContractCoreClientImpl", "listQuery", request, response, errMsg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, errMsg, ErrorLevelsEnum.ERROR);
        }
    }

    private void initRequest(ContractQueryRequest request) {
        String requestId = UUID.randomUUID().toString();
        request.setRequestId(requestId);
        request.setBizChannel(FeignConstants.SERVICE_NAME);
        request.setBizTraceContext(new BizTraceContext() {{
            setCnlNo(requestId);
            setCnlPdCode(PD_CODE);
            setCnlEvCode(EV_CODE);
        }});
    }

    private String extractErrorMsg(BaseResponse response) {
        try {
            return Optional.ofNullable(response.getErrorContext())
                    .map(ErrorContext::getErrorStack)
                    .filter(list -> !list.isEmpty())
                    .map(stack -> stack.get(0))
                    .map(CommonError::getErrorMsg)
                    .orElse("fail");
        } catch (Exception e) {
            return "fail";
        }
    }

    @Override
    public String applyProofBeforeQuery(ContractProofApplyRequest req) {
        ContractQueryRequest query = new ContractQueryRequest();
        query.setBizType(req.getBizType());
        query.setBizNo(req.getBizNo());
        query.setContractTypeList(req.getContractTypeList());

        List<ContractVO> contractVOS = listQuery(query);
        if(CollectionUtils.isNotEmpty(contractVOS) && StringUtils.isNotBlank(contractVOS.get(0).getDownloadUrl()) ){
            return "success";
        }
        return applyProof(req);
    }
}