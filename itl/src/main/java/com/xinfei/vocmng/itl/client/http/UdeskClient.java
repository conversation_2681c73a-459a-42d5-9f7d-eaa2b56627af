/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.http;

/**
 * <AUTHOR>
 * @version $ UdeskClient, v 0.1 2024-01-16 16:16 junjie.yan Exp $
 */

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.CalllogsResponse;
import com.xinfei.vocmng.itl.rr.CreateCustomerResponse;
import com.xinfei.vocmng.itl.rr.ExportCustomerResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerFilterListResponse;
import com.xinfei.vocmng.itl.rr.ImCallLogQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsListQueryResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsQueryResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerListResponse;
import com.xinfei.vocmng.itl.rr.MergeCustomerResponse;
import com.xinfei.vocmng.itl.rr.SessionsVoteResponse;
import com.xinfei.vocmng.itl.rr.udesk.UdeskCCBaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.U_DESK, contextId = FeignConstants.U_DESK, path = "/")
public interface UdeskClient {

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/im_session/#_8">获取某一客户的聊天记录列表</a>
     *
     * @param timestamp     时间戳
     * @param sign          对 email&openApi&timestamp&nonce&signVersion sha1加密
     * @param customerPhone 用户手机号
     * @param nonce         随机数
     * @param signVersion   v2
     * @param email         <EMAIL>
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return
     */
    @GetMapping("/open_api_v1/callcenter/calllogs")
    CalllogsResponse customerCallLogs(
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("customer_phone") String customerPhone,
            @RequestParam("nonce") Integer nonce,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("email") String email,
            @RequestParam("start_time") LocalDate startTime,
            @RequestParam("end_time") LocalDate endTime
    );


    /**
     * <a href="https://www.udesk.cn/doc/apiv2/im_session/#_9">获取某一客户的聊天记录列表</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param startTime
     * @param endTime
     * @param page
     * @param type
     * @param content
     * @return
     */
    @GetMapping("/open_api_v1/im/sessions/customer_im_logs")
    CalllogsResponse customerImLogs(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Integer nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("start_time") String startTime,
            @RequestParam("end_time") String endTime,
            @RequestParam("page") Integer page,
            @RequestParam("type") String type,
            @RequestParam("content") String content
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/agents/#_7">获取客服详情</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param type
     * @param content
     * @return
     */
    @GetMapping("/open_api_v1/agents/get_agent")
    CalllogsResponse getAgent(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Integer nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("type") String type,
            @RequestParam("content") String content
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/im_session/#im">获取对话吉利列表</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param page
     * @param pageSize
     * @return
     */
    @GetMapping("/open_api_v1/im/sessions/search")
    ImSessionsListQueryResponse sessionsSync(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("page") Integer page,
            @RequestParam("page_size") Integer pageSize,
            @RequestParam("start_time") String startTime,
            @RequestParam("end_time") String endTime
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/im_session/#im_1/#_7">获取对话记录详情</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param imSubSessionId
     * @return
     */
    @GetMapping("/open_api_v1/im/sessions/im_sub_session")
    ImSessionsDetailsQueryResponse sessionDetailsSync(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("im_sub_session_id") String imSubSessionId
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/callcenter/#_21">获取通话记录</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param page
     * @param perPage
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/open_api_v1/callcenter/calllogs")
    ImCallLogQueryResponse callSync(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("page") Integer page,
            @RequestParam("per_page") Integer perPage,
            @RequestParam("start_time") String startTime,
            @RequestParam("end_time") String endTime
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/customers/#_5">获取客户详情</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param type
     * @param content
     * @return
     */
    @GetMapping("/open_api_v1/customers/get_customer")
    ImCustomerDetailsQueryResponse getCustomerDetails(
            @RequestParam("type") String type,
            @RequestParam("content") String content,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign_version") String signVersion
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/user_groups/">获取客服组列表</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param page
     * @param perPage
     * @return
     */
    @GetMapping("/open_api_v1/user_groups")
    ImUserGroupsQueryResponse userGroups(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("page") Integer page,
            @RequestParam("per_page") Integer perPage
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/agents/#_3">获取客服列表</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param page
     * @param perPage
     * @return
     */
    @GetMapping("/open_api_v1/agents")
    CalllogsResponse agents(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("page") Integer page,
            @RequestParam("per_page") Integer perPage
    );


    /**
     * <a href="https://www.udesk.cn/doc/apiv2/customers/#_5">获取客户列表</a>
     *
     * @param filterId    客户过滤器ID（可选）
     * @param query       客户搜索时的关键字（可选）
     * @param page        页码，从 1 开始，默认 1
     * @param pageSize    每页数量，默认 20，最大 100
     * @param email       请求者邮箱（用于身份验证）
     * @param timestamp   请求时间戳（秒）
     * @param sign        请求签名
     * @param nonce       请求随机数，保证请求唯一性
     * @param signVersion 签名版本（例如 "v2"）
     * @return 返回客户列表响应体
     */
    @GetMapping("/open_api_v1/customers")
    GetCustomerListResponse getCustomerList(
            @RequestParam(value = "filter_id", required = false) Integer filterId,
            @RequestParam(value = "query", required = false) String query,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") String nonce,
            @RequestParam("sign_version") String signVersion
    );

    /**
     * <a href="https://www.udesk.cn/doc/apiv2/callcenter/#_26">获取通话记录详情</a>
     *
     * @param email
     * @param signVersion
     * @param nonce
     * @param sign
     * @param timestamp
     * @param callId
     * @return
     */
    @GetMapping("/open_api_v1/callcenter/call_log")
    ImCallLogQueryResponse callDetailsSync(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("call_id") String callId
    );

    /**
     * 调用 UDesk “客户批量导出”接口（/customers/export）
     * <a href="https://www.udesk.cn/doc/apiv2/customers/#-">客户批量导出</a>
     *
     * @param filterId 客户过滤器 ID，首次调用可选
     * @param query    关键字搜索，可选（与 filterId 互斥）
     * @param scrollId 上一批返回的 scroll_id；首次调用传 null
     * @return 导出结果封装对象，含 customers / scroll_id / total
     */
    @GetMapping("/open_api_v1/customers/export")
    ExportCustomerResponse exportCustomers(
            @RequestParam("filter_id") Long filterId,
            @RequestParam("query") String query,
            @RequestParam("scroll_id") String scrollId,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") String nonce,
            @RequestParam("sign_version") String signVersion);

    /**
     * 查询用户过滤器
     *
     * @return 用户过滤器结构体
     */
    @GetMapping("/open_api_v1/customers/filters")
    GetCustomerFilterListResponse getCustomerFilters(
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") String nonce,
            @RequestParam("sign_version") String signVersion);

    /**
     * 合并用户
     *
     * @return 是否合并成功
     */
    @PostMapping("/open_api_v1/customers/merge")
    MergeCustomerResponse mergeCustomers(
            @RequestBody Map<String, String> body,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") String nonce,
            @RequestParam("sign_version") String signVersion);

    /**
     * 创建用户
     *
     * @return 创建是否成功
     */
    @PostMapping("/open_api_v1/customers")
    CreateCustomerResponse createCustomer(
            @RequestBody Map<String, Object> body,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("nonce") String nonce,
            @RequestParam("sign_version") String signVersion);

    /**
     * 满意度查询
     */
    @GetMapping("/open_api_v1/im/sessions/vote")
    SessionsVoteResponse sessionsVote(
            @RequestParam("email") String email,
            @RequestParam("sign_version") String signVersion,
            @RequestParam("nonce") Long nonce,
            @RequestParam("sign") String sign,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("page") Integer page,
            @RequestParam("page_size") Integer pageSize,
            @RequestParam("start_time") String startTime,
            @RequestParam("end_time") String endTime);
}
