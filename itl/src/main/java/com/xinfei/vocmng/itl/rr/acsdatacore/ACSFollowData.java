package com.xinfei.vocmng.itl.rr.acsdatacore;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ACSFollowData, v 0.1 2025/3/6 15:50 shaohui.chen Exp $
 */
@Data
public class ACSFollowData {

    /**
     * 用户基本信息
     */
    private String userName;               // 客户名称
    private String userNo;                 // 用户编号
    private List<String> registerApp;      // 注册 APP（多选）
    private List<String> accountStatus;    // 账号状态（多选）
    private String gender;                 // 性别
    private List<String> userNoList;       // 所有 userNo 列表
    private List<String> userTagLabelList; // 客户打标（多选）


    /**
     * 小结信息
     */
    private Long briefSummaryId;                             // 小结 ID
    private List<Integer> incomingLineSourceList;               // 进线来源（多选）
    private List<Long> questionClassificationOneLevelList;   // 问题分类(一级)
    private List<Long> questionClassificationTwoLevelList;   // 问题分类(二级)
    private List<Long> questionClassificationThreeLevelList; // 问题分类(三级)
    private List<String> questionClassificationOneLevelStrList;   // 问题分类(一级)
    private List<String> questionClassificationTwoLevelStrList;   // 问题分类(二级)
    private List<String> questionClassificationThreeLevelStrList; // 问题分类(三级)
    private List<String> remark;                                     // 备注（无）
    private Boolean createWorkOrderFlag;                       // 是否创建工单（单选）


    /**
     * 工单相关信息
     */
    private String workOrderListPageUrl;   // 工单列表页面 URL
    private String customerDetailPageUrl;  // 客户详情页面 URL


    /**
     * 坐席信息
     */
    private String seatsName;                  // 坐席名称
    @JsonProperty("uDeskAgentId")
    private Long uDeskAgentId;                     // Udesk坐席 ID
    private String seatsOrganId;              // 坐席组织架构 ID
    private String seatsOrganName;            // 坐席架构名称
    private List<String> seatsGroupList;      // 坐席组别（多选）
    private List<String> userTagUdeskLabelList; // 用户标签（多选）


    /**
     * Udesk 聊天数据
     */
    @JsonProperty("uDeskConversationId")
    private String uDeskConversationId;                     // 对话子ID
    @JsonProperty("uDeskMainConversationId")
    private Long uDeskMainConversationId;                     // 对话主ID
    @JsonProperty("uDeskConversationStartTime")
    private String uDeskConversationStartTime;              // 会话开始时间
    @JsonProperty("uDeskConversationEndTime")
    private String uDeskConversationEndTime;                // 会话结束时间
    @JsonProperty("uDeskLastMsgSender")
    private String uDeskLastMsgSender;                      // 最后消息发送方
    @JsonProperty("uDeskFirstResponseTime")
    private Integer uDeskFirstResponseTime;                 // 首次响应时间（整数）
    @JsonProperty("uDeskConversationEndWayList")
    private List<String> uDeskConversationEndWayList;       // 对话结束方式（多选）
    @JsonProperty("uDeskConversationSatisfactionEvaluationList")
    private List<String> uDeskConversationSatisfactionEvaluationList; // 满意度评价（多选）


    /**
     * Udesk 电话数据
     */
    @JsonProperty("uDeskCallId")
    private String uDeskCallId;                              // Call ID
    @JsonProperty("uDeskRecordStartTime")
    private String uDeskRecordStartTime;                     // 录音开始时间（无）
    @JsonProperty("uDeskRecordEndTime")
    private String uDeskRecordEndTime;                       // 录音结束时间（无）
    @JsonProperty("uDeskCallType")
    private String uDeskCallType;                            // 通话类型（单选）
    @JsonProperty("uDeskCallHungUper")
    private String uDeskCallHungUper;                        // 通话挂断方（单选）
    @JsonProperty("uDeskCallSatisfactionEvaluationList")
    private List<String> uDeskCallSatisfactionEvaluationList; // 满意度评价（多选）
    @JsonProperty("uDeskCallKeepTime")
    private Integer uDeskCallKeepTime;                       // 保持时长（整数，单位：秒）
    @JsonProperty("uDeskCallRingTime")
    private Integer uDeskCallRingTime;                       // 响铃时间（整数，单位：秒）
    @JsonProperty("uDeskCallDuration")
    private Integer uDeskCallDuration;                       // 通话时长（整数，单位：秒）


    /**
     * 催收相关数据
     */
    private Boolean stopUrgingFlag;   // 是否停催（单选）
    private String stopUrgingWay;     // 停催方式（单选）
    private Integer stopUrgingDays;   // 停催天数（整数）


}
