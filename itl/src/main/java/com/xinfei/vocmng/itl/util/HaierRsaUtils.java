package com.xinfei.vocmng.itl.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

/**
 * 海尔消金接口RSA加密工具类
 * 
 * <AUTHOR>
 * @version $ HaierRsaUtils, v 0.1 2025/08/08 HaierRsaUtils Exp $
 */
@Slf4j
public class HaierRsaUtils {
    
    private static final String ALGORITHM = "RSA";
    private static final String TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    /**
     * 使用指定的RSA公钥加密
     *
     * @param plaintext 需要加密的明文
     * @param publicKeyStr 公钥字符串
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plaintext, String publicKeyStr) {
        try {
            // 解码公钥
            byte[] keyBytes = Base64.decodeBase64(publicKeyStr);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 加密
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            log.error("RSA加密失败: ", e);
            throw new RuntimeException("RSA加密失败", e);
        }
    }
}
