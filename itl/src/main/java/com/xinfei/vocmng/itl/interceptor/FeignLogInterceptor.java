//package com.xinfei.vocmng.itl.interceptor;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import feign.Request;
//import feign.Response;
//import feign.Util;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.MDC;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.util.Collection;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * Feign 日志拦截器，用于记录请求和响应的详细信息
// *
// * <AUTHOR>
// * @version $ FeignLogInterceptor, v 0.1 2025/5/10 $
// */
//@Slf4j
//@Component
//public class FeignLogInterceptor {
//
//    private static final int MAX_BODY_LENGTH = 8192; // 限制日志中请求/响应体的最大长度
//
//    /**
//     * 记录请求信息
//     *
//     * @param interfaceName 接口名称
//     * @param methodName 方法名称
//     * @param request 请求对象
//     */
//    public void logRequest(String interfaceName, String methodName, Request request) {
//        try {
//            // 记录请求头
//            Map<String, Object> requestInfo = new HashMap<>();
//            requestInfo.put("url", request.url());
//            requestInfo.put("method", request.httpMethod().name());
//            requestInfo.put("headers", getHeadersMap(request.headers()));
//
//            // 记录请求体
//            if (request.body() != null && request.body().length > 0) {
//                String bodyStr = new String(request.body(), StandardCharsets.UTF_8);
//                if (bodyStr.length() > MAX_BODY_LENGTH) {
//                    bodyStr = bodyStr.substring(0, MAX_BODY_LENGTH) + "... (truncated)";
//                }
//                requestInfo.put("body", bodyStr);
//            }
//
//            // 记录接口和方法信息
//            requestInfo.put("interface", interfaceName);
//            requestInfo.put("operation", methodName);
//
//            // 记录追踪ID
//            String traceId = MDC.get("Vocmng-TraceID");
//            if (StringUtils.isNotBlank(traceId)) {
//                requestInfo.put("traceId", traceId);
//            }
//
//            // 输出日志
//            log.info("Feign Request: {}", JSON.toJSONString(requestInfo));
//        } catch (Exception e) {
//            log.warn("Failed to log Feign request", e);
//        }
//    }
//
//    /**
//     * 记录响应信息
//     *
//     * @param interfaceName 接口名称
//     * @param methodName 方法名称
//     * @param response 响应对象
//     */
//    public Response logResponse(String interfaceName, String methodName, Response response) {
//        try {
//            // 记录响应头
//            Map<String, Object> responseInfo = new HashMap<>();
//            responseInfo.put("status", response.status());
//            responseInfo.put("reason", response.reason());
//            responseInfo.put("headers", getHeadersMap(response.headers()));
//
//            // 记录响应体 - 只有当响应体存在时才处理
//            if (response.body() != null) {
//                // 创建一个副本以避免消费原始响应
//                Response.Body originalBody = response.body();
//
//                // 读取响应体数据
//                byte[] bodyData = Util.toByteArray(originalBody.asInputStream());
//
//                // 记录响应体（可能被截断）
//                String bodyStr = new String(bodyData, StandardCharsets.UTF_8);
//                if (bodyStr.length() > MAX_BODY_LENGTH) {
//                    bodyStr = bodyStr.substring(0, MAX_BODY_LENGTH) + "... (truncated)";
//                }
//                responseInfo.put("body", bodyStr);
//
//                // 创建新的响应对象，包含原始数据
//                response = response.toBuilder()
//                        .body(bodyData)
//                        .build();
//            }
//
//            // 记录接口和方法信息
//            responseInfo.put("interface", interfaceName);
//            responseInfo.put("operation", methodName);
//
//            // 记录追踪ID
//            String traceId = MDC.get("Vocmng-TraceID");
//            if (StringUtils.isNotBlank(traceId)) {
//                responseInfo.put("traceId", traceId);
//            }
//
//            // 输出日志
//            log.info("Feign Response: {}", JSON.toJSONString(responseInfo, SerializerFeature.DisableCircularReferenceDetect));
//        } catch (IOException e) {
//            log.warn("Failed to log Feign response", e);
//        }
//
//        return response;
//    }
//
//    /**
//     * 将请求/响应头转换为Map
//     *
//     * @param headers 请求/响应头
//     * @return 转换后的Map
//     */
//    private Map<String, Object> getHeadersMap(Map<String, Collection<String>> headers) {
//        Map<String, Object> result = new HashMap<>();
//        if (headers != null) {
//            for (Map.Entry<String, Collection<String>> entry : headers.entrySet()) {
//                // 敏感信息过滤
//                if (isSensitiveHeader(entry.getKey())) {
//                    result.put(entry.getKey(), "******");
//                } else {
//                    result.put(entry.getKey(), entry.getValue());
//                }
//            }
//        }
//        return result;
//    }
//
//    /**
//     * 判断是否为敏感头信息
//     *
//     * @param headerName 头信息名称
//     * @return 是否敏感
//     */
//    private boolean isSensitiveHeader(String headerName) {
//        if (StringUtils.isBlank(headerName)) {
//            return false;
//        }
//
//        String lowerCaseHeader = headerName.toLowerCase();
//        return lowerCaseHeader.contains("token") ||
//               lowerCaseHeader.contains("password") ||
//               lowerCaseHeader.contains("secret") ||
//               lowerCaseHeader.contains("authorization");
//    }
//}
