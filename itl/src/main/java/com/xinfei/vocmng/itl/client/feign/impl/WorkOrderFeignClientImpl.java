/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.WorkOrderFeignClient;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.CreateTaskDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClientImpl, v 0.1 2024-01-17 15:10 junjie.yan Exp $
 */
@Slf4j
@Component
public class WorkOrderFeignClientImpl {

    @Resource
    private WorkOrderFeignClient workOrderFeignClient;

    public GetTaskByUserIdResp getTaskByUserNo(Long userNo) {
        WorkOrderResponse<GetTaskByUserIdResp> response = null;
        String msg = "WorkOrderFeignClient.getTaskByUserId:";
        GetTaskByUserIdReq request = new GetTaskByUserIdReq();
        request.setUserId(userNo);
        try {
            response = workOrderFeignClient.getTaskByUserId(request);
            log.info(LogUtil.clientLog("WorkOrderFeignClient", "getTaskByUserId", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || !"请求成功".equals(response.getMessage()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("WorkOrderFeignClient", "getTaskByUserId", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<GetTaskByMobileResp> getTaskByMobile(String mobile) {
        WorkOrderResponse<List<GetTaskByMobileResp>> response = null;
        String msg = "WorkOrderFeignClient.getTaskByMobile:";
        GetTaskByMobileReq request = new GetTaskByMobileReq();
        request.setMobile(mobile);
        try {
            response = workOrderFeignClient.getTaskByMobile(request);
            log.info(LogUtil.clientLog("WorkOrderFeignClient", "getTaskByMobile", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || !"请求成功".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("WorkOrderFeignClient", "getTaskByMobile", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public void createTask(CreateTaskDto request) {
        WorkOrderResponse<Object> response = null;
        String msg = "WorkOrderFeignClient.createTask:";
        try {
            response = workOrderFeignClient.createTask(request);
            log.info(LogUtil.clientLog("WorkOrderFeignClient", "createTask", request, response));
            if (Objects.isNull(response) || "4".equals(response.getCode()) || response.getMessage().contains("创建失败")) {
                    msg += response == null ? "response is null" : response.getMessage();
                    throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("WorkOrderFeignClient", "createTask", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}