/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.AppProdClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.LogOffDto;
import com.xinfei.vocmng.itl.rr.dto.LogOffQueryDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/7/4 14:28
 *
 */

@Slf4j
@Component
public class LogOffService {
    @Resource
    private AppProdClient appProdClient;

    public LogOffDto logOffCheck(LogOffCheckRequest request) {

        LogOffResponse response = null;
        String msg = "AppProdClient.logOffCheck:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = appProdClient.logOffCheck(request);
            log.info(LogUtil.clientLog("AppProdClient", "logOffCheck", request, response));
            if (Objects.isNull(response) || !"success".equals(response.getMessage()) || response.getCode() != 1) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AppProdClient", "logOffCheck", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean logOff(LogOffRequest request) {
        LogOffResponse response = null;
        String msg = "AppProdClient.logOff:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = appProdClient.logOff(request);
            log.info(LogUtil.clientLog("AppProdClient", "logOff", request, response));
            if (Objects.isNull(response) || !"success".equals(response.getMessage()) || response.getCode() != 1) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AppProdClient", "logOff", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean logOffCancel(LogOffCancelRequest request) {
        LogOffResponse response = null;
        String msg = "AppProdClient.logOffCancel:";
        try {
            response = appProdClient.logOffCancel(request);
            log.info(LogUtil.clientLog("AppProdClient", "logOffCancel", request, response));
            if (Objects.isNull(response) || !"success".equals(response.getMessage()) || response.getCode() != 1) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AppProdClient", "logOffCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<LogOffQueryDto> logOffQuery(String app, String mobile) {
        LogOffQueryRequest request = new LogOffQueryRequest();
        List<Integer> statusList = new ArrayList<>();
        //注销状态 0-注销失败 1-已注销 2-待注销 3-无资格 4-取消注销
        statusList.add(1);
        statusList.add(2);
        request.setStatus(statusList);
        request.setMobile(mobile);
        List<String> typeList = new ArrayList<>();
        //操作类型 logoff-注销 batch-批量上传
        typeList.add("logoff");
        typeList.add("batch");
        request.setType(typeList);
        request.setApp(app);
        LogOffQueryResponse<LogOffQueryDataInfo<List<LogOffQueryDto>>> response = null;
        String msg = "AppProdClient.logOffQuery:";
        try {
            response = appProdClient.logOffQuery(request);
            log.info(LogUtil.clientLog("AppProdClient", "logOffQuery", request, response));
            if (Objects.isNull(response) || !"success".equals(response.getMessage()) || response.getCode() != 1 || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                log.warn(msg);
                return null;
            }

            LogOffQueryDataInfo<List<LogOffQueryDto>> pageDataInfo = response.getData();
            if (pageDataInfo == null || CollectionUtils.isEmpty(pageDataInfo.getRecords())) {
                msg += pageDataInfo == null ? "response is null" : response.getMessage();
                log.warn(msg);
                return null;
            }

            return pageDataInfo.getRecords();
        } catch (Exception e) {
            log.warn(LogUtil.clientErrorLog("AppProdClient", "logOffQuery", request, response, msg), e);
            return null;
        }
    }
}