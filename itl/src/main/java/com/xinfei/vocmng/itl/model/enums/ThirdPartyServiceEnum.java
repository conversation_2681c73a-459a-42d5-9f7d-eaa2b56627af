package com.xinfei.vocmng.itl.model.enums;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/17
 */
@Getter
public enum ThirdPartyServiceEnum {
    CIS(FeignConstants.CIS,"CIS用户服务"),
    CREDIT(FeignConstants.CREDIT,"信贷服务"),
    CONTRACT(FeignConstants.CONTRACT,"合同系统"),
    MEMBER_INTEREST(FeignConstants.MEMBER_INTEREST,"会员权益系统"),
    SMS_CENTER(FeignConstants.SMS_CENTER,"短信中心服务"),
    CALL_CENTER(FeignConstants.CALL_CENTER,"呼叫中心服务"),
    WORK_ORDER(FeignConstants.WORK_ORDER,"工单系统"),
    PLATFORM_SERVICE_API(FeignConstants.PLATFORM_SERVICE_API,"API导流服务")
    ;

    ThirdPartyServiceEnum(String appName, String description) {
        this.appName = appName;
        this.description = description;
    }

    /** 三方服务应用名称 */
    private String appName;
    /** 应用描述 */
    private String description;
}
