/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.fundcore.facade.api.FundLoanFacade;
import com.xinfei.fundcore.facade.api.FundProofFacade;
import com.xinfei.fundcore.facade.api.request.*;
import com.xinfei.fundcore.facade.api.response.*;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.FundCoreClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ FundCoreClientImpl, v 0.1 2024/6/26 15:07 wancheng.qu Exp $
 */

@Slf4j
@Component
public class FundCoreClientImpl implements FundCoreClient {

    @Resource
    private FundProofFacade fundProofFacade;

    @Resource
    private FundLoanFacade fundLoanFacade;

    @Override
    public SettleCertApplyResponse settleCertApply(SettleCertApplyRequest request) {
        Response<SettleCertApplyResponse> sr = null;
        String msg = "FundCoreClientImpl.settleCertApply:";
        try {
            sr = fundProofFacade.settleCertApply(request);
            log.info(LogUtil.clientLog("FundCoreClientImpl", "settleCertApply", request, sr));
            if (Objects.isNull(sr) || !sr.isSuccess()) {
                msg += sr == null ? "response is null" : sr.getMsg();
                throw new Exception(msg);
            }
            return sr.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundCoreClientImpl", "settleCertApply", request, sr, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public String settleCertQuery(SettleCertQueryRequest request) {
        Response<SettleCertQueryResponse> sr = null;
        String msg = "FundCoreClientImpl.settleCertQuery:";
        try {
            sr = fundProofFacade.settleCertQuery(request);
            log.info(LogUtil.clientLog("FundCoreClientImpl", "settleCertQuery", request, sr));
            if (Objects.isNull(sr) || !sr.isSuccess() || Objects.isNull(sr.getData()) || CollectionUtils.isEmpty(sr.getData().getOssInfos())) {
                msg += sr == null ? "response is null" : sr.getMsg();
                throw new Exception(msg);
            }
            return sr.getData().getOssInfos().get(0).getDownloadUrl();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundCoreClientImpl", "settleCertQuery", request, sr, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public Boolean logoutQuotaCheck(IsCanLogoutQuotaRequest request) {
        Response<IsCanLogoutQuotaResponse> sr = null;
        String msg = "FundCoreClientImpl.logoutQuotaCheck:";
        try {
            sr = fundLoanFacade.isCanLogoutQuotaQuery(request);
            log.info(LogUtil.clientLog("FundCoreClientImpl", "logoutQuotaCheck", request, sr));
            if (Objects.isNull(sr) || !sr.isSuccess() || Objects.isNull(sr.getData())) {
                log.error(msg += sr == null ? "response is null" : sr.getMsg());
                return false;
            }
            return "1".equals(sr.getData().getStatus());
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundCoreClientImpl", "logoutQuotaCheck", request, sr, msg), e);
            return false;
        }
    }

    @Override
    public Boolean logoutQuota(LogoutQuotaRequest request) {
        Response<LogoutQuotaResponse> sr = null;
        String msg = "FundCoreClientImpl.logoutQuota:";
        try {
            sr = fundLoanFacade.logoutQuotaQuery(request);
            log.info(LogUtil.clientLog("FundCoreClientImpl", "logoutQuota", request, sr));
            if (Objects.isNull(sr) || !sr.isSuccess() || Objects.isNull(sr.getData())) {
                log.error(msg += sr == null ? "response is null" : sr.getMsg());
                return false;
            }
            return "1".equals(sr.getData().getStatus());
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundCoreClientImpl", "logoutQuota", request, sr, msg), e);
            return false;
        }
    }

    @Override
    public FundOrderQueryResponse fundOrderQuery(FundOrderQueryRequest request) {
        Response<FundOrderQueryResponse> sr = null;
        String msg = "FundCoreClientImpl.logoutQuotaCheck:";
        try {
            sr = fundLoanFacade.fundOrderQuery(request);
            log.info(LogUtil.clientLog("FundCoreClientImpl", "fundOrderQuery", request, sr));
            if (Objects.isNull(sr) || !sr.isSuccess() || Objects.isNull(sr.getData())) {
                log.error(msg += sr == null ? "response is null" : sr.getMsg());
                throw new Exception(msg);
            }
            return sr.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundCoreClientImpl", "fundOrderQuery", request, sr, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}