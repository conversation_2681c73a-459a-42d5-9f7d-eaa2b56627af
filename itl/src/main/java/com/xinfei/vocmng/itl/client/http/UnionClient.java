package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@FeignClient(name = FeignConstants.UNION, contextId = FeignConstants.UNION, path = "/")
public interface UnionClient {


    /**
     * https://www.tapd.cn/60211538/prong/stories/view/1160211538001073742
     * @param request
     * @return
     */
    @PostMapping("/market/list")
    SmsResponse<PageResultInfo<List<MarketListResp>>> list(UnionBaseReq<MarketListReq> request);

    /**
     * https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014198
     * @param request
     * @return
     */
    @PostMapping("/market/mobile")
    SmsResponse<String> marketMobile(UnionBaseReq<MarketUpdateReq> request);
}
