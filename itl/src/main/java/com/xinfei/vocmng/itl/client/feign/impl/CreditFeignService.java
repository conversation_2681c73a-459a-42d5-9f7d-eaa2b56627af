/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.CreditFeignClient;
import com.xinfei.vocmng.itl.rr.AccountInfoReq;
import com.xinfei.vocmng.itl.rr.AccountInfoResp;
import com.xinfei.vocmng.itl.rr.AmountInfoHeader;
import com.xinfei.vocmng.itl.rr.dto.AmsAccountInfo;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ CreditFeignService, v 0.1 2024-04-23 16:08 junjie.yan Exp $
 */
@Slf4j
@Component
public class CreditFeignService {

    @Resource
    private CreditFeignClient creditFeignClient;

    public AmsAccountInfo amountInfo(String custNo, String app) {
        if (custNo == null || app == null) {
            return null;
        }

        AmountInfoHeader header = new AmountInfoHeader();
        String uuid = UUID.randomUUID().toString();
        header.setRequester("vocmng");
        header.setBizType("vocmng");
        header.setBizFlowNumber(uuid);
        header.setSeqId(uuid);
        header.setSourceType("other");
        header.setAppName(app);
        header.setInnerApp(app);
        header.setApp(app);

        AccountInfoReq request = new AccountInfoReq();
        request.setCustNo(custNo);
        request.setApp(app);
        AccountInfoResp response = null;

        String msg = "CreditFeignClient.amountInfo:";
        try {
            response = creditFeignClient.amountInfo(request, JsonUtil.toJson(header));
            log.info(LogUtil.clientLog("CreditFeignClient", "amountInfo", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || !"成功".equals(response.getMessage()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMessage();
                if (response != null && "560012".equals(response.getCode())) {
                    log.warn(LogUtil.clientErrorLog("CreditFeignClient", "amountInfo", request, response, msg));
                    return new AmsAccountInfo();
                }
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("ams未开户")) {
                return new AmsAccountInfo();
            }
            log.error(LogUtil.clientErrorLog("CreditFeignClient", "amountInfo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}