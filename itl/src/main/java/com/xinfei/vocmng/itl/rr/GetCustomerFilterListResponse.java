package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * UDesk /customers/filters 接口返回体
 * <AUTHOR>
 * @version $ ExportCustomerResponse, v 0.1 2025/4/29 $
 */
@Data
public class GetCustomerFilterListResponse {

    /** 执行结果码，1000 表示成功 */
    private int code;

    /** 过滤器列表 */
    @JsonProperty("customer_filters")
    private List<CustomerFilter> customerFilters;

    @Data
    public static class CustomerFilter {
        private int id;
        private String name;
        private boolean active;
    }
}
