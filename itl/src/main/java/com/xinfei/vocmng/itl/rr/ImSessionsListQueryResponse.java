/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ImSessionsQueryResponse, v 0.1 2024-12-17 16:28 pengming.liu Exp $
 */
@Data
public class ImSessionsListQueryResponse {
    private Integer status;
    private String message;
    private Integer size;
    private Integer total;
    @JsonProperty("total_pages")
    private Integer totalPages;

    private List<ImSessionsResponse> item;
}