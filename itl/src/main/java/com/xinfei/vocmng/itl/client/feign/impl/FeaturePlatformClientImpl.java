package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.FeaturePlatformClient;
import com.xinfei.vocmng.itl.rr.RealCollectReq;
import com.xinfei.vocmng.itl.rr.RealCollectResp;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Slf4j
@Component
public class FeaturePlatformClientImpl {

    @Value("${apiData}")
    private String apiData;

    @Resource
    private FeaturePlatformClient featurePlatformClient;

    private final static String successCode = "10000";

    private final static String successMsg = "成功";

    public String accountInfo(RealCollectReq request) {
        request.setRequestId(UUID.randomUUID().toString());
        request.setRequestGroup("vocmng");
        RealCollectResp response = null;
        String msg = "FeaturePlatformClient.realCollect:";
        try {
            response = featurePlatformClient.realCollect(apiData, request);
            log.info(LogUtil.clientLog("FeaturePlatformClient", "realCollect", request, response));
            if (Objects.isNull(response) || !successCode.equals(response.getCode()) || !successMsg.equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getVariablesResponse();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FeaturePlatformClient", "realCollect", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}
