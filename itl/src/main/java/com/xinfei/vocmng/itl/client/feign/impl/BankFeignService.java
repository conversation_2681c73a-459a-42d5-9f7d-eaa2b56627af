/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.BankFeignClient;
import com.xinfei.vocmng.itl.rr.BankCardRequest;
import com.xinfei.vocmng.itl.rr.BaseResponse;
import com.xinfei.vocmng.itl.rr.dto.BankCardDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class BankFeignService {
    @Resource
    private BankFeignClient bankFeignClient;

    public List<BankCardDto> queryLoanQuery(String custNo, String app) {
        if (custNo == null || app == null) {
            return null;
        }

        BankCardRequest request = new BankCardRequest();
        request.setCustNo(custNo);
        request.setApp(app);
        BaseResponse<BankCardDto> response = null;
        String msg = "BankFeignClient.queryBankCardList:";
        try {
            response = bankFeignClient.queryBankCardList(custNo, app);
            log.info(LogUtil.clientLog("BankFeignClient", "queryBankCardList", request, response));
            if (Objects.isNull(response) || !response.getSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("BankFeignClient", "queryBankCardList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}