/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ContractBaseDataDto, v 0.1 2024/5/21 16:27 wancheng.qu Exp $
 */
@Data
public class ContractBaseDataDto implements Serializable {

    @ApiModelProperty("app")
    private List<Data> appList;

    @ApiModelProperty("出具单位")
    private List<Data> unitList;

    @lombok.Data
    public static class Data {
        @ApiModelProperty("渠道key")
        private String key;

        @ApiModelProperty("渠道名")
        private String name;

        @ApiModelProperty("渠道号")
        private String fundChannel;

        @ApiModelProperty("结清证明模版")
        private String jqzm;

        @ApiModelProperty("别名")
        private String alias;
    }

}