package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.FeatureQueryReq;
import com.xinfei.vocmng.itl.rr.FeatureQueryResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * @since 2025/02/14
 */
@FeignClient(name = FeignConstants.FEATURE_QUERY, contextId = FeignConstants.FEATURE_QUERY, path = "/")
public interface FeatureQueryClient {

    @PostMapping("/api/feature_query")
    FeatureQueryResp featureQuery(@RequestHeader Map<String, Object> headers, FeatureQueryReq request);
}
