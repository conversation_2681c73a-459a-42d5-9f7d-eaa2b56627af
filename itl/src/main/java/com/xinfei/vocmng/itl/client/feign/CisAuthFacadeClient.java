package com.xinfei.vocmng.itl.client.feign;

import com.xyf.user.auth.dto.response.OcrFaceImagesResponse;

/**
 * <AUTHOR>
 * @version $ CIsFacadeClient, v 0.1 2023/11/11 12:52 valiant.shaw Exp $
 * @Description: <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017474">用户影像信息(标准接口)</a>
 */
public interface CisAuthFacadeClient {

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001017474@toc6">获取Ocr和face图片信息</a>
     *
     * @param userNo 必填
     * @param custNo
     * @param status
     * @return
     */
    OcrFaceImagesResponse queryOcrFaceImages(Long userNo, String custNo, String status);

}
