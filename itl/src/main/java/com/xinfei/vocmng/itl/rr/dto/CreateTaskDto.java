/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CreateTaskDto, v 0.1 2024-05-27 19:40 junjie.yan Exp $
 */
@Data
public class CreateTaskDto {

    @ApiModelProperty("问题大类id(借款问题:391,还款问题392 ...)")
    @JsonProperty("task_type_id")
    private Integer taskTypeId;

    @ApiModelProperty("问题明细id(注册相关:398,额度激活:399 ...)")
    @JsonProperty("question_type_id")
    private Integer questionTypeId;

    @ApiModelProperty("订单号")
    @JsonProperty("order_number")
    private String orderNumber;

    @ApiModelProperty("业务主题")
    @JsonProperty("customize_scene_id")
    private Integer customizeSceneId;

    @ApiModelProperty("工单渠道")
    @JsonProperty("customize_channel")
    private Integer customizeChannel;

    @ApiModelProperty("credit_user表id")
    @JsonProperty("user_id")
    private String userId;

    @ApiModelProperty("回电号码")
    @JsonProperty("call_number")
    private String callNumber;

    @ApiModelProperty("APP版本")
    @JsonProperty("version")
    private String version;

    @ApiModelProperty("手机型号")
    @JsonProperty("device")
    private String device;

    @ApiModelProperty("转账方式 支付宝、银行卡")
    @JsonProperty("entry_method")
    private String entryMethod;

    @ApiModelProperty("是否自定义")
    @JsonProperty("is_customize")
    private Boolean isCustomize;

    @ApiModelProperty("退款金额")
    @JsonProperty("refund_amount")
    private BigDecimal refundAmount;

    @ApiModelProperty("转账账号后四位")
    @JsonProperty("account_last_four")
    private String accountLastFour;

    @ApiModelProperty("收款账户")
    @JsonProperty("account_credited")
    private String accountCredited;

    @ApiModelProperty("紧急度 '1.一般，2，重要，3紧急，4特急'")
    @JsonProperty("emergency_status")
    private Integer emergencyStatus;

    @ApiModelProperty("描述")
    @JsonProperty("comment")
    private String comment;

    @ApiModelProperty("附件 最多10个附件")
    @JsonProperty("task_uploads")
    private List<String> taskUploads;

    @ApiModelProperty("app_id : xyf ,cxh ,xyf01")
    @JsonProperty("app_id")
    private String appId;

    @ApiModelProperty("身份证")
    @JsonProperty("id_card")
    private String idCard;

    @ApiModelProperty("会员订单号")
    @JsonProperty("vip_order_number")
    private String vipOrderNumber;

}