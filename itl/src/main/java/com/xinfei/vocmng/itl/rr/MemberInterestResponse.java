package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * 会员权益接口响应基类
 *
 * <AUTHOR>
 * @version $ MemberInterestResponse, v 0.1 2023/12/22 17:49 qu.lu Exp $
 */
@Data
public class MemberInterestResponse<T> {
    private String code;
    private String message;
    private T data;

    public boolean isSuccess(){
        return FeignConstants.MEMBER_INTEREST_SUCCESS_CODE.equalsIgnoreCase(code);
    }
}
