package com.xinfei.vocmng.itl.interceptor;

import com.fasterxml.jackson.core.type.TypeReference;

import com.xinfei.vocmng.itl.rr.UnifiedBaseResponse;
import com.xinfei.vocmng.util.constant.MonitorConstant;
import com.xinfei.vocmng.util.logger.MonitorLogBuilder;
import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.feign.XFFeignClient;
import feign.Client;
import feign.Request;
import feign.Response;
import feign.Util;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.io.IOException;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ MonitorFeignClient, v 0.1 2025/4/9 17:07 shaohui.chen Exp $
 */
@Slf4j
public class MonitorFeignClient extends XFFeignClient {

    public MonitorFeignClient(Client client) {
        super(client);
    }

    @Override
    public Response execute(Request request, Request.Options options) throws IOException {
        long start = System.currentTimeMillis();
        boolean success = true;
        Response response = null;
        String interfaceName = request.headers()
                .getOrDefault("X-Feign-Interface", Collections.singleton("unknown"))
                .iterator().next();

        Map<String, String> mdcContext = MDC.getCopyOfContextMap();

        try {
            response = super.execute(request, options);

            if (Objects.nonNull(response) && Objects.nonNull(response.body())) {
                String bodyStr = Util.toString(response.body().asReader(Util.UTF_8));

                try {
                    UnifiedBaseResponse<?> unified =
                            JsonUtil.parseJson(bodyStr, new TypeReference<UnifiedBaseResponse<?>>() {
                            });
                    success = unified.isSuccess();
                } catch (Exception e) {
                    log.warn("Failed to parse response as UnifiedBaseResponse: {}", bodyStr, e);
                    success = false;
                }

                // Rebuild response to keep body readable
                response = response.toBuilder()
                        .body(bodyStr, Util.UTF_8)
                        .build();
            } else {
                success = false;
            }
            return response;

        } catch (Exception e) {
            success = false;
            throw e;
        } finally {
            Map<String, String> originalThreadMdc = MDC.getCopyOfContextMap();
            if (mdcContext != null) {
                MDC.setContextMap(mdcContext);
            } else {
                MDC.clear();
            }

            try {
                long duration = System.currentTimeMillis() - start;
                URI uri = URI.create(request.url());

                MonitorLogBuilder.getInstance(MonitorConstant.Instance.FEIGN_REQUEST, duration, success)
                        .builder(MonitorConstant.BuilderKey.FEIGN_NAME, interfaceName)
                        .builder(MonitorConstant.BuilderKey.METHOD, request.httpMethod().name())
                        .builder(MonitorConstant.BuilderKey.URI, uri.getPath())
                        .builder(MonitorConstant.BuilderKey.REMOTE_IP, uri.getHost())
                        .print();
            } finally {
                if (originalThreadMdc != null) {
                    MDC.setContextMap(originalThreadMdc);
                } else {
                    MDC.clear();
                }
            }
        }
    }
}