package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * 会员卡状态枚举
 *
 * <AUTHOR>
 * @version $ CardStatusEnum, v 0.1 2023/12/25 21:19 qu.lu Exp $
 */
@Getter
public enum CardStatusEnum {
    OPEN(1,"已开卡"),
    REFUND(2,"已退卡"),
    INVALID(3,"已失效"),
    ;

    CardStatusEnum(Integer cardStatus, String desc){
        this.cardStatus = cardStatus;
        this.desc = desc;
    }

    /** 会员卡状态 */
    private Integer cardStatus;
    /** 状态描述 */
    private String desc;


    /**
     * 根据会员卡状态获取会员卡状态描述
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code){
        if(code == null){
            return null;
        }

        for (CardStatusEnum status : CardStatusEnum.values()){
            if(status.getCardStatus().equals(code)){
                return status.getDesc();
            }
        }

        return null;
    }
}
