/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ BillDto, v 0.1 2023-12-20 15:55 junjie.yan Exp $
 */
@Data
public class VocBillDto {

    /**
     * 期数
     */
    private String term;

    /**
     * 账单状态
     */
    private String rpyFlag;

    /**
     * 账单号（还款计划编号）
     */
    private String planNo;

    /**
     * 本金
     */
    private String prinAmt;

    /**
     * 应还利息（利息）
     */
    private String intAmt;

    /**
     * 已还利息（实还利息）
     */
    private String actIntAmt;

    /**
     * 罚息
     */
    private String ointAmt;
    /**
     * 担保逾期（贷后逾期管理费）
     */
    private String fee3Amt;
    /**
     * 到期应还（总额）
     */
    private String totalAmt;
    /**
     * 计划担保费（贷后管理费）已还
     */
    private String actFee1Amt;
    /**
     * 计划担保费（贷后管理费）应还
     */
    private String fee1Amt;
    /**
     * 应还担保费（平台服务费）已还
     */
    private String actFee2Amt;
    /**
     * 应还担保费（平台服务费）应还
     */
    private String fee2Amt;
    /**
     * 账单日（到期日）
     */
    private String dateDue;
    /**
     * 容时期限（宽限期）
     */
    private String dateGrace;
    /**
     * 还款时间（结清日期）
     */
    private String dateSettle;
    /**
     * 利率
     */
    private String feeRate;
}