package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.CallCenterBaseResponse;
import com.xinfei.vocmng.itl.rr.CallListRequest;
import com.xinfei.vocmng.itl.rr.CallRecordDetail;
import com.xinfei.vocmng.itl.rr.PageDataInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 呼叫中心相关接口对接
 *
 * <AUTHOR>
 * @version $ CallCenterFeignClient, v 0.1 2023/12/27 19:14 qu.lu Exp $
 */
@FeignClient(name = FeignConstants.CALL_CENTER,contextId = FeignConstants.CALL_CENTER+".FeinClient",path = "/")
public interface CallCenterFeignClient {
    /**
     * 查询话单信息列表
     * 接口文档：https://confluence.joyborrow.com/pages/viewpage.action?pageId=39790062
     *
     * @param request
     * @return
     */
    @PostMapping("/api/v1/cdrData/pageCdrData")
    CallCenterBaseResponse<PageDataInfo<List<CallRecordDetail>>> queryCallRecordList(CallListRequest request);
}
