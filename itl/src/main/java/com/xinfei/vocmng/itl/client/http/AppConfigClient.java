package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.AppConfigRequest;
import com.xinfei.vocmng.itl.rr.AppConfigResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.GATEWAY, contextId = FeignConstants.GATEWAY, path = "/")
public interface AppConfigClient {

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014843">gateway_app_config表数据获取与修改</a>
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001015029">域名 + 验签</a>
     *
     * @param request
     * @return
     */
    @PostMapping("/gateway-admin/appconfig/appconfig/get-all")
    AppConfigResponse getAll(AppConfigRequest request);
}
