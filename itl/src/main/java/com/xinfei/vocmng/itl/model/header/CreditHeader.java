package com.xinfei.vocmng.itl.model.header;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 信贷API公共请求头
 * <AUTHOR>
 * @since 2023/12/17
 */
@Data
public class CreditHeader extends BaseHeader {
    /** APP标识，部分接口非必填 */
    @JsonProperty(value = "App")
    private String app;
    /** 放置的APP标识，部分接口非必填 */
    @JsonProperty(value = "Inner-App")
    private String innerApp;
    /** 用户token，部分接口非必填 */
    @JsonProperty(value = "Token")
    private String token;
    /** 用户ID，部分接口非必填 */
    @JsonProperty(value = "User-Id")
    private String userId;
    /** 来源类型：client 客户端，wap web端，api_ppd 拍拍贷 */
    @JsonProperty(value = "Source-Type")
    private String sourceType;
    /** 系统类型：android 安卓，ios 苹果，other 其他 */
    @JsonProperty(value = "Os")
    private String os;
}
