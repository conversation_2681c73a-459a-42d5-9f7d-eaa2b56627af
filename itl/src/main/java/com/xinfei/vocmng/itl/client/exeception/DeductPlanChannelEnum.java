package com.xinfei.vocmng.itl.client.exeception;/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */

import io.kyoto.pillar.lcs.base.BaseEnum;
import lombok.AllArgsConstructor;

/**
 * 减免放款创建渠道
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
@AllArgsConstructor
public enum DeductPlanChannelEnum implements BaseEnum {

    /**
     * 客服
     */
    PVC("PVC", "客服"),

    /**
     * 催收
     */
    HUTTA("HUTTA", "催收"),
    ;

    private static final DeductPlanChannelEnum[] values;

    static {
        values = DeductPlanChannelEnum.values();
    }

    private final String code;
    private final String desc;

    public static DeductPlanChannelEnum getByCode(String code) {
        for (DeductPlanChannelEnum deductChannelEnum : values) {
            if (deductChannelEnum.code.equalsIgnoreCase(code)) {
                return deductChannelEnum;
            }
        }
        return null;
    }

    @Override
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
