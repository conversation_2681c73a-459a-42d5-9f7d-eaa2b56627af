package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.RealCollectReq;
import com.xinfei.vocmng.itl.rr.RealCollectResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.FEATURE_PLATFORM, contextId = FeignConstants.FEATURE_PLATFORM, path = "/")
public interface FeaturePlatformClient {

    @PostMapping("{apiData}data/realCollect")
    RealCollectResp realCollect(@PathVariable("apiData") String apiData, RealCollectReq request);
}
