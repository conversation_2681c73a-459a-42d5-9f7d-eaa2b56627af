/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ FundOrderDto, v 0.1 2023-12-27 20:32 junjie.yan Exp $
 */
@Data
public class FundOrderDto {
    @JsonProperty("order_number")
    private String orderNumber;
    @JsonProperty("loan_no")
    private String loanNo;
    @JsonProperty("out_order_number")
    private String outOrderNumber;
    @JsonProperty("fund_source")
    private String fundSource;
    @JsonProperty("failed_reason")
    private String failedReason;
    @JsonProperty("loan_time")
    private String loanTime;
    @JsonProperty("remit_status")
    private String remitStatus;

}