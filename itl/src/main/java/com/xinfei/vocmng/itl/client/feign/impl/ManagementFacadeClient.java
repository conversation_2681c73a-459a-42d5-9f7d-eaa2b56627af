/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.cashiercore.common.service.facade.api.ManagementFacade;
import com.xinfei.cashiercore.common.service.facade.request.management.ShortLinkCloseRequest;
import com.xinfei.cashiercore.common.service.facade.request.management.ShortLinkCreateRequest;
import com.xinfei.cashiercore.common.service.facade.request.management.WhitelistMarkRequest;
import com.xinfei.cashiercore.common.service.facade.response.DataResponse;
import com.xinfei.cashiercore.common.service.facade.vo.BaseVO;
import com.xinfei.cashiercore.common.service.facade.vo.management.ShortLinkVO;
import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ RepayFacadeClientImpl, v 0.1 2023-12-18 14:20 junjie.yan Exp $
 */

@Component
@Slf4j
public class ManagementFacadeClient {
    @Resource
    private ManagementFacade managementFacade;

    private static final String CNL_PD_CODE = "CSC004000005";

    private static final String CNL_EV_CODE_CREATE = "PY00300003";

    private static final String CNL_EV_CODE_CLOSE = "PY00400004";

    private static final String BIZ_CHANNEL = "VOCMNG";

    private static final String BIZ_TYPE = "REPAYTRADE";


    public ShortLinkVO linkCreate(ShortLinkCreateRequest request) {
        DataResponse<ShortLinkVO> response = null;
        request.setBizTraceContext(createBizTraceContext());
        request.setBizChannel(BIZ_CHANNEL);
        request.setBizType(BIZ_TYPE);

        String msg = "ManagementFacade.linkCreate:";
        try {
            response = managementFacade.linkCreate(request);
            log.info(LogUtil.clientLog("ManagementFacade", "linkCreate", request, response));
            if (response == null || !response.isSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManagementFacade", "linkCreate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    private BizTraceContext createBizTraceContext() {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(CNL_PD_CODE);
        bizTraceContext.setCnlNo(UUID.randomUUID().toString());
        return bizTraceContext;
    }

    public BaseVO linkClose(ShortLinkCloseRequest request) {
        DataResponse<BaseVO> response = null;
        request.setBizTraceContext(createBizTraceContext());
        request.setBizChannel(BIZ_CHANNEL);

        String msg = "ManagementFacade.linkClose:";
        try {
            response = managementFacade.linkClose(request);
            log.info(LogUtil.clientLog("ManagementFacade", "linkClose", request, response));
            if (response == null || !response.isSuccess()) {
                msg += response == null ? "response is null" : response.getErrorContext();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManagementFacade", "linkClose", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public BaseVO whitelistMark(WhitelistMarkRequest request) {
        DataResponse<BaseVO> response = null;
        BizTraceContext bizTraceContext = createBizTraceContext();
        bizTraceContext.setCnlPdCode("CSC005000082");
        bizTraceContext.setCnlEvCode("PY00100001");
        request.setBizTraceContext(bizTraceContext);
        request.setBizChannel(BIZ_CHANNEL);
        request.setRequestId(UUID.randomUUID().toString());

        String msg = "ManagementFacade.whitelistMark:";
        try {
            response = managementFacade.whitelistMark(request);
            log.info(LogUtil.clientLog("ManagementFacade", "whitelistMark", request, response));
            if (response == null || !response.isSuccess()) {
                msg += response == null ? "response is null" : response.getErrorContext();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManagementFacade", "whitelistMark", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

}