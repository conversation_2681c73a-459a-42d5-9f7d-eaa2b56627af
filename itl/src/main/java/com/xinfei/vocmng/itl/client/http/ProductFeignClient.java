package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.LoanBaseResponse;
import com.xinfei.vocmng.itl.rr.ProfitProductRequest;
import com.xinfei.vocmng.itl.rr.ProfitProductResponse;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ UdeskClient, v 0.1 2024-04-12 17:25 junjie.yan Exp $
 */
@FeignClient(name = FeignConstants.PRODUCT, contextId = FeignConstants.PRODUCT, path = "/")
public interface ProductFeignClient {
    /**
     * <a href="https://dev-fin-config.devxinfei.cn/doc.html#/default/%E5%9F%BA%E7%A1%80%E6%9E%9A%E4%B8%BE%E7%9B%B8%E5%85%B3%E5%86%85%E9%83%A8%E8%B0%83%E7%94%A8%E6%8E%A5%E5%8F%A3/baseUsingGET"></a>
     * 服务类型
     *
     * @param module 必填
     * @param type   必填
     * @return
     */
    @GetMapping("/facade/base/info")
    LoanBaseResponse<FinServiceType> baseInfo(@RequestParam("module") String module, @RequestParam("type") String type);

    /**
     * <a href="https://dev-fin-config.devxinfei.cn/doc.html#/default/%E5%AD%90%E4%BA%A7%E5%93%81%E9%85%8D%E7%BD%AE%E7%9B%B8%E5%85%B3%E5%86%85%E9%83%A8%E8%B0%83%E7%94%A8%E6%8E%A5%E5%8F%A3/loadProfitProductByConditionUsingPOST">...</a>
     * @param request
     * @return
     */
    @PostMapping("/facade/config/loadProfitProductByCondition")
    LoanBaseResponse<List<ProfitProductResponse>> loadProfitProductByCondition(ProfitProductRequest request);
}
