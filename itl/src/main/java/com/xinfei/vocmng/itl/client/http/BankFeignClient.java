package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.BaseResponse;
import com.xinfei.vocmng.itl.rr.dto.BankCardDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.BANK, contextId = FeignConstants.BANK, path = "/")
public interface BankFeignClient {
    /**
     * <a href="https://www.tapd.cn/********/markdown_wikis/show/#11********001017610">返回银行卡列表 （多）</a>
     * 获取银行卡信息
     * @param custNo 必填
     * @param app 必填
     * @return
     */
    @GetMapping("/multi/queryBankCardList")
    BaseResponse<BankCardDto> queryBankCardList(@RequestParam("custNo") String custNo, @RequestParam("app") String app);
}
