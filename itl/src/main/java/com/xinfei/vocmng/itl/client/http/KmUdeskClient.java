/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.http;

/**
 * <AUTHOR>
 * @version $ KmUdeskClient, v 0.1 2024-12-19 16:16 pengming.liu Exp $
 */

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.RobotSessionsDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsQueryResponse;
import com.xinfei.vocmng.itl.rr.RobotSessionsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
@FeignClient(name = FeignConstants.KM_DESK, contextId = FeignConstants.KM_DESK, path = "/")
public interface KmUdeskClient {

    /**
     * <a href="https://www.udesk.cn/doc/robot/sessions/#_1">获取机器人对话列表记录</a>
     *
     * @param email
     * @param sign
     * @param timestamp
     * @param robotSessionsRequest
     * @return
     */
    @PostMapping("/api/v1/sessions")
    RobotSessionsQueryResponse robotSync(
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,RobotSessionsRequest robotSessionsRequest
    );

    /**
     * <a href="https://www.udesk.cn/doc/robot/sessions/#_8">获取机器人对话列表详情</a>
     *
     * @param email
     * @param sign
     * @param timestamp
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/api/v1/sessions/{id}/logs")
    RobotSessionsDetailsQueryResponse robotDetailsSync(
            @PathVariable("id") Long sessionId,
            @RequestParam("email") String email,
            @RequestParam("timestamp") Long timestamp,
            @RequestParam("sign") String sign,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize
    );
}
