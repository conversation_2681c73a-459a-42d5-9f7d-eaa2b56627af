/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.EngineFeignClient;
import com.xinfei.vocmng.itl.rr.DecisionRequest;
import com.xinfei.vocmng.itl.rr.DecisionResponse;
import com.xinfei.vocmng.itl.rr.EngineHeader;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class EngineFeignService {
    @Resource
    private EngineFeignClient engineFeignClient;

    @Value("${organId}")
    private Integer organId;

    public static String getTime() {
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return Long.toString(now);
    }

    public Map<String, String> decision(String engineCode, Map<String, Object> fields, String scene) {
        if (StringUtils.isEmpty(engineCode) || MapUtils.isEmpty(fields)) {
            return null;
        }

        EngineHeader header = new EngineHeader();
        header.setRequestId("vocmng_decision" + getTime());
        header.setTpCode("vocmng");
        header.setScene(scene);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
//        headers.put("Authorization", JsonUtil.toJson(header));
        headers.put("tpCode", "vocmng");
        headers.put("requestId", "vocmng_decision" + getTime());
//        headers.put("scene", "减免");

        DecisionRequest request = new DecisionRequest();
        request.setEngineCode(engineCode);
        request.setFields(fields);
        request.setOrganId(organId);
        DecisionResponse response = null;
        String msg = "EngineFeignClient.decision:";
        try {
            response = engineFeignClient.decision(request, headers);
            log.info(LogUtil.clientLog("EngineFeignClient", "decision", request, response));
            if (Objects.isNull(response) || !"SUCCESS".equals(response.getState())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getOut();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("EngineFeignClient", "decision", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}