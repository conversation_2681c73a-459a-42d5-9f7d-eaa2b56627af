/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.supervip.common.enums.VipTypeEnum;
import com.xinfei.supervip.common.model.SvcBaseResponse;
import com.xinfei.supervip.interfaces.facade.admin.VipAdminOpsFacade;
import com.xinfei.supervip.interfaces.facade.admin.VipAdminQueryFacade;
import com.xinfei.supervip.interfaces.model.admin.dto.*;
import com.xinfei.supervip.interfaces.model.admin.request.*;
import com.xinfei.vipcore.facade.VipAdminFacade;
import com.xinfei.vipcore.facade.VipInfoFacade;
import com.xinfei.vipcore.facade.rr.dto.*;
import com.xinfei.vipcore.facade.rr.request.*;
import com.xinfei.vipcore.facade.rr.response.*;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.model.enums.MemberTypeEnum;
import com.xinfei.vocmng.itl.util.CisCommonAttributes;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class VipFacadeClientImpl {

    public static final String LOG_TRACE_ID = "Vocmng-TraceID";

    @Resource
    private VipInfoFacade vipInfoFacade;

    @Resource
    private VipAdminFacade vipAdminFacade;

    @Resource
    private VipAdminQueryFacade adminVipFacade;

    @Resource
    private VipAdminOpsFacade vipAdminOpsFacade;

    public List<RenewLogAdminDto> renewLogAdmin(List<Long> userNo, String orderNo, Integer status) {
        RenewLogAdminResponse response = null;
        RenewAdminRequest request = new RenewAdminRequest();
        request.setStatus(status);
        if (userNo != null && !userNo.isEmpty()) {
            request.setUserNos(userNo);
        }
        if (StringUtils.isNotBlank(orderNo)) {
            request.setOrderNo(orderNo);
        }
        String msg = "VipInfoFacade.renewLogAdmin:";
        try {
            response = vipInfoFacade.renewLogAdmin(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "renewLogAdmin", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "renewLogAdmin", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员列表
     *
     * @param userNo
     * @param orderNo
     * @param status
     * @return
     */
    public List<VipOrderDetailAdminDTO> queryVipOrderList(List<Long> userNo, String orderNo, String status, Long id) {
        SvcBaseResponse<List<VipOrderDetailAdminDTO>> response = null;
        QueryVipOrderListAdminRequest request = new QueryVipOrderListAdminRequest();
        request.setOrderStatus(status);
        if (CollectionUtils.isNotEmpty(userNo)) {
            request.setUserNoList(userNo.stream()
                    .map(Object::toString) // 或 String::valueOf
                    .collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(orderNo)) {
            request.setVipOrderNo(orderNo);
        }
        request.setVipOrderId(id);
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "AdminVipFacade.queryVipOrderList:";
        try {
            response = adminVipFacade.queryVipOrderList(request);
            log.info(LogUtil.clientLog("AdminVipFacade", "queryVipOrderList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AdminVipFacade", "queryVipOrderList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞享会员停止续费
     */
    public Boolean renewStopAdmin(String userNo) {
        RenewResponse response = null;
        RenewRequest request = new RenewRequest();
        request.setUserNo(Long.parseLong(userNo));
        String msg = "VipInfoFacade.renewStopAdmin:";
        try {
            response = vipInfoFacade.renewStopAdmin(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "renewStopAdmin", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "renewStopAdmin", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员停止续费
     */
    public Boolean disableVipRenew(String userNo, String operator) {
        SvcBaseResponse<VipRenewDisableResultAdminDTO> response = null;
        DisableVipRenewAdminRequest request = new DisableVipRenewAdminRequest();
        request.setUserNo(userNo);
        request.setOperator(operator);
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "adminVipFacade.disableVipRenew:";
        try {
            response = vipAdminOpsFacade.disableVipRenew(request);
            log.info(LogUtil.clientLog("adminVipFacade", "disableVipRenew", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData().getSuccess();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "disableVipRenew", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞享会员停止扣款
     */
    public Boolean stopWithhold(Long vipOrderId, String operator) {
        StopWithholdResponse response = null;
        StopWithholdRequest request = new StopWithholdRequest();
        request.setVipOrderId(vipOrderId);
        request.setOperator(operator);
        String msg = "VipInfoFacade.stopWithhold:";
        try {
            response = vipInfoFacade.stopWithhold(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "stopWithhold", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "stopWithhold", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员停止扣款
     */
    public Boolean cancelVipDeduct(Long vipOrderId, String operator) {
        SvcBaseResponse<VipDeductCancelResultAdminDTO> response = null;
        CancelVipDeductAdminRequest request = new CancelVipDeductAdminRequest();
        request.setVipOrderId(vipOrderId);
        request.setOperator(operator);
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "vipAdminOpsFacade.cancelVipDeduct:";
        try {
            response = vipAdminOpsFacade.cancelVipDeduct(request);
            log.info(LogUtil.clientLog("adminVipFacade", "cancelVipDeduct", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData().getSuccess();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "cancelVipDeduct", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞享会员权益查询
     */
    public List<ReceiveLogAdminDto> receiveLogAdmin(Integer vipOrderId, Integer vipCardType) {
        ReceiveLogAdminResponse response = null;
        ReceiveLogAdminRequest request = new ReceiveLogAdminRequest();
        request.setVipOrderId(vipOrderId);
        request.setVipCardType(vipCardType);
        String msg = "VipInfoFacade.receiveLogAdmin:";
        try {
            response = vipInfoFacade.receiveLogAdmin(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "receiveLogAdmin", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "receiveLogAdmin", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员权益查询
     */
    public List<RightsReceiveLogAdminDTO> queryVipRightsReceiveLogList(Integer vipOrderId) {
        SvcBaseResponse<List<RightsReceiveLogAdminDTO>> response = null;
        QueryVipRightsReceiveLogListAdminRequest request = new QueryVipRightsReceiveLogListAdminRequest();
        request.setVipOrderId(vipOrderId.longValue());
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "adminVipFacade.queryVipRightsReceiveLogList:";
        try {
            response = adminVipFacade.queryVipRightsReceiveLogList(request);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipRightsReceiveLogList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipRightsReceiveLogList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public UserVipStatusDto userVipStatus(Long userNo) {
        UserVipStatusResponse response = null;
        UserVipStatusRequest request = new UserVipStatusRequest();
        request.setUserNo(userNo);
        String msg = "VipInfoFacade.userVipStatus:";
        try {
            response = vipInfoFacade.userVipStatus(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "userVipStatus", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "userVipStatus", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 判断是否飞跃会员
     *
     * @param userNo
     * @return
     */
    public VipUserStatusAdminDTO userSuperVipStatus(Long userNo) {
        SvcBaseResponse<VipUserStatusAdminDTO> queryVipUserStatusResponse = null;
        QueryVipUserStatusAdminRequest queryVipUserStatusRequest = new QueryVipUserStatusAdminRequest();
        queryVipUserStatusRequest.setUserNo(String.valueOf(userNo));
        queryVipUserStatusRequest.setVipType(MemberTypeEnum.FEI_YUE.getCode());
        String msg = "VipInfoFacade.userSuperVipStatus:";
        try {
            queryVipUserStatusResponse = adminVipFacade.queryVipUserStatus(queryVipUserStatusRequest);

            log.info(LogUtil.clientLog("VipInfoFacade", "userSuperVipStatus", queryVipUserStatusRequest, queryVipUserStatusResponse));
            if (queryVipUserStatusResponse == null || !queryVipUserStatusResponse.isSuc()) {
                msg += queryVipUserStatusResponse == null ? "response is null" : queryVipUserStatusResponse.getMessage();
                throw new Exception(msg);
            }
            return queryVipUserStatusResponse.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "userSuperVipStatus", queryVipUserStatusRequest, queryVipUserStatusResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<VipOrderRefundLogDto> refundList(Long vipOrderId) {
        RefundListResponse response = null;
        RefundListRequest request = new RefundListRequest();
        request.setVipOrderId(vipOrderId);
        String msg = "VipInfoFacade.refundList:";
        try {
            response = vipInfoFacade.refundList(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "refundList", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "refundList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 会员卡申请退款
     */
    public RefundApplyResDto vipCardRefundApply(VipRefundApplyRequest request) {
        VipRefundApplyResponse response = null;
        String msg = "vipAdminFacade.vipCardRefundApply:";
        try {
            response = vipAdminFacade.refundApply(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipCardRefundApply", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipCardRefundApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员卡申请退款
     */
    public RefundApplyResultAdminDTO createVipRefundApply(CreateVipRefundApplyAdminRequest request) {
        SvcBaseResponse<RefundApplyResultAdminDTO> response = null;
        String msg = "AdminVipFacade.createVipRefundApply:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = vipAdminOpsFacade.createVipRefundApply(request);
            log.info(LogUtil.clientLog("AdminVipFacade", "createVipRefundApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AdminVipFacade", "createVipRefundApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞享会员卡退款申请列表
     */
    public RefundApplyListDto vipCardRefundApplyList(Long vipCardId, Integer cardType) {
        VipRefundApplyListResponse response = null;
        VipRefundApplyListRequest request = new VipRefundApplyListRequest();
        request.setVipCardId(vipCardId);
        request.setCardType(cardType);
        String msg = "vipAdminFacade.vipCardRefundApplyList:";
        try {
            response = vipAdminFacade.getApplyList(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipCardRefundApplyList", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipCardRefundApplyList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员卡退款申请列表
     */
    public RefundApplyListAdminDTO queryVipRefundApplyList(Long vipOrderId) {
        SvcBaseResponse<RefundApplyListAdminDTO> response = null;
        QueryVipRefundApplyListAdminRequest request = new QueryVipRefundApplyListAdminRequest();
        request.setVipOrderId(vipOrderId);
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "adminVipFacade.queryVipRefundApplyList:";
        try {
            response = adminVipFacade.queryVipRefundApplyList(request);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipRefundApplyList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipRefundApplyList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 会员卡立即退款
     */
    public RefundStartDto vipCardRefundStart(VipRefundApplyStartRequest vipRefundApplyStartRequest) {
        VipRefundStartResponse response = null;
        String msg = "vipAdminFacade.cardRefundList:";
        try {
            response = vipAdminFacade.refundStart(vipRefundApplyStartRequest);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipCardRefundStart", vipRefundApplyStartRequest, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipCardRefundStart", vipRefundApplyStartRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员立即退款
     *
     * @param vipRefundApplyStartRequest
     * @return
     */
    public RefundApplyExecuteResultAdminDTO executeVipRefundApply(ExecuteVipRefundApplyAdminRequest vipRefundApplyStartRequest) {
        SvcBaseResponse<RefundApplyExecuteResultAdminDTO> response = null;
        String msg = "AdminVipFacade.executeVipRefundApply:";
        CisCommonAttributes.setVipCardCommonAttributes(vipRefundApplyStartRequest);
        try {
            response = vipAdminOpsFacade.executeVipRefundApply(vipRefundApplyStartRequest);
            log.info(LogUtil.clientLog("AdminVipFacade", "executeVipRefundApply", vipRefundApplyStartRequest, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AdminVipFacade", "executeVipRefundApply", vipRefundApplyStartRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 会员卡撤销退款
     */
    public RefundStartDto vipCardRefundCancel(VipRefundApplyStartRequest vipRefundApplyStartRequest) {
        VipRefundStartResponse response = null;
        String msg = "vipAdminFacade.vipCardRefundCancel:";
        try {
            response = vipAdminFacade.refundCancel(vipRefundApplyStartRequest);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipCardRefundCancel", vipRefundApplyStartRequest, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipCardRefundCancel", vipRefundApplyStartRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员撤销退款
     *
     * @param vipRefundApplyStartRequest
     * @return
     */
    public RefundApplyCancelResultAdminDTO cancelVipRefundApply(CancelVipRefundApplyAdminRequest vipRefundApplyStartRequest) {
        SvcBaseResponse<RefundApplyCancelResultAdminDTO> response = null;
        String msg = "AdminVipFacade.cancelVipRefundApply:";
        CisCommonAttributes.setVipCardCommonAttributes(vipRefundApplyStartRequest);
        try {
            response = vipAdminOpsFacade.cancelVipRefundApply(vipRefundApplyStartRequest);
            log.info(LogUtil.clientLog("AdminVipFacade", "cancelVipRefundApply", vipRefundApplyStartRequest, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AdminVipFacade", "cancelVipRefundApply", vipRefundApplyStartRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞享会员卡退款记录列表
     */
    public RefundListDto vipCardRefundList(Long vipCardId, Integer cardType) {
        VipRefundListResponse response = null;
        VipRefundApplyListRequest request = new VipRefundApplyListRequest();
        request.setVipCardId(vipCardId);
        request.setCardType(cardType);
        String msg = "vipAdminFacade.vipCardRefundList:";
        try {
            response = vipAdminFacade.refundList(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipCardRefundList", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipCardRefundList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃会员卡退款记录列表
     */
    public RefundListAdminDTO queryVipRefundList(Long vipOrderId) {
        SvcBaseResponse<RefundListAdminDTO> response = null;
        QueryVipRefundListAdminRequest request = new QueryVipRefundListAdminRequest();
        request.setVipOrderId(vipOrderId);
        String msg = "adminVipFacade.queryVipRefundList:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = adminVipFacade.queryVipRefundList(request);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipRefundList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipRefundList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 会员卡支付订单获取
     */
    public PayAccountDTO orderPayAccount(OrderPayAccountRequest orderPayAccountRequest) {
        OrderPayAccountResponse response = null;
        String msg = "vipAdminFacade.orderPayAccount:";
        try {
            response = vipAdminFacade.orderPayAccount(orderPayAccountRequest);
            log.info(LogUtil.clientLog("vipAdminFacade", "orderPayAccount", orderPayAccountRequest, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage()) || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "orderPayAccount", orderPayAccountRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 获取原始退款账号
     *
     * @param orderPayAccountRequest
     * @return
     */
    public OriginalRefundAccountAdminDTO queryOriginalRefundAccount(QueryOriginalRefundAccountAdminRequest orderPayAccountRequest) {
        SvcBaseResponse<OriginalRefundAccountAdminDTO> response = null;
        String msg = "AdminVipFacade.queryOriginalRefundAccount:";
        CisCommonAttributes.setVipCardCommonAttributes(orderPayAccountRequest);
        try {
            response = adminVipFacade.queryOriginalRefundAccount(orderPayAccountRequest);
            log.info(LogUtil.clientLog("AdminVipFacade", "queryOriginalRefundAccount", orderPayAccountRequest, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AdminVipFacade", "queryOriginalRefundAccount", orderPayAccountRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    /**
     * 会员卡支付订单获取
     */
    public List<VipOrderPayLogAdminDTO> queryVipOrderPayLogList(Long orderId) {
        if (Objects.isNull(orderId)) {
            return Collections.emptyList();
        }
        SvcBaseResponse<List<VipOrderPayLogAdminDTO>> queryVipOrderPayLogListResponse = null;
        QueryVipOrderPayLogListAdminRequest queryVipOrderPayLogListRequest = null;
        String msg = "adminVipFacade.queryVipOrderPayLogList:";
        try {
            // todo BizTraceContext
            queryVipOrderPayLogListRequest = new QueryVipOrderPayLogListAdminRequest();
            queryVipOrderPayLogListRequest.setVipOrderId(orderId);
            queryVipOrderPayLogListRequest.setBizChannel(FeignConstants.SERVICE_NAME);
            queryVipOrderPayLogListRequest.setRequestId(StringUtils.isNotBlank(MDC.get(LOG_TRACE_ID)) ?
                    MDC.get(LOG_TRACE_ID) : UUID.randomUUID().toString().replaceAll("-", ""));
            queryVipOrderPayLogListRequest.setBizTraceContext(new BizTraceContext());
            queryVipOrderPayLogListResponse = adminVipFacade.queryVipOrderPayLogList(queryVipOrderPayLogListRequest);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipOrderPayLogList", queryVipOrderPayLogListRequest, queryVipOrderPayLogListResponse));
            if (queryVipOrderPayLogListResponse == null || !queryVipOrderPayLogListResponse.isSuc() || queryVipOrderPayLogListResponse.getData() == null) {
                msg += queryVipOrderPayLogListResponse == null ? "response is null" : queryVipOrderPayLogListResponse.getMessage();
                throw new Exception(msg);
            }
            return queryVipOrderPayLogListResponse.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipOrderPayLogList", queryVipOrderPayLogListRequest, queryVipOrderPayLogListResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


    public VipOpsResultAdminDTO createVipReduceApply(Long vipOrderId, String operator, BigDecimal reduceAmount) {
        SvcBaseResponse<VipOpsResultAdminDTO> response = null;
        CreateVipReduceApplyAdminRequest request = new CreateVipReduceApplyAdminRequest();
        request.setVipOrderId(vipOrderId);
        request.setOperator(operator);
        request.setReduceAmount(reduceAmount.intValue());
        request.setVipType(VipTypeEnum.FEI_YUE.getCode());
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "vipAdminOpsFacade.createVipReduceApply:";
        try {
            response = vipAdminOpsFacade.createVipReduceApply(request);
            log.info(LogUtil.clientLog("vipAdminOpsFacade", "createVipReduceApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminOpsFacade", "createVipReduceApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public VipOpsResultAdminDTO cancelVipReduceApply(Long vipReduceId, String operator) {
        SvcBaseResponse<VipOpsResultAdminDTO> response = null;
        CancelVipReduceApplyAdminRequest request = new CancelVipReduceApplyAdminRequest();
        request.setVipReduceId(vipReduceId);
        request.setOperator(operator);
        request.setVipType(VipTypeEnum.FEI_YUE.getCode());
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "vipAdminOpsFacade.cancelVipReduceApply:";
        try {
            response = vipAdminOpsFacade.cancelVipReduceApply(request);
            log.info(LogUtil.clientLog("vipAdminOpsFacade", "cancelVipReduceApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminOpsFacade", "cancelVipReduceApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public VipOrderReduceDetailAdminDTO queryVipReduceDetail(Long vipReduceId) {
        SvcBaseResponse<VipOrderReduceDetailAdminDTO> response = null;
        QueryVipReduceDetailAdminRequest request = new QueryVipReduceDetailAdminRequest();
        request.setVipReduceId(vipReduceId);
        request.setVipType(VipTypeEnum.FEI_YUE.getCode());
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "adminVipFacade.queryVipReduceDetail:";
        try {
            response = adminVipFacade.queryVipReduceDetail(request);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipReduceDetail", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipReduceDetail", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public PageAdminDTO<VipOrderReducePriceApplyAdminDTO> queryVipReduceList(QueryVipReduceListAdminRequest request) {
        SvcBaseResponse<PageAdminDTO<VipOrderReducePriceApplyAdminDTO>> response = null;
        request.setVipType(VipTypeEnum.FEI_YUE.getCode());
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "adminVipFacade.queryVipReduceList:";
        try {
            response = adminVipFacade.queryVipReduceList(request);
            log.info(LogUtil.clientLog("adminVipFacade", "queryVipReduceList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("adminVipFacade", "queryVipReduceList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}