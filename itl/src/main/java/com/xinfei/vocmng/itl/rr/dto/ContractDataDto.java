/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ContractDataDto, v 0.1 2024/8/23 15:26 wancheng.qu Exp $
 */
@Data
@Builder
public class ContractDataDto implements Serializable {

    private String id;

    @ApiModelProperty("合同标题")
    private String title;
    @ApiModelProperty("url")
    private String viewUrl;
    @ApiModelProperty("标题")
    private String shortName;
    private String name;
    private String mobile;
    private String idCardNumber;
    private String contractId;
    private String createdTime;
    @ApiModelProperty("融担信息列表")
    private String guaranteeList;



}