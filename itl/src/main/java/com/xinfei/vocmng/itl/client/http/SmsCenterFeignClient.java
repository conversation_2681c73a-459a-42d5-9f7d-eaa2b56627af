package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.SmsTemplateArgs;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 短信中心相关接口
 *
 * <AUTHOR>
 * @version $ SmsCenterFeignClient, v 0.1 2023/12/27 09:41 qu.lu Exp $
 */
@FeignClient(name = FeignConstants.SMS_CENTER, contextId = FeignConstants.SMS_CENTER + ".FeinClient", path = "/")
public interface SmsCenterFeignClient {

    /**
     * 根据手机号码查询
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001015981
     *
     * @param request
     * @return
     */
    @PostMapping("/admin/sms/get-history-record")
    SmsResponse<List<SmsRecordDetail>> queryUserSmsRecordList(SmsBaseRequest<SmsRecordRequest> request);

    /**
     * 短信中心-发送短信
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001003787
     *
     * @param request
     * @return
     */
    @PostMapping("/sms/send")
    BaseUserCenterResponse<SmsSendDTO> smsSend(@RequestBody BaseUserCenterRequest<SmsSendArgs> request);

    @PostMapping("/admin/sms-template/list")
    BaseUserCenterResponse<SmsTemplatedResponse> templateList(@RequestBody BaseUserCenterRequest<SmsTemplateArgs> request);

    /**
     * 分页获取手机号半年内短信记录
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018999
     *
     * @param request
     * @return
     */
    @PostMapping("/admin/sms/loadHistoryRecords")
    SmsResponse<PageResultInfo<List<SmsRecordsDetail>>> queryUserSmsRecordsList(SmsRecordsRequest request);


    /**
     * 获取短信黑名单（老系统）
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001013282
     *
     * @param request
     * @return
     */
    @PostMapping("/admin/sms-black-list/query")
    SmsResponse<SmsBlackResponse> smsBlackList(@RequestBody BaseUserCenterRequest<SmsBlackListRequest> request);


    /**
     * 修改短信黑名单（老系统）
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001013282
     *
     * @param request
     * @return
     */
    @PostMapping("/admin/sms-black-list/save")
    SmsResponse smsBlackSave(@RequestBody BaseUserCenterRequest<SmsBlackEditRequest> request);
}
