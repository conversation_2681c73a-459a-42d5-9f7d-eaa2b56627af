/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.fundcore.facade.api.request.FundOrderQueryRequest;
import com.xinfei.fundcore.facade.api.response.FundOrderQueryResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.FundCoreClient;
import com.xinfei.vocmng.itl.client.http.FundOrderFacade;
import com.xinfei.vocmng.itl.rr.BaseUserCenterRequest;
import com.xinfei.vocmng.itl.rr.BaseUserCenterResponse;
import com.xinfei.vocmng.itl.rr.Order;
import com.xinfei.vocmng.itl.rr.OrderListResponse;
import com.xinfei.vocmng.itl.rr.dto.FundOrderArgs;
import com.xinfei.vocmng.itl.rr.dto.FundOrderDto;
import com.xinfei.vocmng.itl.rr.dto.OrderListArgs;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class FundFeignService {
    @Resource
    private FundOrderFacade fundOrderFacade;

    @Resource
    private FundCoreClient fundCoreClient;


    public FundOrderDto queryLoanQuery(String outOrderNumber, String orderNumber) {
        BaseUserCenterRequest<FundOrderArgs> request = new BaseUserCenterRequest<>();
        FundOrderArgs fundOrderArgs = new FundOrderArgs();
        fundOrderArgs.setOutOrderNumber(outOrderNumber);
        fundOrderArgs.setOrderNumber(orderNumber);
        request.setArgs(fundOrderArgs);
        request.setUa("vocmng");
        request.setSign("vocmng");
        BaseUserCenterResponse<FundOrderDto> response = null;
        String msg = "FundOrderFacade.fundOrder:";
        try {
            response = fundOrderFacade.fundOrder(request);
            log.info(LogUtil.clientLog("FundOrderFacade", "fundOrder", request, response));
            if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getResponse();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundOrderFacade", "fundOrder", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    //资方新接口订单号
    public FundOrderQueryResponse queryLoanNewQuery(String outOrderNumber, String orderNumber) {
        FundOrderQueryRequest request = new FundOrderQueryRequest();
        request.setOutOrderNumber(outOrderNumber);
        request.setOrderNumber(orderNumber);
        return fundCoreClient.fundOrderQuery(request);
    }



    //渠道方订单号
    public List<Order> getOrderList(String outOrderNumber, String orderNumber) {
        BaseUserCenterRequest<OrderListArgs> request = new BaseUserCenterRequest<>();
        OrderListArgs orderListArgs = new OrderListArgs();
        orderListArgs.setOutOrderNumber(outOrderNumber);
        orderListArgs.setOrderNumber(orderNumber);
        orderListArgs.setPage(1);
        orderListArgs.setPageSize(10);
        request.setArgs(orderListArgs);
        BaseUserCenterResponse<OrderListResponse> response = null;
        String msg = "FundOrderFacade.getOrderList:";
        try {
            response = fundOrderFacade.getOrderList(request, "vocmng", UUID.randomUUID().toString());
            log.info(LogUtil.clientLog("FundOrderFacade", "getOrderList", request, response));
            if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getResponse().getList();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FundOrderFacade", "getOrderList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}