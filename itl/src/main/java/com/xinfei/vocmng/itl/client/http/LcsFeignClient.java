package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.BatchLoanQueryResponse;
import com.xinfei.vocmng.itl.rr.PlanDetailResponse;
import com.xinfei.vocmng.itl.rr.QueryLoanReq;
import com.xinfei.vocmng.itl.rr.QueryLoanResp;
import io.kyoto.pillar.lcs.loan.domain.LoanBatchQueryRequest;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.LCS, contextId = FeignConstants.LCS, path = "/")
public interface LcsFeignClient {
    /**
     * LCS批量查询借据、账单信息
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014451@toc82">接口文档</a>
     * <a href="https://dev-lcs.devxinfei.cn/doc.html#/%E5%88%86%E7%BB%84%E5%90%8D%E7%A7%B0/%E5%80%9F%E6%8D%AE%E6%9F%A5%E8%AF%A2%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/batchLoanQueryUsingPOST">swagger文档</a>
     * <a href="https://confluence.joyborrow.com/pages/viewpage.action?pageId=39786846">接口与客服字段对应</a>
     *
     * @param request
     * @return
     */
    @PostMapping("/lcs/loan/batchLoanQuery")
    BatchLoanQueryResponse batchLoanQuery(LoanBatchQueryRequest request);


    @PostMapping("/lcs/loan/plan-detail")
    PlanDetailResponse planDetail(LoanPlanRequest request);

    /**
     * @param request:
     * @return PlanDetailResponse
     * <AUTHOR>
     * @description https://qa1-lcs.testxinfei.cn/doc.html#/%E5%88%86%E7%BB%84%E5%90%8D%E7%A7%B0/%E8%B5%84%E9%87%91%E8%B4%A6%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/queryLoanInfoUsingPOST
     * @date 2024/12/27 15:21
     */
    @PostMapping("/lcs/cap/query-loan-infos")
    QueryLoanResp queryLoanInfo(QueryLoanReq request);
}
