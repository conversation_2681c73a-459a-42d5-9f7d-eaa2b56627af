/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.repaytrade.facade.*;
import com.xinfei.repaytrade.facade.rr.dto.PlanDetailInfo;
import com.xinfei.repaytrade.facade.rr.enums.CalcSettleTypeEnum;
import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.enums.PayTypeEnum;
import com.xinfei.repaytrade.facade.rr.request.*;
import com.xinfei.repaytrade.facade.rr.request.live.LiveRecordRequest;
import com.xinfei.repaytrade.facade.rr.request.live.LiveRepayApplyRequest;
import com.xinfei.repaytrade.facade.rr.request.live.LiveRepayCancelRequest;
import com.xinfei.repaytrade.facade.rr.request.reduction.CalculateReductionAmountRequest;
import com.xinfei.repaytrade.facade.rr.request.repay.RepayCancelRequest;
import com.xinfei.repaytrade.facade.rr.response.Base.BaseDataResponse;
import com.xinfei.repaytrade.facade.rr.response.Base.DataResponse;
import com.xinfei.repaytrade.facade.rr.response.*;
import com.xinfei.repaytrade.facade.rr.response.live.LiveRecordResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.CalculateReductionAmountResponse;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ RepayFacadeClientImpl, v 0.1 2023-12-18 14:20 junjie.yan Exp $
 */

@Component
@Slf4j
public class RepayFacadeClientImpl implements RepayFacadeClient {
    @Resource
    private RepayQueryFacade repayQueryFacade;

    @Resource
    private RepaymentPlanFacade repaymentPlanFacade;

    @Resource
    private GroupPaymentFacade groupPaymentFacade;

    @Resource
    private RepayFacade repayFacade;

    @Resource
    private RepayLiveFacade repayLiveFacade;

    @Resource
    private ReductionFacade reductionFacade;

    @Resource
    private RefundFacade refundFacade;

    @Override
    public QueryRepaymentsByLoanNoResponse getRepayments(List<String> loanNo, List<String> payStatus, String payType, Integer currentPage, Integer pageSize) {
        BaseDataResponse<QueryRepaymentsByLoanNoResponse> response = null;
        QueryRepaymentsByLoanNoRequest request = new QueryRepaymentsByLoanNoRequest();
        request.setLoanNos(loanNo);
        if (CollectionUtils.isNotEmpty(payStatus)) {
            request.setPayStatus(payStatus);
        }
        if (StringUtils.isNotBlank(payType)) {
            request.setPayType(payType);
        }
        request.setPage(currentPage);
        request.setPageSize(pageSize);
        String msg = "RepayQueryFacade.queryLoanRepayments:";
        try {
            response = repayQueryFacade.queryLoanRepayments(request);
            log.info(LogUtil.clientLog("RepayQueryFacade", "queryLoanRepayments", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayQueryFacade", "queryLoanRepayments", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public MultiRepayLoanCalcResponse multiRepayCalculate(List<String> loanNos, CalcSettleTypeEnum calcSettleTypeEnum) {
        BaseDataResponse<MultiRepayLoanCalcResponse> response = null;
        MultiRepayLoanCalcRequest request = new MultiRepayLoanCalcRequest();
        if (CollectionUtils.isEmpty(loanNos)) {
            return null;
        }
        List<OneRepayLoanCalc> oneRepayLoanCalcs = new ArrayList<>();
        loanNos.forEach(e -> {
            OneRepayLoanCalc oneRepayLoanCalc = new OneRepayLoanCalc();
            oneRepayLoanCalc.setLoanNo(e);
            oneRepayLoanCalc.setCalcSettleTypeEnum(calcSettleTypeEnum);
            oneRepayLoanCalcs.add(oneRepayLoanCalc);
        });
        request.setRepayLoanCalcList(oneRepayLoanCalcs);
        String msg = "RepayQueryFacade.multiRepayCalculate:";
        try {
            response = repayQueryFacade.multiRepayCalculate(request);
            log.info(LogUtil.clientLog("RepayQueryFacade", "multiRepayCalculate", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayQueryFacade", "multiRepayCalculate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public RepayLoanCalcResponse repayCalculate(String loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum calcSettleTypeEnum, String settleLimitCalcFlag, List<String> terms, Boolean needRepayCheck, BigDecimal transUnprofitDeduct) {
        BaseDataResponse<RepayLoanCalcResponse> response = null;
        RepayLoanCalcRequest request = new RepayLoanCalcRequest();
        request.setLoanNo(loanNo);
        request.setCalcSettleTypeEnum(calcSettleTypeEnum);
        request.setTerms(terms);
        request.setSettleLimitCalcFlag(settleLimitCalcFlag);
        request.setNeedRepayCheck(needRepayCheck);
        request.setTransUnprofitDeduct(transUnprofitDeduct);
        request.setNeedCanDeductAmt(true);
        if (transUnprofitDeduct != null) {
            request.setFeeAllotScene("REDUCTION");
            request.setSettleLimitCalcFlag("off");
        }
        request.setUnprofitDeductStage("prin");

        String msg = "RepayQueryFacade.repayCalculate:";
        try {
            response = repayQueryFacade.repayCalculate(request);
            log.info(LogUtil.clientLog("RepayQueryFacade", "repayCalculate", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayQueryFacade", "repayCalculate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public RepayLoanCalcResponse repayCalculateFeeRatio(String loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum calcSettleTypeEnum,  List<String> terms, BigDecimal targetFeeRatio, BigDecimal targetDeduct) {
        BaseDataResponse<RepayLoanCalcResponse> response = null;
        RepayLoanCalcRequest request = new RepayLoanCalcRequest();
        request.setLoanNo(loanNo);
        request.setCalcSettleTypeEnum(calcSettleTypeEnum);
        request.setTerms(terms);
        if (Objects.nonNull(targetFeeRatio) || Objects.nonNull(targetDeduct)) {
            DeductCalculateRequest deductCalculateRequest = new DeductCalculateRequest();
            deductCalculateRequest.setTargetFeeRatio(targetFeeRatio);
            deductCalculateRequest.setTargetDeduct(targetDeduct);
            request.setDeductCalculateRequest(deductCalculateRequest);
        }
        String msg = "RepayQueryFacade.repayCalculateFeeRatio:";
        try {
            response = repayQueryFacade.repayCalculate(request);
            log.info(LogUtil.clientLog("RepayQueryFacade", "repayCalculateFeeRatio", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayQueryFacade", "repayCalculateFeeRatio", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public RefundCalculateResponse refundCalculate(String loanNo, BigDecimal targetFeeRatio,  BigDecimal targetRefundAmt) {
        BaseDataResponse<RefundCalculateResponse> response = null;
        RefundCalculateRequest request = new RefundCalculateRequest();
        request.setLoanNo(loanNo);
        request.setTargetFeeRatio(targetFeeRatio);
        request.setTargetRefundAmt(targetRefundAmt);
        String msg = "RefundFacade.refundCalculate:";
        try {
            response = refundFacade.refundCalculate(request);
            log.info(LogUtil.clientLog("RefundFacade", "refundCalculate", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RefundFacade", "refundCalculate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public CreateRepaymentPlanResponse createRepaymentPlan(CreateRepaymentPlanRequest request, List<PlanDetailInfo> planDetailInfos, String creator, List<String> beReducedLoanNos, List<FeeSubjectEnum> reductionFeeList, Date time) {
        BaseDataResponse<CreateRepaymentPlanResponse> response = null;
        request.setCreatedBy(creator);
        request.setPlanDetailInfos(planDetailInfos);
        request.setReductionFromLoanNos(beReducedLoanNos);
        request.setReductionFeeList(reductionFeeList);
        request.setCreatedTimeLimit(time);
        String msg = "RepaymentPlanFacade.createRepaymentPlan:";
        try {
            response = repaymentPlanFacade.createRepaymentPlan(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "createRepaymentPlan", request, response));
            if (response == null) {
                msg += "response is null";
                throw new Exception(msg);
            }
            if (!response.isSuc()) {
                String failedCode = response.getCode().substring(response.getCode().length() - 3, response.getCode().length() - 1);
                if ("812".equals(failedCode) || "813".equals(failedCode) || "814".equals(failedCode)) {
                    msg += response.getErrorContext().getErrDesc();
                } else if ("810".equals(failedCode)) {
                    msg += "创建减免方案失败，抵扣已成功，请勿重复抵扣";
                } else {
                    msg += response.getErrorContext().getErrDesc();
                }
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "createRepaymentPlan", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public RepaymentPlanInvalidResponse repaymentPlanInvalid(List<String> planDetailIds, List<String> loanNos, String remark) {
        if (CollectionUtils.isEmpty(planDetailIds)) {
            return null;
        }

        BaseDataResponse<RepaymentPlanInvalidResponse> response = null;
        RepaymentPlanInvalidRequest request = new RepaymentPlanInvalidRequest();
        request.setPlanDetailIds(planDetailIds);
        request.setRemark(remark);
        request.setLoanNos(loanNos);
        String msg = "RepaymentPlanFacade.repaymentPlanInvalid:";
        try {
            response = repaymentPlanFacade.repaymentPlanInvalid(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "repaymentPlanInvalid", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "repaymentPlanInvalid", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public QueryRepaymentPlanResultResponse queryRepaymentPlanResult(List<String> planDetailIds, Integer planType, String loanNo) {
        if (CollectionUtils.isEmpty(planDetailIds)) {
            return null;
        }
        BaseDataResponse<QueryRepaymentPlanResultResponse> response = null;
        QueryRepaymentPlanResultRequest request = new QueryRepaymentPlanResultRequest();
        request.setPlanDetailIds(planDetailIds);
        request.setLoanNo(loanNo);
        if (planType != null) {
            request.setSettleType(planType == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
        }
        String msg = "RepaymentPlanFacade.queryRepaymentPlanResult:";
        try {
            response = repaymentPlanFacade.queryRepaymentPlanResult(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "queryRepaymentPlanResult", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "queryRepaymentPlanResult", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public RepayApplyResponse repayApply(RepayApplyRequest request) {
        BaseDataResponse<RepayApplyResponse> response = null;
        request.setPayType(PayTypeEnum.MANAGER_AUTO.getCode());

        String msg = "RepayFacade.repayApply:";
        try {
            response = repayFacade.repayApply(request);
            log.info(LogUtil.clientLog("RepayFacade", "repayApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayFacade", "repayApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public RepayApplyResponse liveRepayApply(LiveRepayApplyRequest request) {
        BaseDataResponse<RepayApplyResponse> response = null;

        String msg = "RepayLiveFacade.liveRepayApply:";
        try {
            response = repayLiveFacade.repayApply(request);
            log.info(LogUtil.clientLog("RepayLiveFacade", "liveRepayApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayLiveFacade", "liveRepayApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public List<LiveRecordResponse> liveRecord(List<String> loanNo, List<String> flowNo) {
        LiveRecordRequest request = new LiveRecordRequest();
        request.setLoanNo(loanNo);
        request.setFlowNo(flowNo);
        BaseDataResponse<List<LiveRecordResponse>> response = null;

        String msg = "RepayLiveFacade.liveRecord:";
        try {
            response = repayLiveFacade.liveRecord(request);
            log.info(LogUtil.clientLog("RepayLiveFacade", "liveRecord", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayLiveFacade", "liveRecord", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public Boolean liveRepayCancel(String repaymentNo, String cancelResult, String updatedBy) {
        LiveRepayCancelRequest request = new LiveRepayCancelRequest();
        request.setRepaymentNo(repaymentNo);
        request.setCancelResult(cancelResult);
        request.setUpdatedBy(updatedBy);
        BaseDataResponse<Boolean> response = null;

        String msg = "RepayLiveFacade.liveRepayCancel:";
        try {
            response = repayLiveFacade.repayCancel(request);
            log.info(LogUtil.clientLog("RepayLiveFacade", "liveRepayCancel", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayLiveFacade", "liveRepayCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public List<RepayCancelVO> cancel(String repaymentNo, String cancelReason, String updatedBy) {
        RepayCancelRequest request = new RepayCancelRequest();
        request.setRepaymentNo(repaymentNo);
        request.setCancelReason(cancelReason);
        request.setUpdatedBy(updatedBy);
        DataResponse<List<RepayCancelVO>> response = null;

        String msg = "RepayFacade.cancel:";
        try {
            response = repayFacade.cancel(request);
            log.info(LogUtil.clientLog("RepayFacade", "cancel", request, response));
            if (response == null || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrorStack().get(0).getErrorMsg();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayFacade", "cancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public CalculateReductionAmountResponse calculateReductionAmount(List<String> userNos, List<FeeSubjectEnum> reductionFeeList, Date payTime) {
        CalculateReductionAmountRequest request = new CalculateReductionAmountRequest();
        request.setUserNos(userNos);
        request.setReductionFeeList(reductionFeeList);
        request.setCreatedTimeLimit(payTime);
        BaseDataResponse<CalculateReductionAmountResponse> response = null;

        String msg = "ReductionFacade.calculateReductionAmount:";
        try {
            response = reductionFacade.calculateReductionAmount(request);
            log.info(LogUtil.clientLog("ReductionFacade", "calculateReductionAmount", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ReductionFacade", "calculateReductionAmount", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public QueryRepaymentPlanResponse queryRepaymentPlan(String loan, String planDetailId, String deductChannel, Integer isValid) {
        QueryRepaymentPlanRequest request = new QueryRepaymentPlanRequest();
        request.setLoanNo(loan);
        request.setPlanDetailId(planDetailId);
        request.setDeductChannel(deductChannel);
        request.setIsValid(isValid);
        BaseDataResponse<QueryRepaymentPlanResponse> response = null;
        String msg = "RepaymentPlanFacade.queryRepaymentPlan:";
        try {
            response = repaymentPlanFacade.queryRepaymentPlan(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "queryRepaymentPlan", request, response));
            if (response == null || !"0000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "queryRepaymentPlan", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public QueryRepaymentPlanResponse batchQueryRepaymentPlan(List<String> loan, Integer isValid) {
        if (CollectionUtils.isEmpty(loan)) {
            return null;
        }
        QueryRepaymentPlanRequest request = new QueryRepaymentPlanRequest();
        request.setLoanNos(loan);
        request.setIsValid(isValid);
        BaseDataResponse<QueryRepaymentPlanResponse> response = null;
        String msg = "RepaymentPlanFacade.batchQueryRepaymentPlan:";
        try {
            response = repaymentPlanFacade.queryRepaymentPlan(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "batchQueryRepaymentPlan", request, response));
            if (response == null || !"0000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "batchQueryRepaymentPlan", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }


    @Override
    public QueryLoansAreRepayingResponse queryLoansAreRepaying(List<String> loans) {
        QueryLoansAreRepayingRequest request = new QueryLoansAreRepayingRequest();
        request.setLoanNos(loans);
        BaseDataResponse<QueryLoansAreRepayingResponse> response = null;
        String msg = "RepaymentPlanFacade.queryLoansAreRepaying:";
        try {
            response = repayQueryFacade.queryLoansAreRepaying(request);
            log.info(LogUtil.clientLog("RepaymentPlanFacade", "queryLoansAreRepaying", request, response));
            if (response == null || !"0000".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepaymentPlanFacade", "queryLoansAreRepaying", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }
}