/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.lendtrade.facade.ManageQueryFacade;
import com.xinfei.lendtrade.facade.ManageTradeFacade;
import com.xinfei.lendtrade.facade.rr.*;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.ManageUserLatestOrderDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ LendFacadeClientImpl, v 0.1 2023-12-18 14:20 junjie.yan Exp $
 */

@Component
@Slf4j
public class LendQueryFacadeClientImpl implements LendQueryFacadeClient {

    @Resource
    private ManageQueryFacade manageQueryFacade;

    @Resource
    private ManageTradeFacade manageTradeFacade;

    private final String sysCode = "vocmng";

    /**
     * 借款引擎订单筛选列表
     *
     * @return 订单列表
     */
    @Override
    public Page<ManageOrderDetailDTO> getOrderList(ManageOrderListRequest request) {
        LendPageResponse<ManageOrderDetailDTO> response = null;
        request.setSysCode(sysCode);
        String msg = "ManageQueryFacade.orderList:";
        try {
            response = manageQueryFacade.orderList(request);
            log.info(LogUtil.clientLog("ManageQueryFacade", "orderList", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManageQueryFacade", "orderList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }

        return response.getData();
    }

    @Override
    public ManageOrderDetailDTO getOrderDetail(ManageOrderDetailRequest request) {
        LendResponse<ManageOrderDetailDTO> response = null;
        request.setSysCode(sysCode);
        String msg = "ManageQueryFacade.orderDetail:";
        try {
            response = manageQueryFacade.orderDetail(request);
            log.info(LogUtil.clientLog("ManageQueryFacade", "orderDetail", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManageQueryFacade", "orderDetail", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }

        return response.getData();
    }

    public ManageUserLatestOrderDTO userLatestOrder(ManageUserLatestOrderRequest request) {
        LendResponse<ManageUserLatestOrderDTO> response = null;
        request.setSysCode(sysCode);
        String msg = "ManageQueryFacade.userLatestOrder:";
        try {
            response = manageQueryFacade.userLatestOrder(request);
            log.info(LogUtil.clientLog("ManageQueryFacade", "userLatestOrder", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManageQueryFacade", "userLatestOrder", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }

        return response.getData();
    }

    @Override
    public Boolean orderCancel(String orderNo) {
        LendResponse<Void> response = null;
        OrderCancelRequest request = new OrderCancelRequest();
        request.setSysCode(sysCode);
        request.setOrderNo(orderNo);
        String msg = "ManageTradeFacade.orderCancel:";
        try {
            response = manageTradeFacade.orderCancel(request);
            log.info(LogUtil.clientLog("ManageTradeFacade", "orderCancel", request, response));
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ManageTradeFacade", "orderCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        if (response == null || !response.isSuc()) {
            msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return true;
    }
}