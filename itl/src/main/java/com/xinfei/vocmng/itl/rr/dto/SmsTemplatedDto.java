/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ SmsTemplatedDto, v 0.1 2023-12-29 15:42 junjie.yan Exp $
 */
@Data
public class SmsTemplatedDto {
    private Long id;
    /**
     * 模板Id
     */
    @ApiModelProperty(value = "模板Id")
    private String templateId;
    /**
     * 供应商模板类型
     */
    @ApiModelProperty(value = "供应商模板类型")
    private String group;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**
     * app
     */
    @ApiModelProperty(value = "app")
    private String app;
    /**
     * 使用目的
     */
    @ApiModelProperty(value = "使用目的")
    private String aim;
    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String description;
    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    private String template;
    /**
     * 创建部门
     */
    @ApiModelProperty(value = "创建部门")
    private String createDept;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createdTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updatedTime;
    /**
     * 触发条件
     */
    @ApiModelProperty(value = "触发条件")
    private String trigger;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
}