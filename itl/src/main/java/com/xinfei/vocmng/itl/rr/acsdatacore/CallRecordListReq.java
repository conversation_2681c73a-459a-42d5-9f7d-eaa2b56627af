package com.xinfei.vocmng.itl.rr.acsdatacore;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ CallRecordListReq, v 0.1 2025/3/5 16:11 shaohui.chen Exp $
 */
@Data
public class CallRecordListReq {
    //必填
    private String appKey;
    private String bizId;
    /**
     * 来源系统， 必填
     */
    private String systemCode;

    /**
     * 报文明细,结构与接口入参保持一致
     */
    private List<TCallRecordRequest> callRecordList;

    @Data
    public static class TCallRecordRequest {
        // 主键ID
        private Long id;
        // 应用键，用于标识不同的应用，必填
        private String appKey;
        // 业务ID，用于标识特定的业务，必填
        private String bizId;
        // Genesys批处理编号，与Genesys系统对接时使用的编号
        private String genesysBatchNo;
        // 呼叫ID，唯一标识一次呼叫,必填(默认值-1)
        private String callId;
        // 关键key
        private String keyId;
        // 坐席电话号码，经过加密处理的号码 ,必填
        private String extPhoneNum;
        // 代理ID，标识坐席的ID ,必填
        private String agentId;
        // 代理编号，坐席的编号,必填
        private String agentNo;
        // 代理名称，坐席的名称,必填
        private String agentName;
        // 组织名称，代理所属的组织
        private String orgName;
        // 技能组编号，标识技能组
        private String skillGroupNo;
        // 呼叫类型，如入呼、出呼等， ,必填
        private Integer callType;
        // 外呼类型，用于区分不同的外呼方式  ,必填
        private Integer outboundType;
        // 用户编号，标识用户 ,必填
        private Long userNo;

        /**
         * 原始的手机号,必填
         */
        private String originMobile;

        /**
         * 外呼的手机号，需要拼接网关前缀,必填
         */
        private String callMobile;

        // 手机密文，加密后的手机号码,必填
        private String mobileCipher;
        // 呼叫手机密文，加密后的被叫手机号码,必填
        private String callMobileCipher;
        // 业务线，标识不同的业务线
        private String bizLine;
        // 业务来源代码，标识业务的来源
        private String bizSourceCode;
        // 线路组编号，标识线路组
        private String lineGroupNo;
        // 网关名称，呼叫经过的网关
        private String gatewayName;
        // 线路名称，呼叫使用的线路
        private String lineName;
        // 批处理开始时间
        private LocalDateTime batchStartTime;
        // 批处理结束时间
        private LocalDateTime batchEndTime;
        // 呼叫开始时间,必填
        private LocalDateTime callStartTime;
        // 呼叫结束时间,必填
        private LocalDateTime callEndTime;
        // 代理响铃时间,必填
        private LocalDateTime tAgentAlertTime;
        // 用户响铃时间,必填
        private LocalDateTime tUserAlertTime;
        // 代理响铃时长,必填
        private Integer tAgentAlert;
        // 用户响铃时长,必填
        private Integer tUserAlert;
        // 接通时间,必填
        private LocalDateTime tConnectTime;
        // 接通时长,必填
        private Integer tConnect;
        // 电话ID，标识电话,必填
        private String phoneId;
        // 挂断方，标识哪一方先挂断电话,必填
        private Integer dropSide;
        // 呼叫结果，描述呼叫的结果 ,必填
        private Integer callResult;
        // 线路结果，描述线路使用的结果，选填
        private String lineResult;
        // 完成原因描述，呼叫结束的原因描述，选填
        private String finishCauseDesc;
        // 完成原因代码，呼叫结束的原因代码，选填
        private Integer finishCause;
        // 媒体开始时间 ,必填
        private LocalDateTime mediaStartTime;
        // 媒体结束时间 ,必填
        private LocalDateTime mediaStopTime;
        // 持续时长，单位为毫秒 ,必填
        private Long duration;
        // 主叫电话，呼叫发起的电话号码
        private String callerPhone;
        // 媒体ID，标识媒体文件
        private String mediaId;
        // 媒体文件大小
        private Long mediaSize;
        // 媒体通道URL，媒体文件的访问地址 ,必填
        private String mediaChannelUrl;
        // 媒体OSS路径，媒体文件在OSS上的路径 ,必填
        private String mediaOssPath;
        // 媒体类型，描述媒体的类型 ,必填
        private String mediaType;
        // 流数据，呼叫过程中的流数据 ,必填
        private String flowData;
        // 创建时间，记录创建的时间 ,必填
        private LocalDateTime createdTime;
        // 更新时间，记录最后一次更新的时间 ,必填
        private LocalDateTime updatedTime;
        // 创建日期，记录创建的日期 ,必填
        private LocalDate createDate;
    }
}
