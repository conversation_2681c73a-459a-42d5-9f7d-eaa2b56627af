package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * 订单状态枚举值
 *
 * <AUTHOR>
 * @version $ WorkOrderStatusEnum, v 0.1 2024/1/15 17:55 qu.lu Exp $
 */
@Getter
public enum WorkOrderStatusEnum {

    NOT_SUBMIT(0,"未提交分配"),
    WAIT_ASSIGN(1,"待分配"),
    PROCESS(2,"跟进中"),
    TRANSFER(3,"转派"),
    CHARGEBACK(4,"退单"),
    TERMINAL(5,"终止"),
    KNOWN_END(6,"知悉结案"),
    COLLECTION_END(7,"催收结案"),
    END(8,"已结案"),
    NOT_CONNECT_END(9,"失联结案"),
    REJECT(10,"不接受方案")
    ;

    WorkOrderStatusEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }

    private Integer status;
    private String desc;

    /**
     * 根据工单状态查询工单状态描述信息
     *
     * @param status
     * @return
     */
    public static String getOrderStatusDesc(Integer status){
        if(status == null){
            return null;
        }

        for (WorkOrderStatusEnum orderStatus : WorkOrderStatusEnum.values()){
            if(orderStatus.getStatus().equals(status)){
                return orderStatus.getDesc();
            }
        }

        return null;
    }
}
