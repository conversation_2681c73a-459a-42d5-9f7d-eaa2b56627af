package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.ManageUserLatestOrderRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.ManageUserLatestOrderDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 *
 * @Description: 借款引擎客户端 对接:高书电
 */
public interface LendQueryFacadeClient {


    /**
     * 借款引擎订单筛选列表
     *
     * @return 订单列表
     */
    Page<ManageOrderDetailDTO> getOrderList(ManageOrderListRequest request);

    /**
     * @param request 订单号
     * @return 订单详情
     */
    ManageOrderDetailDTO getOrderDetail(ManageOrderDetailRequest request);

    /**
     * @param request userNos 或 custNo
     * @return 最新一笔订单对应的userNo
     */
    ManageUserLatestOrderDTO userLatestOrder(ManageUserLatestOrderRequest request);

    /**
     * 取消订单
     *
     * @param orderNo
     * @return
     */
    Boolean orderCancel(String orderNo);

}
