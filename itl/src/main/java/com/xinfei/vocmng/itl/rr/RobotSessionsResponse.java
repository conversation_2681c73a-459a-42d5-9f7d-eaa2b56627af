/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ RobotSessionsResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class RobotSessionsResponse {
    /** 对应接口返回值里的 id */
    @JsonProperty("id")
    private Long sessionId;

    /** 机器人id */
    private Long robotId;

    /** 机器人 */
    private Long robot;

    /** 创建时间 */
    private LocalDateTime startTime;

    /** 关闭时间 */
    private LocalDateTime endTime;

    /** 对话总数 */
    private Long totalCount;

    /** IP值 */
    private String ipLocation;

    /** 客户消息数 */
    private Long customerMsgCount;

    /** 机器人消息数 */
    private Long robotMsgCount;

    /** 机器人匹配回答数 */
    private Long robotRepliedCount;

    /** 未知回答数 */
    private Long unknownQuestionCount;

    /** 有用回答数 */
    private Long usefulResponseCount;

    /** 无用回答数 */
    private Long uselessResponseCount;

    /** 评价 1.未评价 2.满意 3.一般 4.不满意 */
    private Long surveyOption;

    /** 评价备注 */
    private String surveyComment;

    /** 是否评价 */
    private String surveyFlag;

    /** 是否转人工 0.无 1.有 */
    private Long isSwitchStaff;

    /** 应用名称 */
    private String appName;

    /** 场景ID */
    private Long channelId;

    /** 场景 */
    private String channelName;

    /** 客户id */
    private Long customerId;

    /** 客户姓名 */
    private String customerName;

    /** imId */
    private Long imSubSessionId;

    /** 对话id */
    private String callId;

    /** 对话描述信息 */
    private String dialogueDesc;

    /** 直接回答数 */
    private Long directResponseCount;

    /** 引导选择回答数 */
    private Long suggestQuestionCount;

    /** 引导未选择回答数 */
    private Long unSuggestQuestionCount;

    /** 客户信息对象 */
    private String customer;

    /** im */
    private String im;}