/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.repaytrade.facade.ReductionFacade;
import com.xinfei.repaytrade.facade.rr.request.reduction.DetailCancelRequest;
import com.xinfei.repaytrade.facade.rr.request.reduction.DetailListRequest;
import com.xinfei.repaytrade.facade.rr.request.reduction.ReductionAmountRequest;
import com.xinfei.repaytrade.facade.rr.response.Base.BaseDataResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.DetailListResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.ReductionAmountResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.RepayTradeClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> 2024/7/16 上午11:21
 */
@Component
@Slf4j
public class RepayTradeClientImpl implements RepayTradeClient {

    @Resource
    private ReductionFacade reductionFacade;

    /**
     * 抵扣记录查询
     */
    @Override
    public DetailListResponse deductionDetailList(List<String> toLoanNo, String status) {
        BaseDataResponse<DetailListResponse> response = null;
        DetailListRequest request = new DetailListRequest();
        request.setToLoanNo(toLoanNo);
        if (StringUtils.isNotEmpty(status)) {
            request.setStatus(status);
        }

        String msg = "ReductionFacade.deductionDetailList:";
        try {
            response = reductionFacade.detailList(request);
            log.info(LogUtil.clientLog("ReductionFacade", "deductionDetailList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ReductionFacade", "deductionDetailList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    /**
     * 抵扣记录明细撤销
     */
    @Override
    public Boolean deductionDetailCancel(String repaymentNo, String updatedBy) {
        BaseDataResponse<Void> response = null;
        DetailCancelRequest request = new DetailCancelRequest();
        request.setUpdatedBy(updatedBy);
        request.setRepaymentNo(repaymentNo);
        String msg = "ReductionFacade.deductionDetailCancel:";
        try {
            response = reductionFacade.detailCancel(request);
            log.info(LogUtil.clientLog("ReductionFacade", "deductionDetailCancel", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ReductionFacade", "deductionDetailCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return true;
    }


    /**
     * 历史抵扣金额获取
     */
    @Override
    public ReductionAmountResponse reductionAmount(String toLoanNo) {
        BaseDataResponse<ReductionAmountResponse> response = null;
        ReductionAmountRequest request = new ReductionAmountRequest();
        request.setToLoanNo(toLoanNo);
        String msg = "ReductionFacade.reductionAmount:";
        try {
            response = reductionFacade.reductionAmount(request);
            log.info(LogUtil.clientLog("ReductionFacade", "reductionAmount", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ReductionFacade", "reductionAmount", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }
}