package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.CreateTaskDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClient, v 0.1 2024/1/15 15:28 qu.lu Exp $
 */
@FeignClient(name = FeignConstants.WORK_ORDER, contextId = FeignConstants.WORK_ORDER + ".PhpFeinClient", path = "/")
public interface WorkOrderFeignClient {

    /**
     * 根据订单号查询工单信息
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018430
     *
     * @param request
     * @return
     */
    @PostMapping("/outapi/task/get-list-by-order-number")
    WorkOrderResponse<WorkOrderListInfo> queryWorkOrderInfo(WorkOrderRequest request);


    /**
     * 根据手机号号查询工单信息
     *
     * @param request
     * @return
     */
    @PostMapping("/outapi/task/get-list-by-phone")
    WorkOrderResponse<WorkOrderListInfo> queryWorkMobileInfo(WorkOrderMobileRequest request);

    /**
     * 根据会话小结查询工单明细信息
     *
     * @param request
     * @return
     */
    @PostMapping("/outapi/task/get-one-by-question-id")
    WorkOrderResponse<WorkOrderSimple> loadWorkOrderInfo(WorkOrderBySummaryRequest request);

    @PostMapping("/outapi/task/get-task-by-user-id")
    WorkOrderResponse<GetTaskByUserIdResp> getTaskByUserId(GetTaskByUserIdReq request);

    @PostMapping("/outapi/task/get-task-by-mobile")
    WorkOrderResponse<List<GetTaskByMobileResp>> getTaskByMobile(GetTaskByMobileReq request);

    @PostMapping("/outapi/task/create-task")
    WorkOrderResponse<Object> createTask(CreateTaskDto request);

    /**
     * 添加反馈
     *
     * @param request
     * @return
     */
    @PostMapping("/outapi/task/add-feedback")
    WorkOrderResponse<Object> addFeedback(AddFeedbackRequest request);

    /**
     * 工单强制结案
     *
     * @param request
     * @return
     */
    @PostMapping("/outapi/task/finish")
    WorkOrderResponse<Object> finishTask(FinishTaskRequest request);
}
