/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2024/8/13 14:10
 * 用戶优惠券 UserCouponDetailDto
 */
@Data
public class UserCouponDetailDto {
    @ApiModelProperty("优惠券id")
    private String couponId;

    @ApiModelProperty("优惠券名称")
    private String couponName;

    @ApiModelProperty("优惠券类型 1:借款免息券 2:还款立减金 3:限时提额券 4:拉卡拉聚合支付 5:x天免息券")
    private String couponType;

    @ApiModelProperty("减免科目")
    private String discountCategory;

    @ApiModelProperty("减免方式")
    private String discountType;

    @ApiModelProperty("优惠券面额")
    private BigDecimal discountAmount ;

    @ApiModelProperty("减免比例")
    private BigDecimal discountRate ;

    @ApiModelProperty("券有效期")
    private String validDaysAfter;

    @ApiModelProperty("可用期次")
    private String periodInfo ;

    @ApiModelProperty("使用时间")
    private String usedTime ;

    @ApiModelProperty("使用说明")
    private List<String> descList;

    @ApiModelProperty("到期时间")
    private String expiredTime;
}