package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.AccountInfoReq;
import com.xinfei.vocmng.itl.rr.AccountInfoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.RCS_PROVIDER, contextId = FeignConstants.RCS_PROVIDER, path = "/")
public interface CreditFeignClient {
    /**
     * 风控-额度信息查询接口
     * 接口文档：<a href="https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8azowOjyF49xXOANWgN7R35y?corpId=dingba457475007182e135c2f4657eb6378f&utm_medium=im_card&iframeQuery=utm_medium%253Dim_card%2526utm_source%253Dim&utm_scene=person_space&utm_source=im">额度信息查询接口</a>
     *
     */
    @PostMapping("/credit/v2/amount-info")
    AccountInfoResp amountInfo(@RequestBody AccountInfoReq request, @RequestHeader(name = "Rpc-Context") String rpcContext);
}
