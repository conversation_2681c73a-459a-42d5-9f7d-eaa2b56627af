package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资方结清明细
 *
 * <AUTHOR>
 * @since 2024/05/31
 */
@Data
public class ContractStatusDetail {

    @JsonProperty("order_number")
    private String orderNumber;

    private String name;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_protyle")
    private String idCardProtyle;

    private String amount;

    @JsonProperty("contract_no")
    private String contractNo;

    @JsonProperty("fund_request_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fundRequestTime;

    @JsonProperty("xyf_settle_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime xyfSettleTime;

    @ApiModelProperty("是否结清")
    @JsonProperty("is_settle")
    private boolean isSettle;

    @JsonProperty("settle_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settleTime;

    @JsonProperty("loan_no")
    private String loanNo;
}
