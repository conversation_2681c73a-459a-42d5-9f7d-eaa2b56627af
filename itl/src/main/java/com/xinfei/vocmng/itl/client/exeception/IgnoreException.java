/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.exeception;

import org.springframework.util.StringUtils;

/**
 * cat可忽略的异常，非告警类异常--------cat已经添加忽略，谨慎使用
 * <AUTHOR>
 * @version $ IgnoreException, v 0.1 2024/4/17 15:09 wancheng.qu Exp $
 */

public class IgnoreException extends RuntimeException{
    /**
     * 结果枚举
     */
    private final TechplayErrDtlEnum resultCodeEnum;

    /**
     * 额外的异常信息
     */
    private String msg;

    /**
     * 构造函数
     *
     * @param resultCodeEnum 错误描述枚举
     */
    public IgnoreException(TechplayErrDtlEnum resultCodeEnum) {
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     */
    public IgnoreException(TechplayErrDtlEnum resultCodeEnum, String msg) {
        super(msg);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param cause          异常
     */
    public IgnoreException(TechplayErrDtlEnum resultCodeEnum, Throwable cause) {
        super(cause);
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     * @param cause          异常
     */
    public IgnoreException(TechplayErrDtlEnum resultCodeEnum, String msg, Throwable cause) {
        super(msg, cause);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * @see Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(200);
        if (super.getMessage() != null) {
            sb.append(super.getMessage());
        }
        sb.append(" 异常原因：");
        sb.append(resultCodeEnum.getDescription());
        if (StringUtils.hasText(msg)) {
            sb.append("|");
            sb.append(msg);
        }
        return sb.toString();
    }

    //~~~ 属性方法 ~~~

    /**
     * Getter method for property <tt>resultCodeEnum</tt>.
     *
     * @return property value of resultCodeEnum
     */
    public TechplayErrDtlEnum getResultCodeEnum() {
        return resultCodeEnum;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }
}