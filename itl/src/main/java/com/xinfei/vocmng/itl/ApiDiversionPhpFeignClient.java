/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version $ UserCenterPhpFeignClient, v 0.1 2024/8/14 15:52 you.zhang Exp $
 */

@FeignClient(name = FeignConstants.PLATFORM_SERVICE_API,contextId = FeignConstants.PLATFORM_SERVICE_API+".PhpFeinClient",path = "/platform-service-api")
public interface ApiDiversionPhpFeignClient {

    @PostMapping("/v1/user-order/get-list")
    ApiDiversionPhpResp queryApiDiversionInfo(ApiDiversionPhpReq request);

    @PostMapping("/v1/user-order/get-product-list")
    ProductResponse queryProductList(ApiDiversionPhpReq request);
}
