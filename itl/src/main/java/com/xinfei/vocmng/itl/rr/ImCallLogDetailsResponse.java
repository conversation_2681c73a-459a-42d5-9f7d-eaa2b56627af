/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ ImCallLogDetailsResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class ImCallLogDetailsResponse {
    /** 对应接口返回值里的 id */
    @JsonProperty("id")
    private Long callLogDetailsId;

    /** 业务记录id */
    @JsonProperty("note_id")
    private Long noteId;

    /** 通话开始时间 */
    @JsonProperty("call_start_at")
    private LocalDateTime callStartAt;

    /** 通话类型("呼入","呼出") */
    @JsonProperty("call_type")
    private String callType;

    /** 主叫号码加密 */
    @JsonProperty("call_number")
    private String callNumberCipher;

    /** 原始主叫号码（未加密）*/
    private String callNumberOrigin;

    /** 号码归属地 */
    @JsonProperty("mobile_area")
    private String mobileArea;

    /** 中继号 */
    @JsonProperty("trunk_number")
    private String trunkNumber;

    /** 客户姓名加密 */
    @JsonProperty("user_name")
    private String userNameCipher;

    /** 客户id */
    @JsonProperty("user_id")
    private Long userId;

    /** 角色名称 */
    @JsonProperty("call_source")
    private String callSource;

    /** 通话来源("客服: xx","队列: xx","负责人: xx","未选择队列") */
    @JsonProperty("queue_type")
    private String queueType;

    /** 排队状态("排队成功","放弃排队","排队超时","无客服在线") */
    @JsonProperty("queue_time")
    private Integer queueTime;

    /** 客服ID */
    @JsonProperty("agent_id")
    private Long agentId;

    /** 客服姓名 */
    @JsonProperty("agent_nick_name")
    private String agentNickName;

    /** 客服邮箱 */
    @JsonProperty("agent_email")
    private String agentEmail;

    /** 设备状态("ip座机","手机") */
    @JsonProperty("device_info")
    private String deviceInfo;

    /** 通话结果("客户未接","客户接听","客服未接","客服拒接","客服接听","未选择队列") */
    @JsonProperty("call_result")
    private String callResult;

    /** 振铃时间 */
    @JsonProperty("ring_time")
    private Integer ringTime;

    /** 保持时长 */
    @JsonProperty("hold_duration")
    private Integer holdDuration;

    /** 挂断方("客户","客服") */
    @JsonProperty("drop_side")
    private String dropSide;

    /** 通话时间 */
    @JsonProperty("call_time")
    private Integer callTime;

    /** 留言 */
    @JsonProperty("leave_message")
    private String leaveMessage;

    /** 客户所属公司id */
    @JsonProperty("organization_id")
    private String organizationId;

    /** 满意度评价("满意","不满意","未评价"或"无需评价") */
    @JsonProperty("satisfaction")
    private String satisfaction;

    /** 满意度评价（新的满意度评价字段，支持智能路由的自定义满意度评价，且兼容快速路由的默认满意度评价） */
    @JsonProperty("survey")
    private String survey;

    /** ivr时长 */
    @JsonProperty("ivr_time")
    private Long ivrTime;

    /** 溢出队列 */
    @JsonProperty("queue_overflow")
    private String queueOverflow;

    /** 自动外呼任务名称 */
    @JsonProperty("ad_task_name")
    private String adTaskName;

    /** ivr变量 */
    @JsonProperty("ivr_variables")
    private String ivrVariables;

    /** 外呼失败原因（开通后显示） */
    @JsonProperty("defeat_cause")
    private String defeatCause;

    /** 外线号码 */
    @JsonProperty("outline_phone_number")
    private String outlinePhoneNumber;

    /** 顺振次数 */
    @JsonProperty("multi_ring_count")
    private Long multiRingCount;

    /** 工单编号 */
    @JsonProperty("tickets")
    private String tickets;

    /** 后续通话 */
    @JsonProperty("has_subsequent_call")
    private String hasSubsequentCall;

    /** 通话唯一标识 */
    @JsonProperty("call_id")
    private String callId;

    @JsonProperty("dtmf")
    private String dtmf;

    @JsonProperty("callout_task_name")
    private String calloutTaskName;

    /** 通话咨询的持续时长 */
    @JsonProperty("consult_duration")
    private Integer consultDuration;

    /** 录音文件地址 */
    @JsonProperty("record_url")
    private String recordUrl;

    @JsonProperty("callout_task_id")
    private String calloutTaskId;

    @JsonProperty("nodes")
    private String nodes;
}