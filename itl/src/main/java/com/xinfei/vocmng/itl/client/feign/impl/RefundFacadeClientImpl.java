/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.repaytrade.facade.RefundFacade;
import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.*;
import com.xinfei.repaytrade.facade.rr.response.Base.BaseDataResponse;
import com.xinfei.repaytrade.facade.rr.response.*;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayFacadeClientImpl, v 0.1 2023-12-18 14:20 junjie.yan Exp $
 */

@Component
@Slf4j
public class RefundFacadeClientImpl {

    @Resource
    private RefundFacade refundFacade;

    /**
     * 可退款金额
     *
     * @param loanNo
     * @param refundFeeList
     * @param billNo
     * @return
     */
    public CalculateRefundAmountResponse calculateRefundAmount(String loanNo, List<FeeSubjectEnum> refundFeeList, String billNo, List<String> repaymentNos) {
        CalculateRefundAmountRequest request = new CalculateRefundAmountRequest();
        request.setLoanNo(loanNo);
        request.setRefundFeeList(refundFeeList);
        request.setPlanNo(billNo);
        request.setRepaymentNos(repaymentNos);
        BaseDataResponse<CalculateRefundAmountResponse> response = null;

        String msg = "RepayRefundFacade.calculateRefundAmount:";
        try {
            response = refundFacade.calculateRefundAmount(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "calculateRefundAmount", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "calculateRefundAmount", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    /**
     * 历史抵扣（抵扣出去）、退款总额
     *
     * @param loanNo
     * @return
     */
    public RefundedAmountResponse refundedAmount(String loanNo) {
        RefundedAmountRequest request = new RefundedAmountRequest();
        request.setLoanNo(loanNo);
        BaseDataResponse<RefundedAmountResponse> response = null;

        String msg = "RepayRefundFacade.refundedAmount:";
        try {
            response = refundFacade.refundedAmount(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "refundedAmount", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "refundedAmount", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public RefundTrialResponse refundTrial(String loanNo, List<FeeSubjectEnum> refundFeeList, String billNo, BigDecimal totalRefundAmt, String refundType, List<String> repaymentNos) {
        RefundTrialRequest request = new RefundTrialRequest();
        request.setLoanNo(loanNo);
        request.setRefundFeeList(refundFeeList);
        request.setPlanNo(billNo);
        request.setTotalRefundAmt(totalRefundAmt);
        request.setRefundType(refundType);
        request.setRepaymentNos(repaymentNos);
        BaseDataResponse<RefundTrialResponse> response = null;

        String msg = "RepayRefundFacade.refundTrial:";
        try {
            response = refundFacade.refundTrial(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "refundTrial", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "refundTrial", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public RefundApplyResponse refundApply(RefundApplyRequest request) {
        BaseDataResponse<RefundApplyResponse> response = null;
        String msg = "RepayRefundFacade.refundApply:";
        try {
            response = refundFacade.refundApply(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "refundApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "refundApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public QueryRefundApplyResponse queryRefundApply(List<String> refundInstructionNos, List<String> loanNos, String status) {
        QueryRefundApplyRequest request = new QueryRefundApplyRequest();
        request.setLoanNos(loanNos);
        request.setRefundInstructionNos(refundInstructionNos);
        request.setRefundStatus(status);
        BaseDataResponse<QueryRefundApplyResponse> response = null;

        String msg = "RepayRefundFacade.queryRefundApply:";
        try {
            response = refundFacade.queryRefundApply(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "queryRefundApply", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "queryRefundApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public RefundRightNowResponse refundRightNow(String refundInstructionNo, String updatedBy) {
        RefundRightNowRequest request = new RefundRightNowRequest();
        request.setRefundInstructionNo(refundInstructionNo);
        request.setUpdatedBy(updatedBy);
        BaseDataResponse<RefundRightNowResponse> response = null;
        String msg = "RepayRefundFacade.refundRightNow:";
        try {
            response = refundFacade.refundRightNow(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "refundRightNow", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "refundRightNow", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    public RefundCancelResponse refundCancel(String refundInstructionNo, String updatedBy) {
        BaseDataResponse<RefundCancelResponse> response = null;
        RefundCancelRequest request = new RefundCancelRequest();
        request.setRefundInstructionNo(refundInstructionNo);
        request.setUpdatedBy(updatedBy);

        String msg = "RepayRefundFacade.refundCancel:";
        try {
            response = refundFacade.refundCancel(request);
            log.info(LogUtil.clientLog("RepayRefundFacade", "refundCancel", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayRefundFacade", "refundCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }
}