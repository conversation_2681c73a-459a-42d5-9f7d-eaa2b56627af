package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.repaytrade.facade.rr.dto.PlanDetailInfo;
import com.xinfei.repaytrade.facade.rr.enums.CalcSettleTypeEnum;
import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.CreateRepaymentPlanRequest;
import com.xinfei.repaytrade.facade.rr.request.GroupPaymentApplyRequest;
import com.xinfei.repaytrade.facade.rr.request.RepayApplyRequest;
import com.xinfei.repaytrade.facade.rr.request.RepayLoanCalcRequest;
import com.xinfei.repaytrade.facade.rr.request.live.LiveRepayApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.*;
import com.xinfei.repaytrade.facade.rr.response.live.LiveRecordResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.CalculateReductionAmountResponse;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 *
 * @Description: 借款引擎客户端 对接:高书电
 */
public interface RepayFacadeClient {

    QueryRepaymentsByLoanNoResponse getRepayments(List<String> loanNo, List<String> payStatus, String payType, Integer currentPage, Integer pageSize);

    MultiRepayLoanCalcResponse multiRepayCalculate(List<String> loanNos, CalcSettleTypeEnum calcSettleTypeEnum);

    RepayLoanCalcResponse repayCalculate(String loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum calcSettleTypeEnum, String settleLimitCalcFlag, List<String> terms, Boolean needRepayCheck, BigDecimal transUnprofitDeduct);

    /**
     * 费率计算
     * @param loanNo
     * @param calcSettleTypeEnum
     * @param terms
     * @param targetFeeRatio
     * @param targetDeduct
     * @return
     */
    RepayLoanCalcResponse repayCalculateFeeRatio(String loanNo, RepayLoanCalcRequest.CalcSettleTypeEnum calcSettleTypeEnum,  List<String> terms, BigDecimal targetFeeRatio, BigDecimal targetDeduct);

    /**
     * 退款计算费率
     * @param loanNo
     * @param targetFeeRatio
     * @param targetRefundAmt
     * @return
     */
    RefundCalculateResponse refundCalculate(String loanNo, BigDecimal targetFeeRatio,  BigDecimal targetRefundAmt);

    /**
     * 创建减免方案
     *
     * @param planDetailInfos 方案明细列表，必传
     * @return
     */
    CreateRepaymentPlanResponse createRepaymentPlan(CreateRepaymentPlanRequest request, List<PlanDetailInfo> planDetailInfos, String creator, List<String> beReducedLoanNos, List<FeeSubjectEnum> reductionFeeList, Date date);


    /**
     * 减免方案失效
     *
     * @param planDetailIds 必传
     * @param remark        非必传
     * @return
     */
    RepaymentPlanInvalidResponse repaymentPlanInvalid(List<String> planDetailIds, List<String> loanNos, String remark);


    /**
     * 查询减免方案结果
     *
     * @param planDetailIds 必传
     * @param planType      非必传
     * @param loanNo        非必传
     * @return
     */
    QueryRepaymentPlanResultResponse queryRepaymentPlanResult(List<String> planDetailIds, Integer planType, String loanNo);


//    /**
//     * @param repaymentNo 还款单号(聚合支付) 必填
//     * @param reason      关闭理由 必填
//     * @return
//     */
//    Void close(String repaymentNo, String reason);

    /**
     * 系统代扣
     *
     * @param request
     * @return
     */
    RepayApplyResponse repayApply(RepayApplyRequest request);

    /**
     * 线下销账
     *
     * @param request
     * @return
     */
    RepayApplyResponse liveRepayApply(LiveRepayApplyRequest request);

    /**
     * 销账记录
     *
     * @param loanNo 借据号，不为空
     * @param flowNo 流水号
     * @return
     */
    List<LiveRecordResponse> liveRecord(List<String> loanNo, List<String> flowNo);

    /**
     * 销账撤回
     *
     * @param repaymentNo  还款编号 必填
     * @param cancelResult 撤回理由 必填
     * @param updatedBy    更新人 必填
     * @return
     */
    Boolean liveRepayCancel(String repaymentNo, String cancelResult, String updatedBy);

    /**
     * 还款撤回
     * @param repaymentNo
     * @param cancelReason
     * @param updatedBy
     * @return
     */
    List<RepayCancelVO> cancel(String repaymentNo, String cancelReason, String updatedBy);

    /**
     * 可抵扣订单及金额
     *
     * @param userNos          必填
     * @param reductionFeeList 必填
     * @param payTime          必填
     * @return
     */
    CalculateReductionAmountResponse calculateReductionAmount(List<String> userNos, List<FeeSubjectEnum> reductionFeeList, Date payTime);


    /**
     * 催收贷后方案查询
     *
     * @param loan 必填
     * @return
     */
    QueryRepaymentPlanResponse queryRepaymentPlan(String loan, String planDetailId, String deductChannel, Integer isValid);


    /**
     * 方案批量查询
     *
     */
    QueryRepaymentPlanResponse batchQueryRepaymentPlan(List<String> loan, Integer isValid);


    QueryLoansAreRepayingResponse queryLoansAreRepaying(List<String> loans);

}
