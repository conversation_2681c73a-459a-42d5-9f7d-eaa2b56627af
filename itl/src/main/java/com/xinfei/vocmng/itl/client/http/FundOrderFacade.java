package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.FundOrderArgs;
import com.xinfei.vocmng.itl.rr.dto.FundOrderDto;
import com.xinfei.vocmng.itl.rr.dto.OrderListArgs;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ SmsCenterFacade, v 0.1 2023/11/23 18:06 valiant.shaw Exp $
 * @Description:
 */
@FeignClient(name = FeignConstants.FUND_SERVICE_NAME, contextId = FeignConstants.FUND_SERVICE_NAME, path = "/")
public interface FundOrderFacade {

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001007258">资金订单号信息</a>
     */
    @PostMapping("/fund/fund/fund-order-query")
    BaseUserCenterResponse<FundOrderDto> fundOrder(BaseUserCenterRequest<FundOrderArgs> request);

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001016726@toc0">渠道订单号</a>
     */
    @PostMapping("/credit_inner_query/cash-loan-order/get-order-list")
    BaseUserCenterResponse<OrderListResponse> getOrderList(BaseUserCenterRequest<OrderListArgs> request, @RequestHeader(name = "ua") String ua, @RequestHeader(name = "request-float-number") String requestFloatNumber);


    /**
     * 放款凭证提交 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018927
     * @date 2024/6/4 16:52
     */
    @PostMapping("/fund/admin/order/apply-loan-proof")
    BaseUserCenterResponse<ApplyLoanProofResp> applyLoanProof(BaseUserCenterRequest<ApplyLoanProofReq> request);

    /**
     * 放款凭证证明查看 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018928
     * @date 2024/6/4 16:52
     */
    @PostMapping("/fund/admin/order/query-loan-proof")
    BaseUserCenterResponse<ApplyLoanProofResp> queryLoanProof(BaseUserCenterRequest<ApplyLoanProofReq> request);

    /**
     * 资方结清证明提交 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018928
     * @date 2024/6/4 16:52
     */
    @PostMapping("/fund/admin/settle/online-settle-proof")
    BaseUserCenterResponse<ApplyOnlineProofResp> applyOnlineProof(BaseUserCenterRequest<ApplyLoanProofReq> request);

    /**
     * 资方结清证明查看 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001018928
     * @date 2024/6/4 16:52
     */
    @PostMapping("/fund/admin/settle/get-settle-link-info")
    BaseUserCenterResponse<OnlineProofResp> queryOnlineProof(BaseUserCenterRequest<ApplyLoanOrderReq> request);

    /**
     * 批量获取订单结清证明需要的信息
     * 接口文档：https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001006764
     * @param request
     * @return
     */
    @PostMapping("/fund/admin/settle/settle-proof")
    BaseUserCenterResponse<List<ContractStatusDetail>> queryContractStatusList(BaseUserCenterRequest<ApplyLoanProofReq> request);

}
