/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xyf.bank.dto.request.QueryBankCardInfoRequest;
import com.xyf.bank.dto.request.QueryBankCardListByUserNoRequest;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.bank.facade.BankCardFacade;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class BankCardFacadeService {
    @Resource
    private BankCardFacade bankCardFacade;

    public List<BankCardResponse> queryBankCardListHistoryByUserNo(String userNo) {
        if (StringUtils.isEmpty(userNo)) {
            return null;
        }
        Long userNoLong = Long.parseLong(userNo);

        QueryBankCardListByUserNoRequest request = new QueryBankCardListByUserNoRequest();
        request.setUserNo(userNoLong);
        BaseResponse<List<BankCardResponse>> response = null;
        String msg = "BankCardFacade.queryBankCardListHistoryByUserNo:";
        try {
            response = bankCardFacade.queryBankCardListHistoryByUserNo(request);
            log.info(LogUtil.clientLog("BankCardFacade", "queryBankCardListHistoryByUserNo", request, response));
            if (Objects.isNull(response) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("BankCardFacade", "queryBankCardListHistoryByUserNo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<BankCardResponse> queryBankCardList(String custNo, String app) {

        QueryBankCardInfoRequest request = new QueryBankCardInfoRequest();
        request.setApp(app);
        request.setCustNo(custNo);
        BaseResponse<List<BankCardResponse>> response = null;
        String msg = "BankCardFacade.queryBankCardListHistoryByUserNo:";
        try {
            response = bankCardFacade.queryBankCardList(request);
            log.info(LogUtil.clientLog("BankCardFacade", "queryBankCardListHistoryByUserNo", request, response));
            if (Objects.isNull(response) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("BankCardFacade", "queryBankCardListHistoryByUserNo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}