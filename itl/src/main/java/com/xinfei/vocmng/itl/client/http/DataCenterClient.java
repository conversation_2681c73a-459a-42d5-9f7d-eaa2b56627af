package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.MobileGetReq;
import com.xinfei.vocmng.itl.rr.MobileGetResp;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.DATACENTER, contextId = FeignConstants.DATACENTER, path = "/")
public interface DataCenterClient {

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001010941">获取手机号明文接口</a>
     *
     * @return
     */
    @RequestMapping(value = "/data-center/user/mobile-get", method = POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Headers("Content-Type: application/x-www-form-urlencoded")
    MobileGetResp mobileGet(@RequestParam("q_no") String qNo, @RequestBody Map<String, String> formParams);

    @PostMapping(value = "/data-center/user/mobile-get", headers = {"Content-Type=application/x-www-form-urlencoded"})
    MobileGetResp mobileGet(@RequestParam("q_no") String qNo, @RequestBody MobileGetReq req);
}

