/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ UserOrderContractReq, v 0.1 2024/8/26 11:29 wancheng.qu Exp $
 */
@Data
public class UserOrderContractReq implements Serializable {

    private String mobile;
    private String fdd_download_url_neq;
    private List<String> andor_extra_data_multi_like;
    private Integer sort_by_contract_id = 3;
    private Integer sort_by_fdd_contract_id=3;
    private String contract_id;
    private Integer period;
    private String fdd_status;
    private List<String> andor_contract_names_multi_like;


}