package com.xinfei.vocmng.itl.model.header;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OrderListHeader extends BaseHeader {
    /**
     * 调用链路id
     */
    @JsonProperty(value = "TRACE_ID")
    private String traceId;
    /**
     * 应用名称
     */
    @JsonProperty(value = "ua")
    private String ua;

    @JsonProperty(value = "request-float-number")
    private String requestFloatNumber;
}
