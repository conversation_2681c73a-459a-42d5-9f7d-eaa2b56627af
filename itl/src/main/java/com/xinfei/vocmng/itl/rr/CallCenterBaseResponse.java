package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * 呼叫中心接口响应通用格式
 *
 * <AUTHOR>
 * @version $ CallCenterBaseResponse, v 0.1 2023/12/27 21:48 qu.lu Exp $
 */
@Data
public class CallCenterBaseResponse<T> {
    /** 响应标识： S：成功，F：失败*/
    private String flag;
    /** 如果flag为S时，返回0，其余失败场合有对应错误码 */
    private Integer code;
    /** 响应提示语 */
    private String message;
    /** 接口实际数据返回的承载字段 */
    private T data;
    /** 时间 */
    private String dateTime;
    /** 页码 */
    private Integer pageNumber;
    /** 每页条目 */
    private Integer pageSize;
    /** 总条数 */
    private Integer total;

    /**
     * 服务响应是否成功：true成功
     *
     * @return
     */
    public boolean isSuccess(){
        return FeignConstants.CALL_CENTER_SUCCESS_CODE.equalsIgnoreCase(flag);
    }

}
