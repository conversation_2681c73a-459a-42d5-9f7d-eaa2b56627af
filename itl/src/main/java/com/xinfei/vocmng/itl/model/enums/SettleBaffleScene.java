/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ DeductRuleEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum SettleBaffleScene {
    //挡板命中场景  TRIAL-客服测算 TRIAL_PROCESS-还款+试算
    TRIAL("TRIAL", "客服测算"),
    TRIAL_PROCESS("TRIAL_PROCESS", "还款+试算");

    private final String code;
    private final String description;

    SettleBaffleScene(String code, String description) {
        this.code = code;
        this.description = description;
    }

}