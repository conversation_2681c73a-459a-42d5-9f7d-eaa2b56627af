package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.CouponDetail;
import com.xinfei.vocmng.itl.rr.CouponRequest;
import com.xinfei.vocmng.itl.rr.CouponResponse;
import com.xinfei.vocmng.itl.rr.PageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR> 2024/8/12 17:45
 * 优惠券相关接口
 */
@FeignClient(name = FeignConstants.COUPON, contextId = FeignConstants.COUPON + ".FeinClient", path = "/")
public interface CouponClient {

    /**
     * 查询用户优惠券
     * 接口文档 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001006988
     *
     * @param request
     * @return
     */
    @PostMapping("/userassetcore/cash-coupon/user/coupon-list")
    CouponResponse<PageResponse> getUserCoupon(CouponRequest request);

    /**
     * 查询用户优惠券详情
     * 接口文档 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001006988
     *
     * @param request
     * @return
     */
    @PostMapping("/userassetcore/cash-coupon/user/coupon-detail")
    CouponResponse<CouponDetail> getUserDetail(CouponRequest request);

}
