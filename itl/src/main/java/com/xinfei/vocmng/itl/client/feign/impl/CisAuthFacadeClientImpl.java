/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.CisAuthFacadeClient;
import com.xinfei.vocmng.itl.util.CisCommonAttributes;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xyf.user.auth.dto.request.QueryOcrFaceImagesRequest;
import com.xyf.user.auth.dto.response.OcrFaceImagesResponse;
import com.xyf.user.auth.facade.UserAuthFacade;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class CisAuthFacadeClientImpl implements CisAuthFacadeClient {

    @Resource
    private UserAuthFacade userAuthFacade;

    @Override
    public OcrFaceImagesResponse queryOcrFaceImages(Long userNo, String custNo, String status) {
        BaseResponse<OcrFaceImagesResponse> response = null;
        QueryOcrFaceImagesRequest request = new QueryOcrFaceImagesRequest();
        request.setUserNo(userNo);
        request.setCustNo(custNo);
        request.setStatus(status);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "UserAuthFacade.queryOcrFaceImages:";
        try {
            response = userAuthFacade.queryOcrFaceImages(request);
            log.info(LogUtil.clientLog("UserAuthFacade", "queryOcrFaceImages", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserAuthFacade", "queryOcrFaceImages", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}