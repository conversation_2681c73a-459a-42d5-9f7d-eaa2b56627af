package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version $ SmsDeliveryStatusEnum, v 0.1 2024/1/16 13:21 qu.lu Exp $
 */
@Getter
public enum SmsDeliveryStatusEnum {

    WAIT_COMMIT("wait_commit","等待请求接口"),
    COMMIT_FAILED("commit_failed","接口请求失败"),
    WAIT_DELIVERY("wait_delivery","等待渠道发送"),
    DELIVERED("delivered","渠道发送成功"),
    FAILED("failed","发送失败"),
    ;

    SmsDeliveryStatusEnum(String deliveryStatus, String msg){
        this.deliveryStatus = deliveryStatus;
        this.msg = msg;
    }

    private String deliveryStatus;
    private String msg;

    /**
     * 根据发送状态码查询状态描述信息
     *
     * @param deliveryStatus
     * @return
     */
    public static String getDeliveryStatusMsgByStatus(String deliveryStatus){
        if(StringUtils.isEmpty(deliveryStatus)){
            return null;
        }

        for (SmsDeliveryStatusEnum status : SmsDeliveryStatusEnum.values()){
            if(status.getDeliveryStatus().equalsIgnoreCase(deliveryStatus)){
                return status.getMsg();
            }
        }

        return null;
    }
}
