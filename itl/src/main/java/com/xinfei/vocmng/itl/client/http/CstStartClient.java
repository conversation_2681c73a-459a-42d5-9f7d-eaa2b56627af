package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = FeignConstants.CST, contextId = FeignConstants.CST, path = "/")
public interface CstStartClient {


    /**
     * 银行流水
     *
     * @param request
     * @return
     */
    @PostMapping("/api/offlineRefund/queryOverPayFlowTransLists")
    RefundRecordResponse<RefundRecordResult> queryOverPayFlowTransLists(RefundRecordQueryReq request);

    /**
     * 退款申请
     *
     * @param request
     * @return
     */
    @PostMapping("/api/offlineRefund/overRefundApply")
    RefundRecordResponse<Boolean> overRefundApply(OverRefundApplyReq request);

    /**
     * 退款申请撤销
     *
     * @param request
     * @return
     */
    @PostMapping("/api/offlineRefund/refundApplyCancel")
    RefundRecordResponse<Boolean> refundApplyCancel(RefundApplyCancelReq request);

    /**
     * 根据流水号查询退款记录
     *
     * @param request
     * @return
     */
    @PostMapping("/api/offlineRefund/queryRefundOrderRecordListByTransNo")
    RefundRecordResponse<List<RefundOrderRecordRes>> queryRefundOrderRecordListByTransNo(RefundQueryConditionReq request);
}
