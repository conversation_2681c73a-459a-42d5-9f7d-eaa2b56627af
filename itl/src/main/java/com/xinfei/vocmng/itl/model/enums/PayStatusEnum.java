package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @version $ PayStatusEnum, v 0.1 2023/12/25 21:19 qu.lu Exp $
 */
@Getter
public enum PayStatusEnum {
    PAID(2,"已支付"),
    NOT_PAID(0,"未支付"),
    UNKNOWN(1,"已支付"),
    ;

    PayStatusEnum(Integer payStatus, String desc){
        this.payStatus = payStatus;
        this.desc = desc;
    }

    /** 支付状态 */
    private Integer payStatus;
    /** 状态描述 */
    private String desc;


    /**
     * 根据支付状态码获取支付状态描述
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code){
        if(code == null){
            return null;
        }

        for (PayStatusEnum status : PayStatusEnum.values()){
            if(status.payStatus.equals(code)){
                return status.getDesc();
            }
        }

        return null;
    }
}
