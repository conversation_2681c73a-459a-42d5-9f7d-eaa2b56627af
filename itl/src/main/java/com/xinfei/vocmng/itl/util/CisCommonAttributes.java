/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.util;

import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.supervip.common.model.SvcBaseRequest;
import com.xyf.user.facade.common.model.BaseCisRequest;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ CisCommonAttributes, v 0.1 2025-04-02 15:12 junjie.yan Exp $
 */

public class CisCommonAttributes {

    private static final String upstreamService = "vocmng";

    // 产品码
    private static final String cnlPdCode = "CSC001000070";

    // 事件码
    private static final String cnlEvCode = "CD00100051";

    // 统一设置公共属性的工具方法
    public static <T extends BaseCisRequest> void setCommonAttributes(T request) {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(cnlPdCode);
        bizTraceContext.setCnlEvCode(cnlEvCode);
        request.setUpstreamService(upstreamService);
        request.setBizTraceContext(bizTraceContext);
    }

    public static <T extends SvcBaseRequest> void setVipCardCommonAttributes(T request) {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(cnlPdCode);
        bizTraceContext.setCnlEvCode(cnlEvCode);
        request.setBizChannel(upstreamService);
        request.setRequestId(UUID.randomUUID().toString());
        request.setBizTraceContext(bizTraceContext);
    }


}