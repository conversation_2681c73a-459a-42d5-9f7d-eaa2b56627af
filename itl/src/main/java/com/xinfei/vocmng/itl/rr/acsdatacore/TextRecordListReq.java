package com.xinfei.vocmng.itl.rr.acsdatacore;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ TextRecordListReq, v 0.1 2025/3/5 16:14 shaohui.chen Exp $
 */
@Data
public class TextRecordListReq {

    private String appKey;
    private String bizId;
    /**
     * 来源系统
     */
    private String systemCode;

    /**
     * 会话信息
     */
    private SessionRecordRequest sessionRecordRequest;
    /**
     * 报文明细,结构与接口入参保持一致
     */
    private List<TextRecordRequest> textRecordList;

    @Data
    public static class SessionRecordRequest{

        /**
         * 会话ID
         */
        private String sessionId;

        private LocalDateTime startTime;

        private LocalDateTime endTime;

        private String agentId;

        private String agentName;

        /**
         * 对话次数
         */
        private Integer totalCount;
        private Integer agentMsgCount;
        private Integer customerMsgCount;

        /**
         * 业务扩展信息
         */
        private String bizExtInfo;


        private LocalDateTime createTime;
        private LocalDateTime updateTime;
    }

    @Data
    public static class TextRecordRequest{
        /**
         * 记录id
         */
        private Long id;
        /**
         * 必填
         */
        private String appKey;

        /**
         * 创建时间,必填
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createdTime;

        /**
         * 业务ID
         */
        private String bizId;

        /**
         * 会话ID ,必填
         */
        private String sessionId;
        /**
         * 请求的唯一标识,必填
         */
        private String msgId;

        /**
         * 厂商渠道(MBS-指掌易， udesk)
         */
        private String channel;

        /**
         * 坐席UM,必填
         */
        private String agentId;

        /**
         * 坐席账号,必填
         */
        private String agentName;

        /**
         * 发送手机号
         */
        private String iccPhone;

        /**
         * 客户手机号
         */
        private String toPhone;

        /**
         * 发送类型 0-发送 1-接收
         */
        private Integer sendType;

        /**
         * 发送时间,必填
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date sendTime;

        /**
         * 短信内容,必填
         */
        private String content;

        /**
         * 文本类型
         */
        private String contentType;

        /**
         * 业务扩展信息(map结构)
         */
        private String bizExtInfo;

        /**
         * 渠道记录ID
         */
        private String channelRecordId;

        /**
         * 产生数据的设备的唯一标识
         */
        private String deviceId;

        /**
         * sim卡编号
         */
        private Integer simNo;

        /**
         * 设备唯一标识
         */
        private String imei;

        /**
         * 设备唯一标识
         */
        private String imei2;

        /**
         * sim卡唯一标识
         */
        private String iccId;

        /**
         * 发送短信网络类型 0-其它 1-5G短信
         */
        private Integer networkType;

        /**
         * 发起方 0-业务系统发起 1-手机发起
         */
        private Integer sendFrom;

        /**
         * 状态 1-接收成功 2-发送成功 5-发送失败 6-发送中
         */
        private String status;

        /**
         * 失败消息
         */
        private String failMsg;

        /**
         * 回调时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date callbackTime;

        /**
         * 信息类型(0-短信 1-彩信，2 udesk-IM内容)
         */
        private Integer type;

        /**
         * 标题
         */
        private String subject;

        /**
         * 源信息id
         */
        private String sourceMsgId;

        /**
         * 送达状态：1-已送达，2-未知
         */
        private Integer sentStatus;

        /**
         * 发送短信网络类型 0-其它 1-5G短信
         */
        private Integer processStatus;

    }
}
