/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.SmsCenterFeignClient;
import com.xinfei.vocmng.itl.client.http.SmsRobotFeignClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.DingMsgDto;
import com.xinfei.vocmng.itl.rr.dto.SmsTemplateArgs;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class SmsFeignService {
    @Resource
    private SmsCenterFeignClient smsCenterFeignClient;

    @Resource
    private SmsRobotFeignClient smsRobotFeignClient;

    private static final String system = "vocmng";

    public SmsTemplatedResponse templateList(String bizType, Integer pageSize, Integer page) {
        BaseUserCenterRequest<SmsTemplateArgs> request = new BaseUserCenterRequest<>();
        SmsTemplateArgs args = new SmsTemplateArgs();
        args.setBizType(bizType);
        args.setPageSize(pageSize);
        args.setPage(page);
        request.setArgs(args);
        request.setUa(system);
        BaseUserCenterResponse<SmsTemplatedResponse> response = null;
        String msg = "SmsCenterFeignClient.templateList:";
        try {
            response = smsCenterFeignClient.templateList(request);
            log.info(LogUtil.clientLog("SmsCenterFeignClient", "templateList", request, response));
            if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getResponse();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SmsCenterFeignClient", "templateList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean smsSend(String mobile, String templateId, String app, Map<String, Object> data) {
        if(data!=null){
            if (data.get("#amount") != null && data.get("#amount").equals(BigDecimal.ZERO)) {
                return true;
            }
        }
        BaseUserCenterRequest<SmsSendArgs> request = new BaseUserCenterRequest<>();
        SmsSendArgs args = new SmsSendArgs();
        args.setTemplateId(templateId);
        args.setMobile(mobile);
        args.setApp(app);
        if(data!=null) {
            args.setData(data);
        }
        request.setArgs(args);
        request.setUa(system);
        BaseUserCenterResponse<SmsSendDTO> response = null;
        String msg = "SmsCenterFeignClient.smsSend:";
        try {
            response = smsCenterFeignClient.smsSend(request);
            log.info(LogUtil.clientLog("SmsCenterFeignClient", "smsSend", request, response));
            if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                if (response.getStatus() == 499110) {
                    msg = "手机号命中短信黑名单，请更换手机号或选择其他还款方式";
                } else {
                    msg = response == null ? "response is null" : response.getMessage();
                }
                throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SmsCenterFeignClient", "smsSend", request, response, e.getMessage()), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean sendRobotMsg(String title, String content, List<String> atMobiles) {
        BaseUserCenterRequest<DingMsgDto> request = new BaseUserCenterRequest<>();
        DingMsgDto args = new DingMsgDto();
        args.setBizType("vocmng_summary");
        args.setMsgType("text");
        args.setTitle(title);
        args.setContent(content);
        args.setAtMobiles(atMobiles);
        args.setAtAll(false);
        request.setArgs(args);
        request.setUa(system);
        BaseUserCenterResponse<String> response = null;
        String msg = "SmsCenterFeignClient.sendRobotMsg:";
        try {
            response = smsRobotFeignClient.sendRobotMsg(request);
            log.info(LogUtil.clientLog("SmsCenterFeignClient", "sendRobotMsg", request, response));
            if (Objects.isNull(response) || response.getStatus() != 1 || !"Success".equals(response.getMessage())) {
                msg = response == null ? "response is null" : response.getMessage();
                throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SmsCenterFeignClient", "sendRobotMsg", request, response, e.getMessage()), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}