package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 会员权益相关的接口
 *
 * <AUTHOR>
 * @since 2023/12/20
 */
@FeignClient(name = FeignConstants.MEMBER_INTEREST, contextId = FeignConstants.MEMBER_INTEREST + ".PhpFeinClient", path = "/app")
public interface MemberInterestFeignClient {
    /**
     * 会员卡列表查询接口（该卡已停售，但可能会有退卡的诉求）
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/vip/pay-records")
    MemberInterestResponse<List<OldMemberCard>> queryPayRecords(MemberCardRequest request);

    /**
     * 查询会员卡信息（在售中）
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/vip/get-new-vip-records")
    MemberInterestResponse<List<MemberCard>> queryNewVipRecords(MemberCardRequest request);

    /**
     * 根据会员卡ID查询会员卡权益使用明细信息
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/vip-card/use-info")
    MemberInterestResponse<List<MemberCardUseInfo>> queryMemberCardUsedInfo(MemberCardUsedRequest request);

    /**
     * 查询权益卡明细信息（含权益使用信息）
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/rights-pack/user-pack-info")
    MemberInterestResponse<RightCardPackInfo> loadRightCardInfo(RightPackRequest request);

    /**
     * 查询会员卡加黑
     * 接口文档 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014670
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/vip-black-list/list")
    MemberInterestResponse<VipBlackRecords> queryVipBlack(VipBlackRequest request);

    /**
     * 会员卡加黑
     * 接口文档 https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014670
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/vip-black-list/edit")
    MemberInterestResponse<VipBlackRecords> vipBlack(VipBlackRequest request);
}
