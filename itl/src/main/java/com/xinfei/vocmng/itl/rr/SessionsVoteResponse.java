/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ SessionsVoteResponse
 */
@Data
public class SessionsVoteResponse {
    private Integer status;
    private String message;
    private Integer size;
    private Integer total;
    @JsonProperty("total_pages")
    private Integer totalPages;
    private List<SessionsVote> item;
}