/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.CstStartClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class CstStartService {
    @Resource
    private CstStartClient cstStartClient;

    public RefundRecordResult getBankFlow(RefundRecordQueryReq request) {

        RefundRecordResponse<RefundRecordResult> response = null;
        String msg = "CstStartClient.queryOfflineRefundLists:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = cstStartClient.queryOverPayFlowTransLists(request);
            log.info(LogUtil.clientLog("CstStartClient", "queryOfflineRefundLists", request, response));
            if (Objects.isNull(response) || !response.getSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getResult();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CstStartClient", "queryOfflineRefundLists", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean offlineRefundApply(OverRefundApplyReq request) {
        request.setUa("vocmng");
        RefundRecordResponse<Boolean> response = null;
        String msg = "CstStartClient.overRefundApply:";
        try {
            response = cstStartClient.overRefundApply(request);
            log.info(LogUtil.clientLog("CstStartClient", "overRefundApply", request, response));
            if (Objects.isNull(response) || !response.getSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getResult();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CstStartClient", "overRefundApply", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean refundApplyCancel(RefundApplyCancelReq request) {
        RefundRecordResponse<Boolean> response = null;
        request.setUa("vocmng");
        String msg = "CstStartClient.refundApplyCancel:";
        try {
            response = cstStartClient.refundApplyCancel(request);
            log.info(LogUtil.clientLog("CstStartClient", "refundApplyCancel", request, response));
            if (Objects.isNull(response) || !response.getSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getResult();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CstStartClient", "refundApplyCancel", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<RefundOrderRecordRes> queryRefundOrderRecordListByTransNo(String transNo, String channelCode) {
        RefundQueryConditionReq request = new RefundQueryConditionReq();
        request.setTransNo(transNo);
        request.setChannelCode(channelCode);
        RefundRecordResponse<List<RefundOrderRecordRes>> response = null;
        String msg = "CstStartClient.queryRefundOrderRecordListByTransNo:";
        try {
            response = cstStartClient.queryRefundOrderRecordListByTransNo(request);
            log.info(LogUtil.clientLog("CstStartClient", "queryRefundOrderRecordListByTransNo", request, response));
            if (Objects.isNull(response) || !response.getSuc()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getResult();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CstStartClient", "queryRefundOrderRecordListByTransNo", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}