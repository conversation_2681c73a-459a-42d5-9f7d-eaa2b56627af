package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.LogOffQueryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;


/**
 * <AUTHOR> 2024/7/4 14:00
 */
@FeignClient(name = FeignConstants.APP_PROD, contextId = FeignConstants.APP_PROD, path = "/")
public interface AppProdClient {

    /**
     * 客户注销检查
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/logoff/eligible-check")
    LogOffResponse logOffCheck(LogOffCheckRequest request);

    /**
     * 客户注销
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/logoff/single")
    LogOffResponse logOff(LogOffRequest request);

    /**
     * 客户撤销注销
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/logoff/cancel")
    LogOffResponse logOffCancel(LogOffCancelRequest request);


    /**
     * 客户撤销查询
     *
     * @param request
     * @return
     */
    @PostMapping("/rpc/logoff/query-all")
    LogOffQueryResponse<LogOffQueryDataInfo<List<LogOffQueryDto>>> logOffQuery(LogOffQueryRequest request);
}


