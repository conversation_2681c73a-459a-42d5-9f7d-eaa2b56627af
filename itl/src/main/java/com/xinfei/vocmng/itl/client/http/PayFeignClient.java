package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.BankListResponse;
import com.xinfei.vocmng.itl.rr.UaRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@FeignClient(name = FeignConstants.PAY, contextId = FeignConstants.PAY, path = "/")
public interface PayFeignClient {

    @PostMapping("/bank/bank-list")
    BankListResponse bankList(UaRequest ua);


}
