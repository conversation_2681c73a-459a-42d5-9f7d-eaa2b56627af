/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.service;

import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version $ LendQueryClientService, v 0.1 2024-05-27 20:24 junjie.yan Exp $
 */
@Service
public class LendQueryClientService {

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    public ManageOrderDetailDTO getOrderNoByLoanNo(String loanNo) {
        ManageOrderDetailDTO manageOrderDetailDTO = new ManageOrderDetailDTO();
        if (StringUtils.isEmpty(loanNo)) {
            return manageOrderDetailDTO;
        }

        ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
        manageOrderListRequest.setLoanNos(Collections.singletonList(loanNo));
        com.xinfei.lendtrade.facade.rr.dto.Page<ManageOrderDetailDTO> manageOrderDetailDTOPage = lendQueryFacadeClient.getOrderList(manageOrderListRequest);
        if (CollectionUtils.isNotEmpty(manageOrderDetailDTOPage.getPageList()) && manageOrderDetailDTOPage.getPageList().get(0) != null) {
            manageOrderDetailDTO = manageOrderDetailDTOPage.getPageList().get(0);
        }

        return manageOrderDetailDTO;
    }

    @NotNull
    public ManageOrderDetailDTO getLoanNoByOrderNo(String orderNo) {
        ManageOrderDetailDTO manageOrderDetailDTO = new ManageOrderDetailDTO();
        if (StringUtils.isEmpty(orderNo)) {
            return manageOrderDetailDTO;
        }

        ManageOrderDetailRequest manageOrderDetailRequest = new ManageOrderDetailRequest();
        manageOrderDetailRequest.setOrderNo(orderNo);
        ManageOrderDetailDTO response = lendQueryFacadeClient.getOrderDetail(manageOrderDetailRequest);
        if (response == null) {
            return manageOrderDetailDTO;
        }
        return response;
    }

}