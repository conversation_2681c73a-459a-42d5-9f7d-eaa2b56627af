package com.xinfei.vocmng.itl.constants;

/**
 * feign配置相关的常量信息
 * <AUTHOR>
 * @since 2023/12/18
 */
public class FeignConstants {
    /** 本系统名称 */
    public static final String SERVICE_NAME = "vocmng";
    /** 合同系统应用名称 */
    public static final String CONTRACT = "contract-service";
    /** CIS系统应用名称 */
    public static final String CIS = "cis-query-service";
    /** 信贷系统应用名称 */
    public static final String CREDIT = "credit-service";
    /** 会员全系系统应用名称 */
    public static final String MEMBER_INTEREST = "member-interest";
    /** 借据账单系统应用名称 */
    public static final String LCS = "lcs";

    /** gateway_app_config表数据 */
    public static final String GATEWAY = "gateway";

    public static final String UNION = "union";

    /** dataCenter */
    public static final String DATACENTER = "data-center";

    /** 海鹰系统 */
    public static final String CST = "cst-start";

    /** udesk */
    public static final String U_DESK = "udesk";

    /** udesk */
    public static final String KM_DESK = "km-udesk";

    /** udesk cc api */
    public static final String CC_DESK = "cc-udesk";

    /** 银行卡信息 */
    public static final String BANK = "bank-core-service";

    /** 产品信息 */
    public static final String PRODUCT = "product";

    /** 账户系统 */
    public static final String AMS = "ams";

    /** 决策引擎 */
    public static final String ENGINE = "engine";

    /** 账户系统 */
    public static final String RCS_PROVIDER = "rcs-provider";

    /** 支付系统 */
    public static final String PAY = "paycenter";

    /** 飞享会员系统 */
    public static final String VIP = "vipcore";

    /** 变量中心 */
    public static final String FEATURE_PLATFORM = "feature-platform";

    /** 特征平台 */
    public static final String FEATURE_QUERY = "feature-query";

    /* 资金系统 */
    /**
     * https://qa1-api.testxinfei.cn/fund
     * https://dev-api.testxinfei.cn/fund
     * https://sit-api.testxinfei.cn/fund
     * https://api.xinfei.io/fund
     */
    public static final String FUND_SERVICE_NAME = "fund";

    /** 短信中心服务 */
    public static final String SMS_CENTER = "sms-center";

    /** 钉钉消息（dev、qa1、sit共用一个qa1地址） */
    public static final String SMS_ROBOT = "sms-robot";

    /** 呼叫中心服务 */
    public static final String CALL_CENTER = "call-center";

    /** 实时质检服务 */
    public static final String QUALITY_INSPECTION = "quality-inspection";

    /** 工单系统 */
    public static final String WORK_ORDER = "work-order";

    /** appProd  */
    public static final String APP_PROD = "appprod";

    /** api导流服务 */
    public static final String PLATFORM_SERVICE_API = "platform-service-api";

    /** nPay服务 */
    public static final String N_PAY = "npay-api";

    /** 通用服务成功响应码 */
    public static final Integer SUCCESS_CODE = 1;

    /** 会员权益成功响应码 */
    public static final String MEMBER_INTEREST_SUCCESS_CODE = "1";

    /** 合同服务成功响应码 */
    public static final Integer CONTRACT_SUCCESS_CODE = 1;

    /** 呼叫中心成功响应码 */
    public static final String CALL_CENTER_SUCCESS_CODE = "S";

    /** 工单系统成功响应码 */
    public static final String WORK_ORDER_SUCCESS_CODE = "000000";

    /** 优惠券服务 */
    public static final String COUPON = "coupon";

    /** 优惠券系统成功响应码 */
    public static final Integer COUPON_SUCCESS_CODE = 1;

    /** 海尔消金客诉转接系统 */
    public static final String HAIER_COMPLAINT = "haier-complaint";

    /** 海尔消金成功响应码 */
    public static final String HAIER_COMPLAINT_SUCCESS_CODE = "00000";
}
