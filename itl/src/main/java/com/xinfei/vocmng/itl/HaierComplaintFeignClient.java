package com.xinfei.vocmng.itl;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.haier.HaierComplaintRequest;
import com.xinfei.vocmng.itl.rr.haier.HaierComplaintResponse;
import com.xinfei.vocmng.itl.rr.haier.QueryTransferCallResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 海尔消金客诉转接接口客户端
 * 
 * <AUTHOR>
 * @version $ HaierComplaintFeignClient, v 0.1 2025/08/08 HaierComplaintFeignClient Exp $
 */
@FeignClient(name = FeignConstants.HAIER_COMPLAINT, contextId = FeignConstants.HAIER_COMPLAINT + ".HaierComplaintFeignClient", path = "/")
public interface HaierComplaintFeignClient {

    /**
     * 查询客诉转接来电信息接口
     * 根据海消通话id、合作机构编号查询消金客诉转接来电记录
     *
     * @param serNo 请求流水号
     * @param tradeTime 时间
     * @param secretKey 准入秘钥
     * @param requestBody 请求体
     * @return 来电信息
     */
    @PostMapping("/api/ccpp/core/complaint/transfer/call/query/by/callId")
    HaierComplaintResponse<QueryTransferCallResponse> queryTransferCallInfo(
            @RequestHeader("serNo") String serNo,
            @RequestHeader("tradeTime") String tradeTime,
            @RequestHeader("secretKey") String secretKey,
            @RequestBody HaierComplaintRequest.HaierRequestBody requestBody);

    /**
     * 新增跟进记录接口
     * 新增客诉转接跟进记录接口
     *
     * @param serNo 请求流水号
     * @param tradeTime 时间
     * @param secretKey 准入秘钥
     * @param requestBody 请求体
     * @return 操作结果
     */
    @PostMapping("/api/ccpp/core/complaint/transfer/process/record/add")
    HaierComplaintResponse<Object> addProcessRecord(
            @RequestHeader("serNo") String serNo,
            @RequestHeader("tradeTime") String tradeTime,
            @RequestHeader("secretKey") String secretKey,
            @RequestBody HaierComplaintRequest.HaierRequestBody requestBody);
}
