package com.xinfei.vocmng.itl.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.SecretKeyFactory;
import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.util.UUID;

/**
 * 海尔消金接口AES加密工具类
 *
 * <AUTHOR>
 * @version $ HaierAesUtils, v 0.1 2025/08/08 HaierAesUtils Exp $
 */
@Slf4j
public class HaierAesUtils {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256";
    private static final int KEY_LENGTH = 128;
    private static final int ITERATION_COUNT = 1000;
    /**
     * 获取加盐后的秘钥
     * 
     * @param password 密码
     * @param salt 盐值
     * @return 返回密码加盐后的key, 长度16
     */
    public static byte[] getKeyByteArr(String password, String salt) {
        try {
            byte[] hexByte = Hex.decodeHex(salt);
            KeySpec spec = new PBEKeySpec(password.toCharArray(), hexByte, ITERATION_COUNT, KEY_LENGTH);
            SecretKeyFactory factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM);
            return factory.generateSecret(spec).getEncoded();
        } catch (Exception e) {
            log.error("生成密钥失败: ", e);
            throw new RuntimeException("生成密钥失败", e);
        }
    }
    
    /**
     * 对明文数据进行aes加密, 将使用传入的password同时作为iv值
     * 
     * @param plaintext 需加密的明文
     * @param password 密码明文
     * @param salt 盐值
     * @return 加密数据
     */
    public static String encrypt(String plaintext, String password, String salt) {
        try {
            byte[] keyByteArr = getKeyByteArr(password, salt);
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyByteArr, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(keyByteArr);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            log.error("AES加密失败: ", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }
    
    /**
     * 对密文数据进行aes解密
     * 
     * @param encryptionContent aes加密的密文数据
     * @param password 密码
     * @param salt 盐值
     * @return 解密数据
     */
    public static String decrypt(String encryptionContent, String password, String salt) {
        try {
            byte[] keyByteArr = getKeyByteArr(password, salt);
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyByteArr, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(keyByteArr);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            
            byte[] decryptedBytes = cipher.doFinal(Base64.decodeBase64(encryptionContent));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败: ", e);
            throw new RuntimeException("AES解密失败", e);
        }
    }
    
    /**
     * 生成UUID密码（去掉横线）
     * 
     * @return UUID密码
     */
    public static String generatePassword() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
