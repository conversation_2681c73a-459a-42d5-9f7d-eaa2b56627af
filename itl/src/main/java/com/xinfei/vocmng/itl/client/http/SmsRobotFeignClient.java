package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.BaseUserCenterRequest;
import com.xinfei.vocmng.itl.rr.BaseUserCenterResponse;
import com.xinfei.vocmng.itl.rr.dto.DingMsgDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 短信中心相关接口
 *
 * <AUTHOR>
 * @version $ SmsCenterFeignClient, v 0.1 2023/12/27 09:41 qu.lu Exp $
 */
@FeignClient(name = FeignConstants.SMS_ROBOT, contextId = FeignConstants.SMS_ROBOT + ".FeinClient", path = "/")
public interface SmsRobotFeignClient {

    /**
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001005897">发送机器人消息</a>
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001005973@toc11">钉钉机器人</a>
     *
     * @param request
     * @return
     */
    @PostMapping("/robot/send-msg")
    BaseUserCenterResponse<String> sendRobotMsg(@RequestBody BaseUserCenterRequest<DingMsgDto> request);
}
