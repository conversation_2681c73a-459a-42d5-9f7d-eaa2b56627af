/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ ImCustomerDetailsResponse, v 0.1 2025-03-10 11:28 pengming.liu Exp $
 */
@Data
public class ImCustomerDetailsResponse {
    /** 客户id */
    private Long id;

    /** 客户名称 */
    @JsonProperty("nick_name")
    private String nickName;

    /** 客户等级 */
    private String level;

    /** 描述 */
    private String description;

    /** 负责客服id */
    @JsonProperty("owner_id")
    private Integer ownerId;

    /** 负责客服组id */
    @JsonProperty("owner_group_id")
    private Integer ownerGroupId;

    /** 自定义字段 */
    @JsonProperty("custom_fields")
    private String customFields;

    /** 外部唯一标识 */
    @JsonProperty("open_api_token")
    private String openApiToken;

    /** 客户公司id */
    @JsonProperty("organization_id")
    private Integer organization_id;

    /** 是否被加入黑名单 */
    @JsonProperty("is_blocked")
    private Boolean isBlocked;

    /** 客户web标识 */
    @JsonProperty("web_token")
    private String webToken;

    /** 客户sdk标识 */
    @JsonProperty("sdk_token")
    private Integer sdkToken;

    /** 标签列表 */
    @JsonProperty("tags")
    private List<Tag> tags;

    /** 背景色标签列表 */
    @JsonProperty("rich_tags")
    private String richTags;

    /** 首次联系时间 */
    @JsonProperty("first_contact_at")
    private LocalDateTime firstContactAt;

    /** 最后联系时间 */
    @JsonProperty("last_contact_at")
    private LocalDateTime lastContactAt;

    /** 首次电话联系时间 */
    @JsonProperty("first_contact_at_via_phone")
    private LocalDateTime firstContactAtViaPhone;

    /** 最后电话联系时间 */
    @JsonProperty("last_contact_at_via_phone")
    private LocalDateTime lastContactAtViaPhone;

    /** 首次在线客服联系时间 */
    @JsonProperty("first_contact_at_via_im")
    private LocalDateTime firstContactAtViaIm;

    /** 最后在线客服联系时间 */
    @JsonProperty("last_contact_at_via_im")
    private LocalDateTime lastContactAtViaIm;

    /** 主邮箱 */
    private String email;

    /** 其他邮箱列表 */
    @JsonProperty("other_emails")
    private String otherEmails;

    /** 联系电话列表 */
    @JsonProperty("cellphones")
    private String cellphones;

    /** 创建渠道中文名称 */
    @JsonProperty("platform")
    private String platform;

    /** 客户来源中文名称 */
    @JsonProperty("source_channel")
    private String sourceChannel;

    /** 微信信息 */
    private String weixins;

    /** weixin_minis */
    @JsonProperty("weixin_minis")
    private String weixinMinis;

    /** weixin_works */
    @JsonProperty("weixin_works")
    private String weixinWorks;

    /** weixin_kfs */
    @JsonProperty("weixin_kfs")
    private String weixinKfs;

    /** 语言偏好 */
    private String lang;

    /** 创建时间 */
    @JsonProperty("created_at")
    private String createdAt;

    /** 更新时间 */
    @JsonProperty("updated_at")
    private String updatedAt;

    @Data
    public static class Tag{
        private Long id;
        private String name;
        @JsonProperty("company_id")
        private Long companyId;
    }
}