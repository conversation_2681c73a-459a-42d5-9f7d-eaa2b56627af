/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version $ BaseUserCenterRequest, v 0.1 2023/11/1 15:03 ****.**** Exp $
 */
@Setter
@Getter
public class BaseUserCenterRequest<Args> {
	@JsonProperty("ua")
	private String ua;
	
	@JsonProperty("args")
	private Args args;
	
	@JsonProperty("sign")
	private String sign;
	
	@JsonProperty("timestamp")
	private Long timestamp = System.currentTimeMillis() / 1000L;
	
	public void formatRequest(String ua, String signKey, Args args) {
		this.args = args;
		this.ua = ua;
		this.sign = signKey;
		this.timestamp = System.currentTimeMillis();
	}
	
}