package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * UDesk / customers/export 接口返回体
 *
 * <AUTHOR>
 * @version $ ExportCustomerResponse, v 0.1 2025/4/29 $
 */
@Data
public class ExportCustomerResponse {

    /** 执行结果码，1000 表示成功 */
    private int code;

    /** 下一批数据的获取 id（滚动游标，1 分钟失效） */
    @JsonProperty("scroll_id")
    private String scrollId;

    /** 满足条件的客户总数 */
    private Long total;

    /** 客户列表（单批最多 1000 条） */
    private List<Customer> customers;
}
