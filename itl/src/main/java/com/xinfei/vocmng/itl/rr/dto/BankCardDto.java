/**
 * Copyright 2023 bejson.com
 */
package com.xinfei.vocmng.itl.rr.dto;

import lombok.Data;

import java.util.Date;

/**
 * Auto-generated: 2023-12-25 15:50:13
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
public class BankCardDto {
    /**
     * 绑卡id
     */
    private Long cardId;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行卡号
     */
    private String cardNo;
    /**
     * 银行预留手机号
     */
    private String reservedMobileNo;
    /**
     * 1-默认卡 2-绑定 3-解绑
     */
    private Integer status;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 签约状态，00-未签约，01-已签约
     */
    private String signed;
    /**
     * bankcode
     */
    private String bankCode;
    /**
     * ccBankid
     */
    private String ccBankId;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * app
     */
    private String app;
    /**
     * 绑定源
     */
    private String sourceChannel;
    /**
     * 绑定时间
     */
    private String bindTime;
}