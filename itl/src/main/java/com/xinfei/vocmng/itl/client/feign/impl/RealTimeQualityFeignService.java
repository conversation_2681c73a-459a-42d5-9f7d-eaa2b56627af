package com.xinfei.vocmng.itl.client.feign.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.rr.BathResponseData;
import com.xinfei.vocmng.itl.rr.OrgData;
import com.xinfei.vocmng.itl.rr.QualityInspectionRequest;
import com.xinfei.vocmng.itl.rr.QualityInspectionResponse;
import com.xinfei.vocmng.itl.rr.StaffData;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.vqcprod.facade.api.RealTimeQualityFacade;
import com.xinfei.vqcprod.facade.model.dto.RcraiOrgDTO;
import com.xinfei.vqcprod.facade.model.dto.RcraiStaffDTO;
import com.xinfei.vqcprod.facade.model.rr.VqcRequest;
import com.xinfei.vqcprod.facade.model.rr.VqcResponse;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ RealTimeQualityFeignService, v 0.1 2025/3/12 16:15 shaohui.chen Exp $
 */
@Slf4j
@Component
public class RealTimeQualityFeignService {

    @Resource
    private RealTimeQualityFacade realTimeQualityFacade;

    private final static String sysCode = "customer_service_system";

    /**
     * 批量同步组织信息
     */
    public void orgBatch(List<OrgData> orgDataList) {
        VqcRequest<List<RcraiOrgDTO>> request = new VqcRequest<>();
        request.setSysCode(sysCode);
        request.setData(JsonUtil.parseJson(JsonUtil.toJson(orgDataList), new TypeReference<List<RcraiOrgDTO>>() {
        }));
        VqcResponse<Boolean> response = null;
        try {
            response = realTimeQualityFacade.orgBatch(request);
            log.info(LogUtil.clientLog("RealTimeQualityFacade", "orgBath", request, response));
            if (Objects.isNull(response) || !response.isSuc()) {
                log.info("RealTimeQualityFeignService#orgBatch,orgBatch fail,response:{}", JsonUtil.toJson(response));
                throw new Exception(Objects.nonNull(response) && Objects.nonNull(response.getErrorContext()) ?
                        response.getErrorContext().getErrDesc() : "失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RealTimeQualityFacade", "orgBath", request, response, e.getMessage()), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 批量客服信息
     */
    public void staffBath(List<StaffData> staffDataList) {
        VqcRequest<List<RcraiStaffDTO>> request = new VqcRequest<>();
        request.setSysCode(sysCode);
        request.setData(JsonUtil.parseJson(JsonUtil.toJson(staffDataList), new TypeReference<List<RcraiStaffDTO>>() {
        }));
        VqcResponse<Boolean> response = null;
        try {
            response = realTimeQualityFacade.staffBatch(request);
            log.info(LogUtil.clientLog("RealTimeQualityFacade", "staffBatch", request, response));
            if (Objects.isNull(response) || !response.isSuc()) {
                log.info("RealTimeQualityFeignService#staffBath,staffBath fail,response:{}", JsonUtil.toJson(response));
                throw new Exception(Objects.nonNull(response) && Objects.nonNull(response.getErrorContext()) ?
                        response.getErrorContext().getErrDesc() : "失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RealTimeQualityFacade", "staffBath", request, response, e.getMessage()), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, ErrorLevelsEnum.ERROR);
        }
    }
}
