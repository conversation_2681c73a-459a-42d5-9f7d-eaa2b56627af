/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.IgnoreException;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.util.CisCommonAttributes;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureBatchEncryptDTO;
import com.xyf.cis.dto.SecureDecryptRequest;
import com.xyf.cis.dto.SecureEncryptDTO;
import com.xyf.cis.dto.SecureEncryptRequest;
import com.xyf.cis.query.facade.CustomerQueryFacade;
import com.xyf.cis.query.facade.SearchFacade;
import com.xyf.cis.query.facade.UserQueryFacade;
import com.xyf.cis.query.facade.dto.request.*;
import com.xyf.cis.query.facade.dto.standard.request.QueryUserNoByMobileAndAppRequest;
import com.xyf.cis.query.facade.dto.standard.request.UserSearchRequest;
import com.xyf.cis.query.facade.dto.standard.response.*;
import com.xyf.user.facade.common.model.BaseResponse;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class CisFacadeClientImpl implements CisFacadeClient {

    @Resource
    private SearchFacade searchFacade;

    @Resource
    private CustomerQueryFacade customerQueryFacade;

    @Resource
    private UserQueryFacade userQueryFacade;

    @Resource
    private SecureClient secureClient;
    public static final String MOBILE_PATTERN = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";//手机号校验

    @Override
    public QueryUserNoByMobilMd5Response queryUserNoByMobilMd5(String mobileMd5) {
        BaseResponse<QueryUserNoByMobilMd5Response> response = null;
        String msg = "UserQueryFacade.queryUserNoByMobilMd5V2:";
        QueryUserNoByMobilMd5Request request = new QueryUserNoByMobilMd5Request();
        request.setMobileMd5(mobileMd5);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryUserNoByMobilMd5V2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryUserNoByMobilMd5V2", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryUserNoByMobilMd5V2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public PageResult<UserSearchDTO> queryUserList(String mobileNo, String custNo, String userNo, Integer pageNum, Integer pageSize) {
        BaseResponse<PageResult<UserSearchDTO>> response = null;
        UserSearchRequest request = new UserSearchRequest();
        if(StringUtils.isNotBlank(mobileNo)){
            request.setMobileNo(mobileNo);
        }
        request.setCustNo(custNo);
        if(StringUtils.isNotBlank(userNo)){
            request.setUserNo(Long.parseLong(userNo));
        }
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "SearchFacade.userList:";
        try {
            response = searchFacade.userList(request);
            log.info(LogUtil.clientLog("SearchFacade", "userList", request, response));
            if (response == null || response.isNotSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SearchFacade", "userList", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public List<UserNoDTO> queryUserNoByMobile(String mobile) {
        BaseResponse<List<UserNoDTO>> response = null;
        String msg = "UserQueryFacade.queryUserNoByMobileV2:";
        QueryUserNoByMobileRequest request = new QueryUserNoByMobileRequest();
        request.setMobile(mobile);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryUserNoByMobileV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryUserNoByMobileV2", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryUserNoByMobileV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public ThreeElementsDTO queryThreeElementsByUserNo(Long userNo) {
        BaseResponse<ThreeElementsDTO> response = null;
        String msg = "UserQueryFacade.queryThreeElementsByUserNoV2:";
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryThreeElementsByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryThreeElementsByUserNoV2", request, response));
            if (response == null || response.isNotSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryThreeElementsByUserNoV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public CustNoDTO queryCustNoByIdNo(String idCardNumber) {
        if (Objects.isNull(idCardNumber)) {
            return null;
        }
        BaseResponse<CustNoDTO> response = new BaseResponse<>();
        String msg = "CustomerQueryFacade.queryCustNoByIdNoV2:";
        QueryCustNoByIdNoRequest request = new QueryCustNoByIdNoRequest();
        request.setIdNo(idCardNumber);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = customerQueryFacade.queryCustNoByIdNoV2(request);
            log.info(LogUtil.clientLog("CustomerQueryFacade", "queryCustNoByIdNoV2", request, response));
            if (Objects.isNull(response) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CustomerQueryFacade", "queryCustNoByIdNoV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public UserNoDTO getUserNoByMobileAndApp(String mobile, String app) {
        if (mobile == null || app == null) {
            return null;
        }
        if (!mobile.matches(MOBILE_PATTERN)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "手机号格式不合法");
        }

        BaseResponse<UserNoDTO> response = null;
        QueryUserNoByMobileAndAppRequest request = new QueryUserNoByMobileAndAppRequest();
        request.setMobile(mobile);
        request.setApp(app);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "UserQueryFacade.queryUserNoByMobileAndAppV2:";
        try {
            response = userQueryFacade.queryUserNoByMobileAndAppV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryUserNoByMobileAndAppV2", request, response));
            if (Objects.isNull(response) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryUserNoByMobileAndAppV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public IdNoDTO queryIdNoByCustNo(String custNo) {
        BaseResponse<IdNoDTO> response = new BaseResponse<>();
        String msg = "CustomerQueryFacade.queryIdNoByCustNo:";
        QueryIdNoByCustNoRequest request = new QueryIdNoByCustNoRequest();
        try {
            if (Objects.isNull(custNo)) {
                return null;
            }
            request.setCustNo(custNo);
            CisCommonAttributes.setCommonAttributes(request);
            response = customerQueryFacade.queryIdNoByCustNoV2(request);
            log.info(LogUtil.clientLog("CustomerQueryFacade", "queryIdNoByCustNoV2", request, response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CustomerQueryFacade", "queryIdNoByCustNoV2", custNo, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public LastLoginDTO queryLastLoginByUserNo(Long userNo) {
        if (Objects.isNull(userNo)) {
            return null;
        }
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        BaseResponse<LastLoginDTO> response = null;
        String msg = "UserQueryFacade.queryLastLoginByUserNoV2:";
        try {
            response = userQueryFacade.queryLastLoginByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryLastLoginByUserNoV2", request, response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryLastLoginByUserNoV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 根据userno查询custno
     */
    @Override
    public CustNoDTO queryCustNoByUserNo(Long userNo) {
        if (Objects.isNull(userNo)) {
            return null;
        }
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        BaseResponse<CustNoDTO> response = null;
        String msg = "UserQueryFacade.queryCustNoByUserNoV2:";
        try {
            response = userQueryFacade.queryCustNoByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryCustNoByUserNoV2", request, response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryCustNoByUserNoV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public MobileDTO queryMobileByUserNo(Long userNo) {
        if (Objects.isNull(userNo)) {
            return null;
        }
        BaseResponse<MobileDTO> response = null;
        String msg = "UserQueryFacade.queryMobileByUserNoV2:";
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryMobileByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryMobileByUserNoV2", request, response));
            if (Objects.isNull(response) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData() == null ? new MobileDTO() : response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryMobileByUserNoV2", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<SecureEncryptDTO> batchEncryptLocal(List<String> mobiles) {
        List<SecureEncryptDTO> response = null;
        String msg = "SecureClient.batchEncryptLocal:";
        try {
            response = secureClient.batchEncrypt(mobiles);
            log.info(LogUtil.clientLog("SecureClient", "batchEncryptLocal", mobiles, response));
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SecureClient", "batchEncryptLocal", mobiles, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<SecureEncryptDTO> batchEncryptByField(String field, List<String> plainTexts) {
        SecureEncryptRequest secureEncryptRequest = new SecureEncryptRequest();
        secureEncryptRequest.setField(field);
        secureEncryptRequest.setPlainTexts(plainTexts);
        SecureBatchEncryptDTO response = null;
        String msg = "SecureClient.batchEncrypt:";
        try {
            response = secureClient.batchEncrypt(secureEncryptRequest);
            log.info(LogUtil.clientLog("SecureClient", "batchEncrypt", secureEncryptRequest, response));
            return response.getCiphers();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SecureClient", "batchEncrypt", secureEncryptRequest, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<SecureEncryptDTO> batchDecrypt(List<String> cipherTexts, String field) {
        com.xyf.cis.dto.SecureBatchEncryptDTO response = null;
        SecureDecryptRequest request = new SecureDecryptRequest();
        request.setCipherTexts(cipherTexts);
        request.setField(field);
        String msg = "SecureClient.batchDecrypt:";
        try {
            response = secureClient.batchDecrypt(request);
            log.info(LogUtil.clientLog("SecureClient", "batchDecrypt", cipherTexts, response));
            if (Objects.isNull(response)) {
                msg += "response is null";
                throw new Exception(msg);
            }
            return response.getCiphers();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SecureClient", "batchDecrypt", cipherTexts, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public Long queryIdByMobile(String mobile) {
        BaseResponse<Long> response = null;
        String msg = "UserQueryFacade.queryIdByMobileV2:";
        QueryMobileRequest request = new QueryMobileRequest();
        request.setMobile(mobile);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryIdByMobileV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryIdByMobileV2", request, response));
            if (response == null || response.isNotSuccess() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryIdByMobileV2", request, response, msg), e);
            return null;
        }
    }

    @Override
    public String queryMobileById(Long userId) {
        BaseResponse<String> response = null;
        String msg = "UserQueryFacade.queryMobileByIdV2:";
        QueryMobileByIdRequest request = new QueryMobileByIdRequest();
        request.setUserId(userId);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryMobileByIdV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryMobileByIdV2", request, response));
            if (response == null || response.isNotSuccess() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryMobileByIdV2", request, response, msg), e);
            return null;
        }
    }
}