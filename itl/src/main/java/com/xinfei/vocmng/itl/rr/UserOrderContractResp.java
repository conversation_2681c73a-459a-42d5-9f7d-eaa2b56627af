/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ UserOrderContractResp, v 0.1 2024/8/26 11:39 wancheng.qu Exp $
 */
@Data
public class UserOrderContractResp implements Serializable {
   private String id;
   @JsonProperty("contract_id")
   private String contractId ;
   private String name;
   private String mobile;
   @JsonProperty("id_card_number")
   private String idCardNumber;
   @JsonProperty("short_name")
   private String shortName;
   @JsonProperty("created_time")
   private String createdTime;
   private String app;
}