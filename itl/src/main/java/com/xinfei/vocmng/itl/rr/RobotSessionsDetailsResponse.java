/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ RobotSessionsDetailsResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class RobotSessionsDetailsResponse {

    /** 对应接口返回值里的 id */
    @JsonProperty("id")
    private Long sessionDetailsId;

    /** 用户类型 */
    private Long userType;

    /** log类型 */
    private Long logType;

    /** 机器人id */
    private Long robotId;

    /** 创建用户id*/
    private Long createUserId;

    /** 记录内容 */
    private String content;

    /** 对话ID */
    private Long sessionId;

    /** 记录创建时间 */
    @JsonProperty("createTime")
    private LocalDateTime createTime;

    /** 引导语 */
    private String leadingWord;

    /** 答案 */
    private String extraAnswerContent;

    /** tags */
    private String tags;
}