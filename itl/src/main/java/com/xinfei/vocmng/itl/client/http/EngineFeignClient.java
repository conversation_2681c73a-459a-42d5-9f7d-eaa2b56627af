/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl.client.http;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import com.xinfei.vocmng.itl.rr.DecisionRequest;
import com.xinfei.vocmng.itl.rr.DecisionResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ AmsFeignClient, v 0.1 2024/8/12 11:28 you.zhang Exp $
 */
@FeignClient(name = FeignConstants.ENGINE, contextId = FeignConstants.ENGINE, path = "/")
public interface EngineFeignClient {

    @PostMapping("/engine/decision")
    DecisionResponse decision(DecisionRequest req, @RequestHeader Map<String, String> headers);

}
