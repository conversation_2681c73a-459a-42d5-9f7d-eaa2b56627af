package com.xinfei.vocmng.itl.model.header;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 呼叫中心统一签名header信息
 *
 * <AUTHOR>
 * @version $ CallCenterHeader, v 0.1 2023/12/27 22:25 qu.lu Exp $
 */
@Data
public class CallCenterHeader extends BaseHeader{
    /** 业务系统标识，由中台分配 */
    @JsonProperty(value = "app_key")
    private String appKey;
    /** 签名:get请求：将body参数先按照key进行排序，然后变成 key=value&key=value的形式，加上密码做MD5
     post请求：将body参数拼接上&app_secret=秘钥 */
    private String sign;
    /** 发送时间	 */
    private Long timestamp;
}
