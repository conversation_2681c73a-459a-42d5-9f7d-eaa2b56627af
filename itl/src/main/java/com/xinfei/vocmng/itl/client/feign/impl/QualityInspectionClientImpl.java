/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.QualityInspectionClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ QualityInspectionClientImpl, v 0.1 2024-10-15 16:42 junjie.yan Exp $
 */
@Slf4j
@Component
public class QualityInspectionClientImpl {

    @Value("${ak}")
    private String ak;

    @Value("${secret}")
    private String secret;

    @Resource
    private QualityInspectionClient qualityInspectionClient;

    private String getAuthorization() {
        //app:<key>:<secret>
        String appKey = "app:" + ak + ":" + secret;
        return "Bearer " + Base64.getEncoder().encodeToString(appKey.getBytes());
    }

    public List<BathResponseData> orgBath(List<OrgData> orgDataList) {
        QualityInspectionRequest<List<OrgData>> request = new QualityInspectionRequest<>();
        request.setData(orgDataList);

        QualityInspectionResponse<List<BathResponseData>> response = null;
        String msg = "QualityInspectionClient.orgBath:";
        try {
            response = qualityInspectionClient.orgBath(request, getAuthorization());
            log.info(LogUtil.clientLog("QualityInspectionClient", "orgBath", request, response));
            if (Objects.isNull(response) || response.getCode() != 0 || !response.getSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("QualityInspectionClient", "orgBath", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<BathResponseData> staffBath(List<StaffData> staffDataList) {
        QualityInspectionRequest<List<StaffData>> request = new QualityInspectionRequest<>();
        request.setData(staffDataList);

        QualityInspectionResponse<List<BathResponseData>> response = null;
        String msg = "QualityInspectionClient.staffBath:";
        try {
            response = qualityInspectionClient.staffBath(request, getAuthorization());
            log.info(LogUtil.clientLog("QualityInspectionClient", "staffBath", staffDataList.size(), response));
            if (Objects.isNull(response) || response.getCode() != 0 || !response.getSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("QualityInspectionClient", "staffBath", staffDataList.size(), response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public String getToken(String staffId) {
        QualityInspectionResponse<List<String>> response = null;
        String msg = "QualityInspectionClient.token:";
        try {
            response = qualityInspectionClient.token(staffId, getAuthorization());
            log.info(LogUtil.clientLog("QualityInspectionClient", "token", staffId, response));
            if (Objects.isNull(response) || response.getCode() != 0 || !response.getSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }

            if (CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData().get(0);
            } else {
                return "";
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("QualityInspectionClient", "token", staffId, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public SessionsMeta sessions(String callId, String staffId, String customerId) {
        SessionsRequest request = new SessionsRequest();
        request.setId(callId);
        request.setStaffId(staffId);
        request.setMode("call");
        request.setCategory("默认分类");
        request.setCustomerId(customerId);
        SessionsResponse response = null;
        String msg = "QualityInspectionClient.sessions:";
        try {
            response = qualityInspectionClient.sessions(request, getAuthorization());
            log.info(LogUtil.clientLog("QualityInspectionClient", "sessions", request, response));
            if (Objects.isNull(response) || response.getMeta() == null || response.getMeta().getCode() != 0) {
                if (Objects.isNull(response)) {
                    throw new Exception(msg);
                } else if (response.getMeta() == null) {
                    msg += "meta is null";
                } else {
                    msg = response.getMeta().getMessage();
                }
                throw new Exception(msg);
            }

            return response.getMeta();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("QualityInspectionClient", "sessions", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public SessionsMeta signals(String callId, String name) {
        SignalsRequest request = new SignalsRequest();
        request.setName(name);
        request.setTimestamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        SessionsResponse response = null;
        String msg = "QualityInspectionClient.signals:";
        try {
            response = qualityInspectionClient.signals(callId, request, getAuthorization());
            log.info(LogUtil.clientLog("QualityInspectionClient", "signals", request, response));
            if (Objects.isNull(response) || response.getMeta() == null || response.getMeta().getCode() != 0) {
                if (Objects.isNull(response)) {
                    throw new Exception(msg);
                } else if (response.getMeta() == null) {
                    msg += "meta is null";
                } else {
                    msg = response.getMeta().getMessage();
                }
                throw new Exception(msg);
            }

            return response.getMeta();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("QualityInspectionClient", "signals", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }


}