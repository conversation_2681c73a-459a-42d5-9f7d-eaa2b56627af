/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.listcore.facade.UserListFacade;
import com.xinfei.listcore.facade.rr.UserListRequest;
import com.xinfei.listcore.facade.rr.UserListResponse;
import com.xinfei.listcore.facade.rr.dto.UserListDto;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.ListCoreFacadeClient;
import com.xinfei.vocmng.itl.client.http.SmsCenterFeignClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/10/23 17:10
 * ListCoreFacadeClientImpl
 */
@Slf4j
@Component
public class ListCoreFacadeClientImpl implements ListCoreFacadeClient {

    @Resource
    private UserListFacade userListFacade;

    @Resource
    private SmsCenterFeignClient smsCenterFeignClient;

    @Override
    public Boolean create(UserListDto userListDto) {
        BaseResponse baseResponse = null;
        String msg = "ListCoreFacadeClientImpl.create:";
        try {
            baseResponse = userListFacade.create(userListDto);
            log.info(LogUtil.clientLog("ListCoreFacadeClientImpl", "create", userListDto, baseResponse));
            if (Objects.isNull(baseResponse)) {
                msg += baseResponse == null ? "response is null" : baseResponse.getErrorContext();
                throw new Exception(msg);
            }
            return baseResponse.isSuc();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ListCoreFacadeClientImpl", "create", userListDto, baseResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public List<UserListDto> getList(UserListRequest userListRequest) {
        UserListResponse userListResponse = null;
        String msg = "ListCoreFacadeClientImpl.create:";
        try {
            userListResponse = userListFacade.getList(userListRequest);
            log.info(LogUtil.clientLog("ListCoreFacadeClientImpl", "create", userListRequest, userListResponse));
            if (Objects.isNull(userListResponse) || !userListResponse.isSuc()) {
                msg += userListResponse == null ? "response is null" : userListResponse.getErrorContext();
                throw new Exception(msg);
            }
            return userListResponse.getUserList();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ListCoreFacadeClientImpl", "create", userListRequest, userListResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public Boolean update(UserListDto userListDto) {
        BaseResponse baseResponse = null;
        String msg = "ListCoreFacadeClientImpl.create:";
        try {
            baseResponse = userListFacade.update(userListDto);
            log.info(LogUtil.clientLog("ListCoreFacadeClientImpl", "create", userListDto, baseResponse));
            if (Objects.isNull(baseResponse)) {
                msg += baseResponse == null ? "response is null" : baseResponse.getErrorContext();
                throw new Exception(msg);
            }
            return baseResponse.isSuc();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ListCoreFacadeClientImpl", "create", userListDto, baseResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public List<SmsBlackListDetail> smsBlackList(SmsBlackListRequest smsBlackListRequest) {
        SmsResponse<SmsBlackResponse> smsResponse = null;
        BaseUserCenterRequest<SmsBlackListRequest> request = new BaseUserCenterRequest<>();
        request.setArgs(smsBlackListRequest);
        request.setUa("system");
        String msg = "ListCoreFacadeClientImpl.create:";
        try {
            smsResponse = smsCenterFeignClient.smsBlackList(request);
            log.info(LogUtil.clientLog("smsCenterFeignClient", "smsBlackList", smsBlackListRequest, smsResponse));
            if (Objects.isNull(smsResponse) || !smsResponse.isSuccess()) {
                msg += smsResponse == null ? "response is null" : smsResponse.getMessage();
                throw new Exception(msg);
            }
            return smsResponse.getResponse().getBlackListInfoList();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ListCoreFacadeClientImpl", "smsBlackList", smsBlackListRequest, smsResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

    @Override
    public Boolean smsBlackEdit(SmsBlackEditRequest smsBlackEditRequest) {
        SmsResponse smsResponse = null;
        String msg = "ListCoreFacadeClientImpl.create:";
        BaseUserCenterRequest<SmsBlackEditRequest> request = new BaseUserCenterRequest<>();
        request.setUa("system");
        request.setArgs(smsBlackEditRequest);
        try {
            smsResponse = smsCenterFeignClient.smsBlackSave(request);
            log.info(LogUtil.clientLog("smsCenterFeignClient", "smsBlackEdit", smsBlackEditRequest, smsResponse));
            if (Objects.isNull(smsResponse)) {
                msg += smsResponse == null ? "response is null" : smsResponse.getMessage();
                throw new Exception(msg);
            }
            return 1 == smsResponse.getStatus();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ListCoreFacadeClientImpl", "smsBlackEdit", smsBlackEditRequest, smsResponse, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}