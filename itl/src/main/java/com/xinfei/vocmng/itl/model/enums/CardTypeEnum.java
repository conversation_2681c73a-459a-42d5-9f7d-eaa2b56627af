package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

/**
 * 会员卡类型
 *
 * <AUTHOR>
 * @version $ CardTypeEnum, v 0.1 2023/12/25 21:26 qu.lu Exp $
 */
@Getter
public enum CardTypeEnum {
    OLD_CARD(1,"老会员卡"),
    NEW_CARD(2,"新会员卡"),
    RENEW_CARD(3,"飞享会员卡"),
    VIP_CARD(4,"飞跃会员卡"),
    ;
    CardTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    private Integer type;
    private String desc;
}
