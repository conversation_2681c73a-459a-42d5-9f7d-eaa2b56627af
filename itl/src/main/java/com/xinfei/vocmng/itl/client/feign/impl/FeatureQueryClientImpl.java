package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.FeatureQueryClient;
import com.xinfei.vocmng.itl.rr.FeatureQueryReq;
import com.xinfei.vocmng.itl.rr.FeatureQueryResp;
import com.xinfei.vocmng.itl.rr.dto.ObjType;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Slf4j
@Component
public class FeatureQueryClientImpl {
    @Resource
    private FeatureQueryClient featureQueryClient;

    @Value("${appAccessKey}")
    private String appAccessKey;

    public Map<String, ObjType> featureQuery(FeatureQueryReq request) {
        FeatureQueryResp response = null;

        Map<String, Object> headers = new HashMap<>();
        headers.put("appId", "vocmng");
        headers.put("appAccessKey", appAccessKey);
        headers.put("requestId", UUID.randomUUID().toString());
        String msg = "FeatureQueryClient.featureQuery:";
        try {
            response = featureQueryClient.featureQuery(headers, request);
            log.info(LogUtil.clientLog("FeatureQueryClient", "featureQuery", request, response, headers));
            if (Objects.isNull(response) || !response.isSuc() || response.isHasFailed()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getFeatureValues();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FeatureQueryClient", "featureQuery", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }

}
