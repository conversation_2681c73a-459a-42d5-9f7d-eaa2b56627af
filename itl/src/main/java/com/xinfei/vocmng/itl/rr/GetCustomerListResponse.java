package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ GetCustomerListResponse, v 0.1 2025/3/17 13:15 shaohui.chen Exp $
 */
@Data
public class GetCustomerListResponse {

    private int code;
    private Meta meta;
    private List<Wechat> wechat;
    private List<Weibo> weibo;
    private List<Customer> customers;

    @Data
    public static class Meta {
        @JsonProperty("current_page")
        private int currentPage;
        @JsonProperty("total_pages")
        private int totalPages;
        @JsonProperty("total_count")
        private int totalCount;
    }

    @Data
    public static class Wechat {
        private String id;
        private String name;
    }

    @Data
    public static class Weibo {
        private String id;
        private String name;
    }

    @Data
    public static class Customer {
        private long id;
        @JsonProperty("nick_name")
        private String nickName;
        private String level;
        private String description;
        @JsonProperty("owner_id")
        private long ownerId;
        @JsonProperty("owner_group_id")
        private long ownerGroupId;
        @JsonProperty("custom_fields")
        private Map<String, List<String>> customFields;
        @JsonProperty("open_api_token")
        private String openApiToken;
        @JsonProperty("organization_id")
        private Long organizationId;
        @JsonProperty("is_blocked")
        private boolean isBlocked;
        @JsonProperty("web_token")
        private String webToken;
        @JsonProperty("sdk_token")
        private String sdkToken;
        private List<Tag> tags;
        @JsonProperty("rich_tags")
        private List<RichTag> richTags;
        @JsonProperty("first_contact_at")
        private String firstContactAt;
        @JsonProperty("last_contact_at")
        private String lastContactAt;
        @JsonProperty("first_contact_at_via_phone")
        private String firstContactAtViaPhone;
        @JsonProperty("last_contact_at_via_phone")
        private String lastContactAtViaPhone;
        @JsonProperty("first_contact_at_via_im")
        private String firstContactAtViaIm;
        @JsonProperty("last_contact_at_via_im")
        private String lastContactAtViaIm;
        private String email;
        @JsonProperty("other_emails")
        private List<Object> otherEmails;
        private List<Cellphone> cellphones;
        private String platform;
        @JsonProperty("source_channel")
        private String sourceChannel;
        private List<Weixin> weixins;
        @JsonProperty("weixin_minis")
        private List<WeixinMini> weixinMinis;
        @JsonProperty("weixin_works")
        private List<WeixinWork> weixinWorks;
    }

    @Data
    public static class Tag {
        private int id;
        private String name;
        @JsonProperty("company_id")
        private int companyId;
    }

    @Data
    public static class RichTag {
        private int id;
        private String name;
        private String color;
        @JsonProperty("company_id")
        private int companyId;
    }

    @Data
    public static class Cellphone {
        private int id;
        private String content;
    }

    @Data
    public static class Weixin {
        private String appid;
        private String openid;
        private String unionid;
    }

    @Data
    public static class WeixinMini {
        private String appid;
        private String openid;
        private String unionid;
    }

    @Data
    public static class WeixinWork {
        private String agentid;
        private String corpid;
        private String userid;
        @JsonProperty("open_userid")
        private String openUserid;
    }
}
