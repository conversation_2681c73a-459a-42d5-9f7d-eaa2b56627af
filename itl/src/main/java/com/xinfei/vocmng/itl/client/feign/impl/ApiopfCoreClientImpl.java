package com.xinfei.vocmng.itl.client.feign.impl;

import cn.hutool.core.convert.Convert;
import com.xinfei.apiopfcore.facade.OutBenefitFacade;
import com.xinfei.apiopfcore.facade.rr.request.BaseRequest;
import com.xinfei.apiopfcore.facade.rr.request.QueryBenefitsOrderInfoRequest;
import com.xinfei.apiopfcore.facade.rr.response.QueryBenefitsOrderInfoResponse;
import com.xinfei.apiopfcore.util.response.ApiResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.ApiopfCoreClient;
import com.xinfei.vocmng.itl.rr.apiopfcore.ApiopfCoreRequest;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: long.cheng
 * @Date: 2025/7/28 17:52
 * @description:
 */
@Slf4j
@Component
public class ApiopfCoreClientImpl implements ApiopfCoreClient {

    @Resource
    private OutBenefitFacade outBenefitFacade;

    @Override
    public QueryBenefitsOrderInfoResponse queryBenefitOrderInfo(ApiopfCoreRequest request) {

        BaseRequest<QueryBenefitsOrderInfoRequest> baseRequest = new BaseRequest<>();

        QueryBenefitsOrderInfoRequest orderInfoRequest = new QueryBenefitsOrderInfoRequest();
        orderInfoRequest.setCustNo(request.getCustNo());
        orderInfoRequest.setCurrentPage(request.getCurrentPage());
        orderInfoRequest.setLimit(request.getLimit());

        baseRequest.setUa("vocmng");
        baseRequest.setArgs(orderInfoRequest);
        baseRequest.setTimestamp(Convert.toInt(System.currentTimeMillis() / 1000));
        baseRequest.setSign(StringUtils.EMPTY);

        ApiResponse<QueryBenefitsOrderInfoResponse> response = null;
        String msg = "ApiopfCoreClient.queryBenefitOrderInfo:";
        try {
            response = outBenefitFacade.queryBenefitsOrderInfo(baseRequest);
            log.info(LogUtil.clientLog("ApiopfCoreClient", "queryBenefitOrderInfo", orderInfoRequest, response));

            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getResponse();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ApiopfCoreClient", "queryBenefitOrderInfo", orderInfoRequest, response, e));
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
