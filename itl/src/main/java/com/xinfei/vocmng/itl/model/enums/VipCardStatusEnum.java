package com.xinfei.vocmng.itl.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum VipCardStatusEnum {

    // 枚举实例定义
    // 业务含义: 描述：飞享会员状态: 飞跃会员状态
    INITIAL_UNPAID("初始未支付", "初始未支付(飞享)", 0, null),
    PROCESSING_AWAITING_PAYMENT("处理中", "处理中(飞享)/待支付(飞跃)", 1, "pay_start"), // 卡1的处理中对应卡2的待支付
    PAYMENT_PROCESSING("支付发起", "支付发起(飞享)/支付中(飞跃)", 2, "paying"),       // 卡1的支付发起对应卡2的支付中
    PAYMENT_SUCCESSFUL("支付成功", "支付成功(飞享)/支付成功(飞跃)", 3, "pay_success"),
    PAYMENT_FAILED("支付失败", "支付失败(飞享)/关闭(飞跃)", 4, "pay_close"), // 卡1的支付失败对应卡2的关闭
    PAYMENT_CLOSED_OR_CANCELLED("支付关闭", "支付关闭(飞享)/取消(飞跃)", 5, "pay_cancel"), // 卡1的支付关闭对应卡2的取消
    REFUND_SUCCESSFUL("退卡", "退卡(飞享)/退款成功(飞跃)", 6, "refund_success"),
    REFUND_PENDING("待退款", "待退款(飞跃)", null, "refund_start"),
    REFUNDING("退款中", "退款中(飞跃)", null, "refunding"),
    REFUND_FAILED("退款失败", "退款失败(飞跃)", null, "refund_fail"),
    UNKNOWN("未知状态", "未知状态", null, null);

    private final String name;                    // 统一状态的程序化名称
    private final String description;             // 详细业务含义描述
    private final Integer renewCardStatusCode;    // 飞享会员的状态码 (用Integer以便可以为null)
    private final String vipCardStatusCode;       // 飞跃会员的状态码 (String本身就可以为null)

    // Constants for card types, useful for the convertToUnifiedStatus method
    public static final String CARD_TYPE_RENEW = "RENEW_CARD"; // 飞享
    public static final String CARD_TYPE_VIP = "VIP_CARD";     // 飞跃

    VipCardStatusEnum(String name, String description, Integer renewCardStatusCode, String vipCardStatusCode) {
        this.name = name;
        this.description = description;
        this.renewCardStatusCode = renewCardStatusCode;
        this.vipCardStatusCode = vipCardStatusCode;
    }

    public Optional<Integer> getRenewCardStatusCode() {
        return Optional.ofNullable(renewCardStatusCode);
    }

    public Optional<String> getVipCardStatusCode() {
        return Optional.ofNullable(vipCardStatusCode);
    }

    // --- Reverse Lookup Methods ---

    /**
     * 根据飞享会员的状态码查找统一状态
     *
     * @param statusCode 飞享会员的原始状态码
     * @return 对应的统一状态，如果找不到则返回 Optional.empty()
     */
    public static Optional<VipCardStatusEnum> fromRenewCardStatus(int statusCode) {
        return Arrays.stream(values())
                .filter(status -> status.renewCardStatusCode != null && status.renewCardStatusCode == statusCode)
                .findFirst();
    }

    /**
     * 根据飞跃会员的状态码查找统一状态
     *
     * @param statusCode 飞跃会员的原始状态码 (区分大小写, 如需不区分可使用 equalsIgnoreCase)
     * @return 对应的统一状态，如果找不到则返回 Optional.empty()
     */
    public static Optional<VipCardStatusEnum> fromVipCardStatus(String statusCode) {
        if (statusCode == null) {
            return Optional.empty(); // Or handle differently if null has a specific meaning
        }
        String trimmedStatus = statusCode.trim();
        return Arrays.stream(values())
                // .filter(status -> status.vipCardStatusCode != null && status.vipCardStatusCode.equalsIgnoreCase(trimmedStatus)) // 不区分大小写
                .filter(status -> status.vipCardStatusCode != null && status.vipCardStatusCode.equals(trimmedStatus)) // 区分大小写
                .findFirst();
    }

    /**
     * 根据统一状态的名称 (name 字段) 查找枚举实例。
     * @param nameValue 要查找的 name 字符串。
     * @return 匹配的 VipCardStatusEnum 实例的 Optional，如果找不到则为空 Optional。
     */
    public static Optional<VipCardStatusEnum> fromName(String nameValue) {
        if (nameValue == null) {
            return Optional.empty(); // 如果传入null，直接返回空Optional
        }
        // 可以根据需要决定是否忽略大小写或 trim()
        // String trimmedName = nameValue.trim(); // 如果需要去除前后空格，先 trim
        return Arrays.stream(values()) // 获取所有枚举实例的流
                // .filter(status -> status.name.equalsIgnoreCase(trimmedName)) // 如果需要忽略大小写匹配
                .filter(status -> status.name.equals(nameValue)) // 当前为精确匹配（区分大小写）
                .findFirst(); // 返回第一个匹配的元素，包装在Optional中
    }

    /**
     * 更通用的转换方法：将原生状态转换为统一状态
     *
     * @param cardType     使用 VipCardStatusEnum.CARD_TYPE_RENEW 或 VipCardStatusEnum.CARD_TYPE_VIP
     * @param nativeStatus 原生状态值 (Integer for RENEW_CARD, String for VIP_CARD)
     * @return 对应的统一状态，找不到则返回 UNKNOWN
     */
    public static VipCardStatusEnum convertToUnifiedStatus(String cardType, Object nativeStatus) {
        if (CARD_TYPE_RENEW.equalsIgnoreCase(cardType) && nativeStatus instanceof Integer) {
            return fromRenewCardStatus((Integer) nativeStatus).orElse(UNKNOWN);
        } else if (CARD_TYPE_VIP.equalsIgnoreCase(cardType) && nativeStatus instanceof String) {
            return fromVipCardStatus((String) nativeStatus).orElse(UNKNOWN);
        } else if (nativeStatus == null) { // If native status is null, it might mean it's not applicable for that card
            // Try to find a status where the respective card's status code is null
            if (CARD_TYPE_RENEW.equalsIgnoreCase(cardType)) {
                return Arrays.stream(values())
                        .filter(s -> s.renewCardStatusCode == null && s.vipCardStatusCode != null) // e.g. REFUND_PENDING
                        .findFirst().orElse(UNKNOWN); // This logic might be too specific or need refinement
            } else if (CARD_TYPE_VIP.equalsIgnoreCase(cardType)) {
                return Arrays.stream(values())
                        .filter(s -> s.vipCardStatusCode == null && s.renewCardStatusCode != null) // e.g. INITIAL_UNPAID
                        .findFirst().orElse(UNKNOWN); // This logic might be too specific
            }
        }
        return UNKNOWN; // 类型不匹配或无法处理
    }


    // --- 用于前端筛选时，获取对应卡类型的原生状态列表 (便利方法) ---

    /**
     * 获取此统一状态对应的飞享会员原生状态码 (如果存在)
     * 用于后端根据前端传入的统一状态去查询飞享会员系统
     */
    public Optional<Integer> getNativeRenewCardStatusForQuery() {
        return getRenewCardStatusCode();
    }

    /**
     * 获取此统一状态对应的飞跃会员原生状态码 (如果存在)
     * 用于后端根据前端传入的统一状态去查询飞跃会员系统
     */
    public Optional<String> getNativeVipCardStatusForQuery() {
        return getVipCardStatusCode();
    }


//    public static void main(String[] args) {
//        System.out.println("演示获取枚举属性:");
//        VipCardStatusEnum status1 = VipCardStatusEnum.PROCESSING_AWAITING_PAYMENT;
//        System.out.println("Enum Constant: " + status1.name()); // PROCESSING_AWAITING_PAYMENT (the constant name itself)
//        System.out.println("Name: " + status1.getName());                 // 处理中
//        System.out.println("Description: " + status1.getDescription());   // 处理中(飞享)/待支付(飞跃)
//        System.out.println("RenewCard Status: " + status1.getRenewCardStatusCode().orElse(null)); // 1
//        System.out.println("VipCard Status: " + status1.getVipCardStatusCode().orElse(null));     // pay_start
//
//        VipCardStatusEnum status2 = VipCardStatusEnum.REFUND_PENDING;
//        System.out.println("\nName: " + status2.getName());                 // 待退款
//        System.out.println("Description: " + status2.getDescription());   // 待退款(飞跃)
//        System.out.println("RenewCard Status: " + status2.getRenewCardStatusCode().orElse(null)); // null
//        System.out.println("VipCard Status: " + status2.getVipCardStatusCode().orElse(null));     // refund_start
//
//        System.out.println("\n演示从原生状态查找统一状态:");
//        VipCardStatusEnum foundFromRenew = VipCardStatusEnum.fromRenewCardStatus(0).orElse(VipCardStatusEnum.UNKNOWN);
//        System.out.println("RenewCard status 0 maps to: " + foundFromRenew.getName() + " (" + foundFromRenew.getDescription() + ")");
//
//        VipCardStatusEnum foundFromVip = VipCardStatusEnum.fromVipCardStatus("pay_success").orElse(VipCardStatusEnum.UNKNOWN);
//        System.out.println("VipCard status 'pay_success' maps to: " + foundFromVip.getName() + " (" + foundFromVip.getDescription() + ")");
//
//        VipCardStatusEnum notFoundFromVip = VipCardStatusEnum.fromVipCardStatus("invalid_status").orElse(VipCardStatusEnum.UNKNOWN);
//        System.out.println("VipCard status 'invalid_status' maps to: " + notFoundFromVip.getName());
//
//        System.out.println("\n演示通用转换方法:");
//        System.out.println("Convert RenewCard status 3: " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_RENEW, 3).getName());
//        System.out.println("Convert VipCard status 'paying': " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_VIP, "paying").getName());
//        System.out.println("Convert RenewCard status 99 (unknown): " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_RENEW, 99).getName());
//        System.out.println("Convert VipCard status 'xyz' (unknown): " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_VIP, "xyz").getName());
//        // Test null native status conversion (might need more thought based on exact requirements)
//        System.out.println("Convert RenewCard status null (e.g. for REFUND_PENDING): " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_RENEW, null).getName());
//        System.out.println("Convert VipCard status null (e.g. for INITIAL_UNPAID): " + VipCardStatusEnum.convertToUnifiedStatus(CARD_TYPE_VIP, null).getName());
//
//
//        System.out.println("\n演示查询时获取原生状态:");
//        VipCardStatusEnum filterStatus = VipCardStatusEnum.PAYMENT_PROCESSING;
//        filterStatus.getNativeRenewCardStatusForQuery().ifPresent(s1 -> System.out.println("Query RenewCard with status: " + s1));
//        filterStatus.getNativeVipCardStatusForQuery().ifPresent(s2 -> System.out.println("Query VipCard with status: " + s2));
//
//        VipCardStatusEnum filterStatusOnlyVip = VipCardStatusEnum.REFUNDING;
//        filterStatusOnlyVip.getNativeRenewCardStatusForQuery().ifPresentOrElse(
//                s1 -> System.out.println("Query RenewCard with status: " + s1),
//                () -> System.out.println("No RenewCard status for " + filterStatusOnlyVip.getName())
//        );
//        filterStatusOnlyVip.getNativeVipCardStatusForQuery().ifPresent(s2 -> System.out.println("Query VipCard with status: " + s2));
//    }
}
