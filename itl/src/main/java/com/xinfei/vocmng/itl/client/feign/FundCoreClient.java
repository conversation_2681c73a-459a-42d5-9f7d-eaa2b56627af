package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.fundcore.facade.api.request.*;
import com.xinfei.fundcore.facade.api.response.FundOrderQueryResponse;
import com.xinfei.fundcore.facade.api.response.SettleCertApplyResponse;


/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */


public interface FundCoreClient {

    /**
     * 结清证明申请接口
     */
    SettleCertApplyResponse settleCertApply(SettleCertApplyRequest request);

    /**
     * 证明类文件通用查询接口
     */
    String settleCertQuery(SettleCertQueryRequest request);

    /**
     * 资方额度取消校验
     */
    Boolean logoutQuotaCheck(IsCanLogoutQuotaRequest request);

    /**
     * 资方额度取消
     */
    Boolean logoutQuota(LogoutQuotaRequest request);

    /**
     * 资方借据单号互查(老系统下线)
     */
    FundOrderQueryResponse fundOrderQuery(FundOrderQueryRequest request);
}
