/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.repaytrade.facade.RepayListFacade;
import com.xinfei.repaytrade.facade.rr.request.list.ListQueryRequest;
import com.xinfei.repaytrade.facade.rr.request.list.ListSaveRequest;
import com.xinfei.repaytrade.facade.rr.request.list.ListUpdateRequest;
import com.xinfei.repaytrade.facade.rr.response.Base.BaseDataResponse;
import com.xinfei.repaytrade.facade.rr.response.list.ListInfoResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.RepayListClient;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> 2024/11/7 14:21
 * RepayListClientImpl
 */
@Component
@Slf4j
public class RepayListClientImpl implements RepayListClient {

    @Resource
    private RepayListFacade repayListFacade;

    @Override
    public List<ListInfoResponse> repayListQuery(String custNo) {
        BaseDataResponse<List<ListInfoResponse>> response = null;
        ListQueryRequest request = new ListQueryRequest();
        request.setListValue(custNo);
        request.setListValueType("CUST_NO");
        request.setBizScene("MULTI_CARD_REPAYMENT");
        String msg = "RepayListFacade.repayListQuery:";
        try {
            response = repayListFacade.query(request);
            log.info(LogUtil.clientLog("RepayListFacade", "repayListQuery", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayListFacade", "repayListQuery", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData();
    }

    @Override
    public Boolean repayListCreate(ListSaveRequest request) {
        BaseDataResponse<ListInfoResponse> response = null;
        String msg = "RepayListFacade.repayListCreate:";
        try {
            response = repayListFacade.create(request);
            log.info(LogUtil.clientLog("RepayListFacade", "repayListCreate", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayListFacade", "repayListCreate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData() != null;
    }

    @Override
    public Boolean repayListUpdate(ListUpdateRequest request) {
        BaseDataResponse<ListInfoResponse> response = null;
        String msg = "RepayListFacade.repayListUpdate:";
        try {
            response = repayListFacade.update(request);
            log.info(LogUtil.clientLog("RepayListFacade", "repayListUpdate", request, response));
            if (response == null || !response.isSuc() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayListFacade", "repayListUpdate", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
        return response.getData() != null;
    }
}