/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 整合类 config
 *
 * <AUTHOR>
 * @version $ Constants, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Data
@Configuration
@EnableFeignClients(basePackageClasses = {ItlConfig.class})
@ComponentScan(basePackageClasses = {ItlConfig.class})
public class ItlConfig {
    /** 调用合同系统配置信息 */
    @Value("${contract.ua}")
    private String contractUA;
}
