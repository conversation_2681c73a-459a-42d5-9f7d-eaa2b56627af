package com.xinfei.vocmng.itl.client.feign;

import com.xinfei.repaytrade.facade.rr.request.list.ListSaveRequest;
import com.xinfei.repaytrade.facade.rr.request.list.ListUpdateRequest;
import com.xinfei.repaytrade.facade.rr.response.list.ListInfoResponse;

import java.util.List;

/**
 * <AUTHOR> 2024/11/7 14:22
 * RepayListClient
 */
public interface RepayListClient {

    List<ListInfoResponse> repayListQuery(String custNo);

    Boolean repayListCreate(ListSaveRequest request);

    Boolean repayListUpdate(ListUpdateRequest request);

}
