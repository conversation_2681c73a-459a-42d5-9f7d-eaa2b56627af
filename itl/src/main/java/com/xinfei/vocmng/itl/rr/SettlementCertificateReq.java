/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ SettlementCertificateReq, v 0.1 2024/6/4 12:01 wancheng.qu Exp $
 */
@Data
public class SettlementCertificateReq implements Serializable {
    @ApiModelProperty("邮箱")
    private String mail;

    @NotBlank(message = "userNo不能为空")
    @ApiModelProperty("userNo")
    private String userNo;

    @NotBlank(message = "custNo不能为空")
    @ApiModelProperty("custNo")
    private String custNo;

    @NotBlank(message = "身份证不能为空")
    @ApiModelProperty("身份证")
    private String idNo;

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("type=2为资方结清证明，type=3为放款凭证，type=4:明珠结清证明")
    private Integer type;

    @ApiModelProperty("订单详情列表")
    private List<LoanInfoRequest> orders;

    private Integer access;//推送状态，1.已推送，2推送失败，3线下提交'

}