package com.xinfei.vocmng.itl.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.vocmng.itl.rr.ContractBaseRequest;
import feign.RequestTemplate;
import feign.codec.EncodeException;
import feign.codec.Encoder;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
public class CustomerEncoder implements Encoder {
    @Override
    public void encode(Object object, Type bodyType, RequestTemplate template) throws EncodeException {
        ObjectMapper mapper = new ObjectMapper();
        try {
            ContractBaseRequest baseRequest = (ContractBaseRequest)object;
            template.body(mapper.writeValueAsString(baseRequest));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
