/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl.client.feign.impl;

import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.http.AmsFeignClient;
import com.xinfei.vocmng.itl.client.http.LcsFeignClient;
import com.xinfei.vocmng.itl.rr.BatchLoanQueryResponse;
import com.xinfei.vocmng.itl.util.LogUtil;
import io.kyoto.pillar.ams.rest.dto.falcon.req.AccountAmtLogRequest;
import io.kyoto.pillar.ams.rest.dto.falcon.resp.AccountAmountLogResponse;
import io.kyoto.pillar.lcs.loan.domain.LoanBatchQueryRequest;
import io.kyoto.pillar.lcs.loan.domain.LoanQueryResponse;
import io.kyoto.sole.api.domain.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ AmsFeignService, v 0.1 2024/8/12 11:39 you.zhang Exp $
 */
@Slf4j
@Component
public class AmsFeignService {

    @Resource
    private AmsFeignClient amsFeignClient;

    public List<AccountAmountLogResponse> queryAccountAmtLog(AccountAmtLogRequest request) {

        Response<List<AccountAmountLogResponse>> response = null;
        String msg = "AcsFeignClient.accountAmtLog:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = amsFeignClient.accountAmtLog(request);
            log.info(LogUtil.clientLog("AmsFeignClient", "accountAmtLog", request, response));
            if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AcsFeignClient", "accountAmtLog", request, response, msg), e);
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, msg, ErrorLevelsEnum.ERROR);
        }
    }
}
