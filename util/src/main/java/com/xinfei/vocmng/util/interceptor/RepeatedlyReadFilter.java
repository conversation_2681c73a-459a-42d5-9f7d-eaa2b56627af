package com.xinfei.vocmng.util.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @version $ RepeatedlyReadFilter, v 0.1 2025/3/17 11:43 shaohui.chen Exp $
 * 获取请求体
 */
@Component
@Slf4j
public class RepeatedlyReadFilter implements Filter {

    private static final Set<String> SUPPORTED_METHODS = Collections.unmodifiableSet(
            Stream.of("POST", "PUT", "PATCH").collect(Collectors.toSet())
    );
    private static final String WRAPPER_ATTRIBUTE = "REPEATED_READ_WRAPPED";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 避免重复包装
        if (httpRequest.getAttribute(WRAPPER_ATTRIBUTE) != null || !needsWrapping(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            HttpServletRequest wrappedRequest = new RepeatedlyRequestWrapper(httpRequest);
            httpRequest.setAttribute(WRAPPER_ATTRIBUTE, true);
            chain.doFilter(wrappedRequest, response);
        } catch (IOException e) {
            log.error("Failed to wrap request", e);
            throw e;
        }
    }

    private boolean needsWrapping(HttpServletRequest request) {
        String method = request.getMethod();
        String contentType = request.getContentType();
        return SUPPORTED_METHODS.contains(method)
                && contentType != null
                && contentType.startsWith("application/json");
    }

}
