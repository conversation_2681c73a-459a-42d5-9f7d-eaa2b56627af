/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.util.exception;

import com.xinfei.vocmng.util.enums.ErrDtlEnum;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * VocmngException异常基类
 *
 * <AUTHOR>
 * @version $ VocmngException, v 0.1 2023/8/28 18:44 Jinyan.Huang Exp $
 */
@Getter
public class VocmngException extends RuntimeException {

    /**
     * 结果枚举
     * -- GETTER --
     *  Getter method for property <tt>resultCodeEnum</tt>.
     *
     * @return property value of resultCodeEnum

     */
    private final ErrDtlEnum resultCodeEnum;

    /**
     * 额外的异常信息
     * -- GETTER --
     *  Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg

     */
    private String msg;

    /**
     * 构造函数
     *
     * @param resultCodeEnum 错误描述枚举
     */
    public VocmngException(ErrDtlEnum resultCodeEnum) {
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     */
    public VocmngException(ErrDtlEnum resultCodeEnum, String msg) {
        super(msg);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param cause          异常
     */
    public VocmngException(ErrDtlEnum resultCodeEnum, Throwable cause) {
        super(cause);
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     * @param cause          异常
     */
    public VocmngException(ErrDtlEnum resultCodeEnum, String msg, Throwable cause) {
        super(msg, cause);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * @see java.lang.Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(200);
        sb.append(resultCodeEnum.getDescription());
        if (StringUtils.hasText(msg)) {
            sb.append("|");
            sb.append(msg);
        }
        return sb.toString();
    }

    //~~~ 属性方法 ~~~

}
