package com.xinfei.vocmng.util.rocketmq;

import com.xinfei.vocmng.util.constant.MonitorConstant;
import com.xinfei.vocmng.util.logger.MonitorLogBuilder;
import com.xinfei.vocmng.util.trace.TraceUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * 使用时 rocketmq.enable = true
 * <AUTHOR>
 * @version $ AbstractMessageListener, v 0.1 2025/3/17 14:29 shaohui.chen Exp $
 */
@Slf4j
public abstract class AbstractMessageListener<T> extends MqConsumerListener<T> {

    @Override
    protected void doMessage(String topic, T body, MessageExt message) {
        TraceUtil.initTraceId();
        long startTime = System.currentTimeMillis();
        Boolean result = Boolean.TRUE;
        MonitorLogBuilder monitorLogBuilder = MonitorLogBuilder.getInstance(MonitorConstant.Instance.MESSAGE_CONSUMER, System.currentTimeMillis(), Boolean.TRUE)
                .builder(MonitorConstant.BuilderKey.TOPIC, message.getTopic())
                .builder(MonitorConstant.BuilderKey.TAG, message.getTags());
        log.info("topic: {}, msgId: {}, retryTimes: {}, body:{}", message.getTopic(), message.getMsgId(), message.getReconsumeTimes(), body);
        try {
            execute(body);
        } catch (Throwable e) {
            log.error("message_error, topic: {}, msgId: {}", message.getTopic(), message.getMsgId(), e);
            result = Boolean.FALSE;
            throw e;
        } finally {
            monitorLogBuilder.builder(MonitorConstant.BuilderKey.COST_TIME, System.currentTimeMillis() - startTime)
                    .builder(MonitorConstant.BuilderKey.RESULT, result)
                    .print();
            TraceUtil.clearTraceId();
        }
    }

    protected abstract void execute(T body);

}
