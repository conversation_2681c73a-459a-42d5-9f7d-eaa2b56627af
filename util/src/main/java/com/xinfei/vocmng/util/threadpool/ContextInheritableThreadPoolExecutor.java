package com.xinfei.vocmng.util.threadpool;

import com.xinfei.xfframework.thread.XfThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ ContextInheritableThreadPoolExecutor, v 0.1 2025/3/17 10:22 shaohui.chen Exp $
 */
@Slf4j
@SuppressWarnings("all")
public class ContextInheritableThreadPoolExecutor extends XfThreadPoolExecutor {

    private static class RunnableProxy implements Runnable {

        private final Runnable delegate;

        private final Map<String, String> mdcContext;

        public RunnableProxy(Runnable runnable) {
            this.delegate = runnable;
            this.mdcContext = MDC.getCopyOfContextMap();
        }

        @Override
        public void run() {
            //保存原线程的上下文
            Map<String, String> backupMdcContext = MDC.getCopyOfContextMap();
            try {
                try {
                    if (mdcContext != null && !mdcContext.isEmpty()) {
                        MDC.setContextMap(mdcContext);
                    }
                } catch (Exception e) {
                    log.error("RunnableProxy replay exception", e);
                }
                //放在try外层 不影响线程运行
                this.delegate.run();
            } finally {
                try {
                    //恢复原线程上下文
                    if (backupMdcContext != null && !backupMdcContext.isEmpty()) {
                        MDC.setContextMap(backupMdcContext);
                    } else {
                        MDC.clear();
                    }
                } catch (Exception e) {
                    log.error("RunnableProxy restore exception", e);
                }
            }
        }

    }

    private static class CallableProxy<V> implements Callable<V> {
        private final Callable<V> delegate;
        private final Map<String, String> mdcContext;

        public CallableProxy(Callable<V> callable) {
            this.delegate = callable;
            this.mdcContext = MDC.getCopyOfContextMap();
        }

        @Override
        public V call() throws Exception {
            //保存原线程的上下文
            Map<String, String> backupMdcContext = MDC.getCopyOfContextMap();
            try {
                try {
                    if (mdcContext != null && !mdcContext.isEmpty()) {
                        MDC.setContextMap(mdcContext);
                    }
                } catch (Exception e) {
                    log.error("RunnableProxy replay exception", e);
                }
                return this.delegate.call();

            } finally {
                try {
                    //恢复原线程上下文
                    if (backupMdcContext != null && !backupMdcContext.isEmpty()) {
                        MDC.setContextMap(backupMdcContext);
                    } else {
                        MDC.clear();
                    }
                } catch (Exception e) {
                    log.error("RunnableProxy restore exception", e);
                }
            }
        }
    }

    @Override
    public void execute(Runnable command) {
        super.execute(new RunnableProxy(command));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(new CallableProxy<>(task));
    }

    public ContextInheritableThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public ContextInheritableThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public ContextInheritableThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public ContextInheritableThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

}
