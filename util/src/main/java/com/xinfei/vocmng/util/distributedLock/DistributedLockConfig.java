package com.xinfei.vocmng.util.distributedLock;

import org.aopalliance.aop.Advice;
import org.aopalliance.intercept.MethodInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.Pointcut;
import org.springframework.aop.framework.autoproxy.ProxyCreationContext;
import org.springframework.aop.support.AbstractPointcutAdvisor;
import org.springframework.aop.support.AopUtils;
import org.springframework.aop.support.StaticMethodMatcherPointcut;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version $ DistributedLockConfig, v 0.1 2025/3/20 17:58 shaohui.chen Exp $
 */
@Configuration
public class DistributedLockConfig extends AbstractPointcutAdvisor {

    /**
     * 代理对象
     */
    private static Map<String, Class<?>> LOCK_PROXY_BEAN = new ConcurrentHashMap<>(16);

    @Resource
    private DefaultListableBeanFactory beanFactory;

    private final StaticMethodMatcherPointcut pointcut = new StaticMethodMatcherPointcut() {

        @Override
        public boolean matches(Method method, Class<?> targetClass) {
            if (matchesMethod(method)) {
                return true;
            }
            // Proxy classes never have annotations on their redeclared methods.
            Method specificMethod;
            if (Proxy.isProxyClass(targetClass)) {
                try {
                    String proxyAdd = targetClass.toString();
                    if (!LOCK_PROXY_BEAN.containsKey(proxyAdd)) {
                        String beanName = ProxyCreationContext.getCurrentProxiedBeanName();
                        if (StringUtils.isBlank(beanName)) {
                            return false;
                        }
                        BeanDefinition definition = beanFactory.getBeanDefinition(beanName);
                        LOCK_PROXY_BEAN.put(proxyAdd, Class.forName(definition.getBeanClassName()));
                    }
                    specificMethod = AopUtils.getMostSpecificMethod(method, LOCK_PROXY_BEAN.get(proxyAdd));
                } catch (Exception e) {
                    return false;
                }
            } else {
                specificMethod = AopUtils.getMostSpecificMethod(method, targetClass);
            }

            return (specificMethod != method && matchesMethod(specificMethod));
        }

        private boolean matchesMethod(Method method) {
            DistributedLock annotation = AnnotationUtils.findAnnotation(method, DistributedLock.class);
            return null != annotation;
        }
    };

    @Override
    public Pointcut getPointcut() {
        return pointcut;
    }

    @Resource
    private DistributedLockMethodInterceptor distributedLockMethodInterceptor;

    @Bean
    @Lazy
    public MethodInterceptor distributedLockMethodInterceptor() {
        return new DistributedLockMethodInterceptor();
    }

    @Override
    public Advice getAdvice() {
        return distributedLockMethodInterceptor;
    }
}
