package com.xinfei.vocmng.util.interceptor;

import com.google.common.base.Charsets;
import com.xinfei.vocmng.util.util.IPAddressUtil;
import lombok.extern.slf4j.Slf4j;
import okio.Okio;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.Set;

/**
 * <AUTHOR>
 * @version $ GateInterceptor, v 0.1 2025/3/20 11:43 shaohui.chen Exp $
 */
@Component
@Slf4j
public class GateInterceptor implements HandlerInterceptor {

    private Set<String> headers;

    public GateInterceptor() {
    }

    public GateInterceptor(Set<String> headers) {
        this.headers = headers;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String method = request.getMethod();
        String URI = request.getServletPath();

        String remoteIp = IPAddressUtil.getRemoteIp(request);

        StringBuilder sb = new StringBuilder(512);
        sb.append(" ");
        sb.append(method);
        sb.append(" ");
        sb.append(URI);
        sb.append(" ");
        sb.append(remoteIp);
        sb.append(" ");
        sb.append(buildHeaders(request));
        sb.append(" ");
        sb.append(buildFormRequest(request));
        sb.append(" ");
        MediaType mediaType = null;
        try {
            mediaType = MediaType.parseMediaType(request.getContentType());
        } catch (Exception e) {
            //ignore
        }

        if (mediaType == null || !MediaType.MULTIPART_FORM_DATA.isCompatibleWith(mediaType)) {
            sb.append(Okio.buffer(Okio.source(request.getInputStream())).readString(Charsets.UTF_8));
        }

        log.info(sb.toString());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
    }

    private String buildFormRequest(HttpServletRequest request) {
        StringBuilder stringBuilder = new StringBuilder();
        Enumeration<String> enumeration = request.getParameterNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getParameter(key);
            stringBuilder.append(key);
            stringBuilder.append("=");
            stringBuilder.append(value);
            stringBuilder.append("&");
        }

        if (stringBuilder.length() > 0) {
            return stringBuilder.substring(0, stringBuilder.length() - 1);
        }

        return stringBuilder.toString();
    }

    private String buildHeaders(HttpServletRequest request) {
        if (headers == null) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            if (!headers.contains(key)) {
                continue;
            }

            String value = request.getHeader(key);
            stringBuilder.append(key);
            stringBuilder.append("=");
            stringBuilder.append(value);
            stringBuilder.append("&");
        }

        if (stringBuilder.length() > 0) {
            return stringBuilder.substring(0, stringBuilder.length() - 1);
        }

        return stringBuilder.toString();
    }

}
