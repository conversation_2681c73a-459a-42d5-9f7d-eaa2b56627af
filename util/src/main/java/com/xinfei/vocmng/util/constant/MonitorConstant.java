package com.xinfei.vocmng.util.constant;

/**
 * <AUTHOR>
 * @version $ MonitorConstant, v 0.1 2025/4/9 15:39 shaohui.chen Exp $
 */
public interface MonitorConstant {

    interface MonitorKey {

        String REQUEST_START_TIME = "request-start-time";

        String MONITOR_LOG_RESULT = "monitor-log-result";
    }

    interface BuilderKey {

        String METHOD = "method";

        String URI = "uri";

        String REMOTE_IP = "remoteIp";

        String TOPIC = "topic";

        String TAG = "tag";

        String FEIGN_NAME = "feignName";

        String RESULT = "result";

        String COST_TIME = "costTime";

        String LOG_KEY = "logKey";
    }

    /**
     *  分类
     */
    interface Instance {
        /**
         * controller api
         */
        String HTTP_REQUEST = "REQUEST";

        /**
         * rocket consumer
         */
        String MESSAGE_CONSUMER = "MESSAGE_CONSUMER";

        /**
         * 死信队列
         */
        String MESSAGE_DEAD_MSG = "MESSAGE_DEAD_MSG";

        /**
         * feign请求外部的LogKey
         */
        String FEIGN_REQUEST = "FEIGN_REQUEST";
    }

}
