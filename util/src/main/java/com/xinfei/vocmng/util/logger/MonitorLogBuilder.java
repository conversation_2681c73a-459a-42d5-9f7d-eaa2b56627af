package com.xinfei.vocmng.util.logger;

import com.xinfei.vocmng.util.constant.MonitorConstant;
import com.xinfei.vocmng.util.holder.MonitorParamHolder;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ MonitorLogBuilder, v 0.1 2025/4/9 15:10 shaohui.chen Exp $
 */
public class MonitorLogBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger("monitor");

    private final Map<String, Object> fieldMap = new LinkedHashMap<>();

    private MonitorLogBuilder(String logKey, long costTime, boolean result) {
        fieldMap.put(MonitorConstant.BuilderKey.LOG_KEY, logKey);
        fieldMap.put(MonitorConstant.BuilderKey.COST_TIME, costTime);
        fieldMap.put(MonitorConstant.BuilderKey.RESULT, result);
    }

    public static MonitorLogBuilder getInstance(String logKey, long costTime, boolean result) {
        return new MonitorLogBuilder(logKey, costTime, result);
    }

    public MonitorLogBuilder builder(String key, Object value) {
        fieldMap.put(key, value);
        return this;
    }

    public void print() {
        try {
            // 从 ThreadLocal 中获取参数
            Map<String, String> threadLocalMap = MonitorParamHolder.get();
            if (MapUtils.isNotEmpty(threadLocalMap)) {
                fieldMap.putAll(threadLocalMap);
            }

            // 设置 MDC 参数
            for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value == null) {
                    continue;
                }

                MDC.put(key, value.toString());
            }

            // 打印日志：实际内容可以简单写个固定的占位符
            LOGGER.info("monitor_log");

        } finally {
            // 清理 MDC，避免 ThreadLocal 污染
            MDC.clear();
        }
    }

}
