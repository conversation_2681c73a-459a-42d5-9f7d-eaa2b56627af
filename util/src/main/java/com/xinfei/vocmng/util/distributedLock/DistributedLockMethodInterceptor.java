package com.xinfei.vocmng.util.distributedLock;

import com.xinfei.vocmng.util.constant.Separator;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ DistributedLockMethodInterceptor, v 0.1 2025/3/20 17:59 shaohui.chen Exp $
 */
@Slf4j
public class DistributedLockMethodInterceptor implements MethodInterceptor {

    private final static String LOCK_VALUE = "LOCK";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Object invoke(MethodInvocation methodInvocation) throws Throwable {
        Method method = methodInvocation.getMethod();
        DistributedLock annotation = getAnnotation(method);
        if (null == annotation) {
            return methodInvocation.proceed();
        }
        String lockName;
        lockName = getRedisKey(annotation, methodInvocation);

        long lockTime = annotation.lockTime();
        long retryTimes = annotation.retryTimes();
        Boolean lock = false;
        try {
            while (null != lock && !lock && retryTimes > 0) {
                // todo 暂时使用redis的setnx命令实现分布式锁，后续可以考虑使用redisson
                lock = redisTemplate.boundValueOps(lockName).setIfAbsent(LOCK_VALUE, lockTime, TimeUnit.MINUTES);
                retryTimes--;
                if (null != lock && !lock) {
                    Thread.sleep(500L);
                }
            }
            if (null != lock && lock) {
                log.info("get redis distributedLock success,lockName is {},threadName is {}", lockName,
                        Thread.currentThread().getName());
                return methodInvocation.proceed();
            } else {
                log.info("get redis distributedLock fail,lockName is {},threadName is {}", lockName,
                        Thread.currentThread().getName());
            }
        } catch (Exception e) {
            log.error("get redis distributedLock error", e);
            throw e;
        } finally {
            if (null != lock && lock) {
                redisTemplate.delete(lockName);
                log.info("redis distributedLock release success");
            }
        }
        return null;
    }

    private String getRedisKey(DistributedLock annotation, MethodInvocation methodInvocation) {
        String key = annotation.lockName();
        if (!key.contains(Separator.SPLIT_POUND)) {
            return key;
        }
        return generateKeyBySpEL(key, methodInvocation.getMethod(), methodInvocation.getArguments());
    }

    /**
     * 用于SpEL表达式解析.
     */
    private SpelExpressionParser parser = new SpelExpressionParser();
    /**
     * 用于获取方法参数定义名字.
     */
    private DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    private String generateKeyBySpEL(String spELString, Method method, Object[] args) {
        String[] paramNames = nameDiscoverer.getParameterNames(method);
        if (null == paramNames) {
            throw new RuntimeException("参数不存在");
        }
        Expression expression = parser.parseExpression(spELString);
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        Object value = expression.getValue(context);
        if (null == value) {
            throw new RuntimeException("参数不存在");
        }
        return value.toString();
    }

    private DistributedLock getAnnotation(Method method) {
        if (method.isAnnotationPresent(DistributedLock.class)) {
            return method.getAnnotation(DistributedLock.class);
        }
        return null;
    }
}
