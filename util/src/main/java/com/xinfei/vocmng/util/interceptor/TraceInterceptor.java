package com.xinfei.vocmng.util.interceptor;

import com.xinfei.vocmng.util.trace.TraceUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version $ TraceInterceptor, v 0.1 2025/3/17 11:39 shaohui.chen Exp $
 */
@Component
@SuppressWarnings("all")
public class TraceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        TraceUtil.initTraceId();
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        TraceUtil.clearTraceId();
    }
}
