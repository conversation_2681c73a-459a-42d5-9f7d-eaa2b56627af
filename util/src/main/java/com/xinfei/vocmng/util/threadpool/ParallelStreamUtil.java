package com.xinfei.vocmng.util.threadpool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;

/**
 * 并行流工具类，提供自定义线程池的并行处理能力
 * 
 * <AUTHOR>
 * @version $ ParallelStreamUtil, v 0.1 2025/4/15 14:30 shaohui.chen Exp $
 */
@Slf4j
public class ParallelStreamUtil {

    /**
     * 使用自定义线程池处理并行任务
     *
     * @param dataList     待处理的数据列表
     * @param parallelism  并行度
     * @param function     处理函数
     * @param <T>          输入类型
     * @param <R>          输出类型
     * @return 处理结果列表
     */
    public static <T, R> List<R> parallelStream(List<T> dataList, int parallelism, Function<T, R> function) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建自定义线程池
        ThreadPoolExecutor executor = createThreadPool(parallelism);
        
        try {
            // 使用CompletableFuture进行并行处理
            List<CompletableFuture<R>> futures = new ArrayList<>(dataList.size());
            
            for (T data : dataList) {
                CompletableFuture<R> future = CompletableFuture.supplyAsync(() -> function.apply(data), executor);
                futures.add(future);
            }
            
            // 等待所有任务完成并收集结果
            List<R> results = new ArrayList<>(dataList.size());
            for (CompletableFuture<R> future : futures) {
                try {
                    results.add(future.get());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Task was interrupted", e);
                } catch (ExecutionException e) {
                    throw new RuntimeException("Error executing task", e.getCause());
                }
            }
            
            return results;
        } finally {
            // 关闭线程池
            executor.shutdown();
        }
    }
    
    /**
     * 创建自定义线程池
     *
     * @param parallelism 并行度
     * @return 线程池
     */
    private static ThreadPoolExecutor createThreadPool(int parallelism) {
        return new ContextInheritableThreadPoolExecutor(
                parallelism,                                    // 核心线程数
                parallelism,                                    // 最大线程数
                60L,                                           // 空闲线程存活时间
                TimeUnit.SECONDS,                              // 时间单位
                new LinkedBlockingQueue<>(1000),               // 工作队列
                new CustomizableThreadFactory("parallel-"),    // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy()      // 拒绝策略
        );
    }
}
