package com.xinfei.vocmng.biz.consumer;

import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.rr.CreateCustomerResponse;
import com.xinfei.vocmng.itl.rr.Customer;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.MergeCustomerResponse;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 合并任务处理器
 * 处理合并任务的具体逻辑
 *
 * <AUTHOR>
 * @version $ MergeTaskHandler, v 0.1 2025/5/1 $
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MergeTaskHandler {

    private final StringRedisTemplate redisTemplate;
    private final UdeskClientService udeskClientService;
    private final CisFacadeClient cisFacadeClient;

    private static final long SUCCESS_TTL_DAYS = 30;
    private static final String APP_XYF = "xyf";
    private static final String APP_XYF01 = "xyf01";
    private static final String UDESK_SEARCH_FIELD_WEB_TOKEN = "web_token";
    private static final String UDESK_CUSTOM_FIELD_UID = "TextField_1500643";

    /** 处理合并任务 */
    public void handleTask(String webTokenUserNo) {
        log.info("Processing merge task for webTokenUserNo: {}", webTokenUserNo);

        if (StringUtils.isBlank(webTokenUserNo)) {
            log.warn("Skipping merge task: webTokenUserNo is blank");
            return;
        }
        try {
            Long.parseLong(webTokenUserNo);
        } catch (NumberFormatException e) {
            // 如果不能转换，记录警告并直接返回，不再执行后续操作
            log.warn("webTokenUserNo '{}' 不是一个有效的Long类型字符串. 将跳过CIS用户查询. 错误: {}", webTokenUserNo, e.getMessage());
            return;
        }
        try {
            PageResult<UserSearchDTO> currentUserCisResult = cisFacadeClient.queryUserList(null, null, webTokenUserNo, 1, 1);
            if (Objects.isNull(currentUserCisResult) || CollectionUtils.isEmpty(currentUserCisResult.getList())) {
                log.warn("CIS user not found for webTokenUserNo: {}. Cannot determine mobile.", webTokenUserNo);
                return;
            }
            UserSearchDTO currentUserCisInfo = currentUserCisResult.getList().get(0);
            String mobile = currentUserCisInfo.getMobile();

            if (StringUtils.isBlank(mobile)) {
                log.warn("Mobile number is blank for CIS user with webTokenUserNo: {}. Cannot find related accounts.", webTokenUserNo);
                throw new RuntimeException("Mobile number is blank for CIS user with webTokenUserNo: " + webTokenUserNo);
            }
            PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
            if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                log.warn("No related CIS users found for mobile: {}", mobile);
                return;
            }
            Map<String, String> appUserNoMap = relatedCisUsersResult.getList().stream()
                    .filter(user -> StringUtils.isNotBlank(user.getApp()) && Objects.nonNull(user.getUserNo()))
                    .collect(Collectors.toMap(
                            UserSearchDTO::getApp,
                            user -> String.valueOf(user.getUserNo()),
                            (existingUserNo, newUserNo) -> existingUserNo
                    ));
            String currentApp = null;
            for (Map.Entry<String, String> entry : appUserNoMap.entrySet()) {
                if (webTokenUserNo.equals(entry.getValue())) {
                    currentApp = entry.getKey();
                    break;
                }
            }
            if (StringUtils.isBlank(currentApp)) {
                log.warn("Could not determine the app for the input webTokenUserNo: {} among users with mobile: {}. AppUserNoMap: {}",
                        webTokenUserNo, mobile, appUserNoMap);
                return;
            }
            String xyfUserNoFromCIS = appUserNoMap.get(APP_XYF);
            String xyf01UserNoFromCIS = appUserNoMap.get(APP_XYF01);
            // 提前检查最终的合并目标 (xyf01) 是否已被标记
            if (StringUtils.isNotBlank(xyf01UserNoFromCIS) && ApolloConstant.mergeDuplicateEnabled) {
                String flagKeyForPotentialTarget = RedisKeyConstants.FLAG_KEY_PREFIX + xyf01UserNoFromCIS;
                if (Boolean.TRUE.equals(redisTemplate.hasKey(flagKeyForPotentialTarget))) {
                    return; // 如果 xyf01 账户已经被标记为合并目标，则当前任务可能无需执行
                }
            }
            log.info("Current app for webTokenUserNo {}: {}. Mobile: {}. CIS {}_userNo: {}, CIS {}_userNo: {}",
                    webTokenUserNo, currentApp, mobile, APP_XYF, xyfUserNoFromCIS, APP_XYF01, xyf01UserNoFromCIS);
            // Rule: xyf01 is the target (master), xyf is the source (subordinate).
            if (APP_XYF01.equals(currentApp)) {
                if (StringUtils.isBlank(xyfUserNoFromCIS)) {
                    log.warn("App '{}' (webTokenUserNo: {} which is TARGET): Corresponding source '{}' userNo not found in CIS for mobile {}. Cannot merge.",
                            APP_XYF01, webTokenUserNo, APP_XYF, mobile);
                    return;
                }
                if (Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(RedisKeyConstants.WEB_TOKEN_SET_KEY, xyfUserNoFromCIS))) {
                    log.info("App '{}' (Target Udesk: {}): Found source Udesk customer for '{}' (web_token: {}). Merging source {} into target {}.",
                            APP_XYF01, webTokenUserNo, APP_XYF, xyfUserNoFromCIS, xyfUserNoFromCIS, webTokenUserNo);
                    performUdeskMerge(xyfUserNoFromCIS, webTokenUserNo); // from: xyf, to: xyf01
                } else {
                    ImCustomerDetailsQueryResponse sourceXyfUdeskCustomer = udeskClientService.getCustomerDetailsNew(UDESK_SEARCH_FIELD_WEB_TOKEN, xyfUserNoFromCIS);
                    if (Objects.nonNull(sourceXyfUdeskCustomer) && Objects.nonNull(sourceXyfUdeskCustomer.getCustomer())) {
                        log.info("App '{}' (Target Udesk: {}): Found source Udesk customer for '{}' (web_token: {}). Merging source {} into target {}.",
                                APP_XYF01, webTokenUserNo, APP_XYF, xyfUserNoFromCIS, xyfUserNoFromCIS, webTokenUserNo);
                        if (StringUtils.equals(sourceXyfUdeskCustomer.getCustomer().getWebToken(), webTokenUserNo)) {
                            return;
                        }
                        performUdeskMerge(xyfUserNoFromCIS, webTokenUserNo);
                    }
                }
            } else if (APP_XYF.equals(currentApp)) {
                if (StringUtils.isBlank(xyf01UserNoFromCIS)) {
                    log.warn("App '{}' (webTokenUserNo: {} which is SOURCE): Corresponding target '{}' userNo not found in CIS for mobile {}. Cannot determine merge target.",
                            APP_XYF, webTokenUserNo, APP_XYF01, mobile);
                    return;
                }

                ImCustomerDetailsQueryResponse exitUdeskDetails = udeskClientService.getCustomerDetailsNew(UDESK_SEARCH_FIELD_WEB_TOKEN, webTokenUserNo);
                String nickName = "合并客户" + System.currentTimeMillis();
                if (Objects.nonNull(exitUdeskDetails) && Objects.nonNull(exitUdeskDetails.getCustomer()) && StringUtils.isNotBlank(exitUdeskDetails.getCustomer().getNickName())) {
                    nickName = exitUdeskDetails.getCustomer().getNickName();
                }
                String actualTargetUdeskWebTokenForXyf01;

                if (Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(RedisKeyConstants.WEB_TOKEN_SET_KEY, xyf01UserNoFromCIS))) {
                    actualTargetUdeskWebTokenForXyf01 = xyf01UserNoFromCIS; // Use actual token from Udesk
                    log.info("App '{}' (Source Udesk: {}): Found target Udesk customer for '{}' (web_token: {}).",
                            APP_XYF, webTokenUserNo, APP_XYF01, actualTargetUdeskWebTokenForXyf01);
                } else {
                    ImCustomerDetailsQueryResponse targetXyf01UdeskDetails = udeskClientService.getCustomerDetailsNew(UDESK_SEARCH_FIELD_WEB_TOKEN, xyf01UserNoFromCIS);
                    if (Objects.nonNull(targetXyf01UdeskDetails) && Objects.nonNull(targetXyf01UdeskDetails.getCustomer())) {
                        actualTargetUdeskWebTokenForXyf01 = targetXyf01UdeskDetails.getCustomer().getWebToken(); // Use actual token from Udesk
                        log.info("App '{}' (Source Udesk: {}): Found target Udesk customer for '{}' (web_token: {}).",
                                APP_XYF, webTokenUserNo, APP_XYF01, actualTargetUdeskWebTokenForXyf01);
                    } else {
                        log.info("App '{}' (Source Udesk: {}): Target Udesk customer for '{}' (web_token candidate: {}) not found. Creating it.",
                                APP_XYF, webTokenUserNo, APP_XYF01, xyf01UserNoFromCIS);
                        Customer newUdeskCustomer = new Customer();
                        newUdeskCustomer.setNickName(nickName);
                        newUdeskCustomer.setWebToken(xyf01UserNoFromCIS);
                        HashMap<String, Object> customFields = new HashMap<>();
                        customFields.put(UDESK_CUSTOM_FIELD_UID, xyf01UserNoFromCIS);
                        newUdeskCustomer.setCustomFields(customFields);
                        CreateCustomerResponse createResponse = udeskClientService.createCustomer(newUdeskCustomer);
                        if (Objects.nonNull(createResponse) && Objects.nonNull(createResponse.getCustomer()) && StringUtils.isNotBlank(createResponse.getCustomer().getWebToken())) {
                            actualTargetUdeskWebTokenForXyf01 = createResponse.getCustomer().getWebToken();
                            log.info("App '{}' (Source Udesk: {}): Successfully created target Udesk customer for '{}' with web_token: {}.",
                                    APP_XYF, webTokenUserNo, APP_XYF01, actualTargetUdeskWebTokenForXyf01);
                        } else {
                            throw new RuntimeException("Failed to create required target Udesk customer " + xyf01UserNoFromCIS);
                        }
                    }
                }


                if (StringUtils.isNotBlank(actualTargetUdeskWebTokenForXyf01)) {
                    log.info("App '{}' (Source Udesk: {}): Merging source Udesk customer {} (for '{}') into target Udesk customer {} (for '{}').",
                            APP_XYF, webTokenUserNo, webTokenUserNo, APP_XYF, actualTargetUdeskWebTokenForXyf01, APP_XYF01);
                    performUdeskMerge(webTokenUserNo, actualTargetUdeskWebTokenForXyf01); // from: xyf, to: xyf01
                }
            }
        } catch (Exception e) {
            log.error("Error processing merge task for webTokenUserNo: {}", webTokenUserNo, e);
            throw e;
        }
    }

    private void performUdeskMerge(String fromWebToken, String toWebToken) {
        if (StringUtils.isBlank(fromWebToken) || StringUtils.isBlank(toWebToken)) {
            log.error("Cannot merge: fromWebToken or toWebToken is blank. From: '{}', To: '{}'", fromWebToken, toWebToken);
            throw new IllegalArgumentException("fromWebToken or toWebToken cannot be blank for merging.");
        }
        if (fromWebToken.equals(toWebToken)) {
            log.warn("Attempted to merge Udesk customer with web_token {} into itself. Skipping merge operation.", fromWebToken);
            return;
        }
        log.info("Attempting to merge Udesk customer from web_token {} (source) to web_token {} (target)", fromWebToken, toWebToken);
        MergeCustomerResponse mergeResponse = udeskClientService.mergeCustomers(
                UDESK_SEARCH_FIELD_WEB_TOKEN, fromWebToken,
                UDESK_SEARCH_FIELD_WEB_TOKEN, toWebToken);

        if (Objects.nonNull(mergeResponse) && mergeResponse.getCode() == 1000) {
            log.info("Successfully merged Udesk customers: from {} to {}", fromWebToken, toWebToken);
            saveSuccess(fromWebToken, toWebToken);
        } else {
            throw new RuntimeException("Failed to merge Udesk customer from web_token " + fromWebToken +
                    " (source) to web_token " + toWebToken);
        }
    }

    private void saveSuccess(String fromWebToken, String toWebToken) {
        // a) 记录合并成功
        String mergedSetKey = RedisKeyConstants.MERGED_ZSET;
        redisTemplate.opsForSet().add(mergedSetKey, toWebToken + "<-" + fromWebToken);
        redisTemplate.expire(mergedSetKey, SUCCESS_TTL_DAYS, TimeUnit.DAYS);

        // b) 记录是否合并
        String flagKey = RedisKeyConstants.FLAG_KEY_PREFIX + toWebToken;
        redisTemplate.opsForValue().set(flagKey, "1", SUCCESS_TTL_DAYS, TimeUnit.DAYS);

    }
}
