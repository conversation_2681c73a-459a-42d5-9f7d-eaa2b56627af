/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy.impl;

import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.rr.CalculationContext;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.rr.response.ExemptionResponse;
import com.xinfei.vocmng.biz.strategy.ExemptionApplicationStrategy;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.itl.model.enums.SettleBaffleScene;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @version $ SettleFeeExemptionStrategy, v 0.1 2025-04-29 14:02 junjie.yan Exp $
 */
@Slf4j
public class SettleFeeExemptionStrategy implements ExemptionApplicationStrategy {

    // Settle Fee 对应 Fee4, 可以直接引用 setters
    private static final BiConsumer<ExemptionResponse, BigDecimal> lowerSetter = ExemptionResponse::setAdvSettLower;
    private static final BiConsumer<ExemptionResponse, BigDecimal> upperSetter = ExemptionResponse::setAdvSettUpper;

    private static final BiConsumer<ExemptionResponse, BigDecimal> maxSetter = ExemptionResponse::setAdvSettMax;

    @Override
    public void apply(CalculationContext context, ExemptionResponse exemption) {
        CalculateFee calculateFee = context.getCalculateFee();
//        PlanDetailDto initPlan = context.getInitPlan();
        BigDecimal actualValue = context.getValue(); // 实际传入的值
        BigDecimal totalRedDeduct = context.getTotalRedDeduct();
//        BigDecimal baseAmount = initPlan.getFee4Amt(); // Fee4Amt 对应 Settle_Fee
        FeeStrategyConfig config = context.getConfig();

        // --- 计算 Settle Fee 特有的 deductAmount 和 calculatedAmount (逻辑与之前相同) ---
        BigDecimal deductAmountRegular;
        BigDecimal calculatedAmountRegular;
        if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(calculateFee.getSettleBaffleScene())) {
            deductAmountRegular = totalRedDeduct.add(calculateFee.getRedTransFee4());
            calculatedAmountRegular = calculateFee.getAdvanceSettlementFee().add(calculateFee.getRedTransFee4());
        } else {
            deductAmountRegular = totalRedDeduct;
            calculatedAmountRegular = calculateFee.getAdvanceSettlementFee();
        }

        log.info(LogUtil.newInfoLog("减免计算过程", "loanNo", context.getCalculateFee().getLoanNo(),
                "费项：" + "结清费" +
                        " 减免百分比：" + actualValue +
                        " 历史减免：" + deductAmountRegular +
                        " 试算金额：" + calculatedAmountRegular +
                        " 还款引擎最大可减免：" + calculateFee.getCanDeductAmtDetail().getTransFee4()));

        // --- 1. 计算 Lower/Upper 使用 actualValue ---
        ControlRes<BigDecimal, BigDecimal> resultRegular = ReductionStrategyUtil.calculateSettleAmount(
                BigDecimal.ZERO,
                actualValue, // 使用实际传入的值
                deductAmountRegular,
                calculatedAmountRegular
        );

        // --- 2. 应用 Lower/Upper ---
        lowerSetter.accept(exemption, resultRegular.getLeft());
        if (calculateFee.getCanDeductAmtDetail().getTransFee4().compareTo(resultRegular.getRight()) > 0) {
            upperSetter.accept(exemption, resultRegular.getRight());
        } else {
            upperSetter.accept(exemption, calculateFee.getCanDeductAmtDetail().getTransFee4());
        }

        // --- 3. 计算 Max 使用 BigDecimal.ONE ---
        // 注意：这里需要确认计算 Max 时，deductAmount 和 calculatedAmount 是否也需要基于100%的逻辑重新计算
        // 假设它们保持不变，只改变输入的百分比 value
        if (config != null && config.getCanBreakOut() == 1) {
            ControlRes<BigDecimal, BigDecimal> resultMax = ReductionStrategyUtil.calculateSettleAmount(
                    BigDecimal.ZERO,
                    new BigDecimal("100"), // <--- 强制使用 100%
                    deductAmountRegular, // 假设这些值不变
                    calculatedAmountRegular // 假设这些值不变
            );

            // --- 4. 应用 Max ---
            maxSetter.accept(exemption, resultMax.getRight());
        }
    }

}