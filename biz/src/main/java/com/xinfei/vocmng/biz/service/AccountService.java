/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.*;
import com.xinfei.vocmng.dal.po.ControlAuth;
import com.xinfei.vocmng.dal.po.IssueCategoryConfig;
import com.xinfei.vocmng.dal.po.LabelCategoryConfig;
import com.xinfei.vocmng.dal.po.OperateLog;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 *  账号相关接口
 * <AUTHOR>
 * @version $ AccountService, v 0.1 2023/12/20 14:33 wancheng.qu Exp $
 */

public interface AccountService {


    List<DepartmentResp> queryDepartment(DepartmentReq departmentReq);

    Boolean updateDepartment(DepartmentReq departmentReq);

    Boolean addDepartment(DepartmentReq departmentReq);

    PageResultResponse<RoleResp> queryRole(RoleReq roleReq);

    List<FeeStrategyConfig> getFeeStrategyConfig();

    Boolean updateRole(RoleReq roleReq);

    Boolean addRole(RoleReq roleReq);

    List<ResourceResp> queryMenuList();

    List<DataAuthResp> queryDataList();

    PageResultResponse<EmployeeResp> queryEmployeeList(EmployeeReq employeeReq);

    Boolean addEmployee(EmployeeReq employeeReq);

    Boolean updateEmployee(EmployeeReq employeeReq);

    Boolean batchUpdateEmployee(EmployeeListReq employeeReq);

    void downloadEmp(HttpServletResponse httpServletResponse) ;

    Boolean uploadEmp(MultipartFile file) throws IOException;

    PageResultResponse<LabelResp> queryLabel(LabelReq labelReq);

    Boolean addLabel(LabelReq labelReq);

    Boolean updateLabel(LabelReq labelReq);

    PageResultResponse<LabelUserResp> queryLabelUser(LabelUserReq labelUserReq);

    Boolean updateLabelUser(LabelUserReq labelUserReq);

    void downloadLabel(HttpServletResponse httpServletResponse);

    Boolean uploadLabel(MultipartFile file) throws IOException;

    List<IssueCategoryConfig> queryIssList(IssueCategoryConfig req);

    LoginResp getUserResource();

    List<LabelCategoryConfig> queryLabelType();

    Boolean addOperateLog(OperateLog log );

    Boolean addUserLabel(LabelReq labelReq);

    List<ControlAuth> queryControlList();

    PageResultResponse<DictResp> queryDictList(DictReq req);

    Boolean opertaDict(DictResp req);

    LoginResp getUserInfo();

}