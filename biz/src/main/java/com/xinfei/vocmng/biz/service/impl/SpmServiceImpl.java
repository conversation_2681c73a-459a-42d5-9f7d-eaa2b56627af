/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.req.ListEntryReq;
import com.xinfei.vocmng.biz.model.req.UserConsultationReq;
import com.xinfei.vocmng.biz.service.OuterService;
import com.xinfei.vocmng.biz.service.SpmService;
import com.xinfei.vocmng.biz.util.RedissonLockUtils;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version $ SpmServiceImpl, v 0.1 2024/5/13 16:39 wancheng.qu Exp $
 */
@Slf4j
@Service
public class SpmServiceImpl implements SpmService {

    @Autowired
    private OuterService outerService;
    @Resource
    private LoginUserConfig loginUserConfig;

    private static final Executor spmExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(2000),
            new CustomizableThreadFactory("spmExecutor-"),
            new ThreadPoolExecutor.AbortPolicy()
    );

    /**
     * @param req:
     * @return Boolean
     * <AUTHOR>
     * 二期文档--> https://www.tapd.cn/60211538/prong/stories/view/1160211538001072854
     * @description 保存用户打点数据，根据规则判断用户是否是黑产，是黑产推送标签系统，规则如下：
     * x小时内，同一个ip/open_id/设备id，是否存在对应y个以上不同的userNo。
     * - 是，则将该ip或open_id认证为黑产，做加黑处理。
     * - 否，则暂时不出加黑。
     * 其中x和y均需要研发侧可配置。（不会频繁变动）
     * @date 2024/5/13 19:27
     */
    @Override
    public Boolean saveUserData(UserConsultationReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getUserNo())) {
            log.warn("saveUserData req is null");
            return Boolean.FALSE;
        }
        List<String> whiteList = loginUserConfig.getWhiteList();
        if (whiteList.contains(req.getIp()) || whiteList.contains(req.getDeviceId()) || whiteList.contains(req.getOpenId())) {
            log.info("req in white list,req={}",JsonUtil.toJson(req));
            return Boolean.FALSE;
        }
        log.info("saveUserData come req:{}", JsonUtil.toJson(req));
        CompletableFuture.runAsync(() -> updateCruiseCallTaskStatus(req), spmExecutor);
        return Boolean.TRUE;
    }

    /**
     * @param req:
     * @return void
     * <AUTHOR>
     * @description {
     * "idCard": "身份证号",
     * "userNo": "credit_user_id",
     * "mobile": "手机号",
     * "ip": "ip",
     * "device_id": "device_id",
     * "custNo": "客户号",
     * "open_id": "open_id"
     * }
     * @date 2024/5/14 10:31
     */
    private void updateCruiseCallTaskStatus(UserConsultationReq req) {
        List<ListEntryReq> lq = new ArrayList<>();
        List<String> adviceFlag = loginUserConfig.getAdviceFlag();
        if (adviceFlag.contains("ip")) {
            recordAndCheckIfBlack(req, "ip", req.getIp(), "ip", lq, getIpLoginCount(req.getCustNo()), loginUserConfig.getTimeRangeHours());
        }
        if (adviceFlag.contains("device")) {
            recordAndCheckIfBlack(req, "deviceid", req.getDeviceId(), "device_id", lq, getDeviceidLoginCount(req.getCustNo()), loginUserConfig.getDeviceTimeRangeHours());
        }
        if (adviceFlag.contains("openid")) {
            recordAndCheckIfBlack(req, "openid", req.getOpenId(), "open_id", lq, getOpenIdLoginCount(req.getCustNo()), loginUserConfig.getOpenIdTimeRangeHours());
        }

        if (!lq.isEmpty()) {
            outerService.sendList(lq);
        }
    }

    private int getIpLoginCount(String custNo) {
        return StringUtils.isBlank(custNo)?loginUserConfig.getLoginCount():loginUserConfig.getCustNoLoginCount();
    }
    private int getDeviceidLoginCount(String custNo) {
        return StringUtils.isBlank(custNo)?loginUserConfig.getDeviceLoginCount():loginUserConfig.getCustNodeviceLoginCount();
    }
    private int getOpenIdLoginCount(String custNo) {
        return StringUtils.isBlank(custNo)?loginUserConfig.getOpenIdLoginCount():loginUserConfig.getCustOpenIdLoginCount();
    }

    private void recordAndCheckIfBlack(UserConsultationReq req, String keyPrefix, String value, String field, List<ListEntryReq> lq, int count, long hours) {
        boolean flag;
        if (StringUtils.isNotBlank(value)) {
            if (StringUtils.isNotBlank(req.getCustNo())) {
                String cacheKey = LoginUserConstants.NEW_CUST_BLACK_CODE_KEY + keyPrefix + ":" + value;
                flag = recordRequest(req.getCustNo(), cacheKey, count, hours);
            } else {
                String cacheKey = LoginUserConstants.NEW_BLACK_CODE_KEY + keyPrefix + ":" + value;
                flag = recordRequest(req.getUserNo().toString(), cacheKey, count, hours);
            }
            if (flag) {
                log.info("Found Black user,user:{}, dimensionality Key: {}",JsonUtil.toJson(req), keyPrefix+"-->"+value);
                lq.add(new ListEntryReq(field, value));
            }
        }
    }

    public boolean recordRequest(String userNo, String key, int count, long hours) {
        String detailData = createDetailData(userNo);

        RMap<String, Set<String>> innerMap = RedissonLockUtils.getCacheMapKeySet(key);
        Set<String> userNos = innerMap.getOrDefault(userNo, new HashSet<>());
        userNos.add(detailData);
        innerMap.put(userNo, userNos);
        innerMap.expire(Duration.ofDays(8));
        long currentTimeMillis = System.currentTimeMillis();
        long timeRangeMillis = TimeUnit.HOURS.toMillis(hours);
        innerMap.keySet().forEach(user -> {
            Set<String> data = innerMap.get(user);
            if (data != null) {
                data.removeIf(userData -> {
                    String[] parts = userData.split(":");
                    if (parts.length == 2) {
                        long entryTimestamp = Long.parseLong(parts[1]);
                        return currentTimeMillis - entryTimestamp > timeRangeMillis;
                    }
                    return false;
                });
                innerMap.put(user, data);
            }
        });

        int uniqueUserCount = (int) innerMap.keySet().stream()
                .filter(user -> !innerMap.getOrDefault(user, Collections.emptySet()).isEmpty())
                .count();

        return uniqueUserCount >= count;
    }


    private String createDetailData(String userNo) {
        LocalDateTime roundedHour = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
        long hourTimestampMillis = roundedHour.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        return userNo + ":" + hourTimestampMillis;
    }

}