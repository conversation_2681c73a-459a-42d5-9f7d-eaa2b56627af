package com.xinfei.vocmng.biz.constants;

/**
 * UDesk相关常量
 *
 * <AUTHOR>
 * @version $ UDeskConstants, v 0.1 2025/08/08 UDeskConstants Exp $
 */
public class UDeskConstants {

    /**
     * 资方编码
     */
    public static class FunderCode {
        /** 海尔消金 */
        public static final String HAIER = "HAIER";
        /** 其他资方可以继续添加 */
    }

    /**
     * 事件类型
     */
    public static class EventType {
        /** 接线成功 */
        public static final String AGENT_INCALL = "agent_incall";
        /** 客户应答 */
        public static final String CUSTOMER_ANSWER = "customer_answer";
        /** 挂断 */
        public static final String HANGUP = "hangup";
    }

    /**
     * 失败原因类型
     */
    public static class FailureReason {
        /** 超时 */
        public static final String TIMEOUT = "timeout";
        /** 系统错误 */
        public static final String SYSTEM_ERROR = "system_error";
        /** 网络错误 */
        public static final String NETWORK_ERROR = "network_error";
        /** 客户拒接 */
        public static final String CUSTOMER_REJECT = "customer_reject";
        /** 坐席忙 */
        public static final String AGENT_BUSY = "agent_busy";
    }

    /**
     * 客户等级
     */
    public static class CustomerLevel {
        /** 普通 */
        public static final String NORMAL = "NORMAL";
        /** 银卡 */
        public static final String SILVER = "SILVER";
        /** 金卡 */
        public static final String GOLD = "GOLD";
        /** 白金卡 */
        public static final String PLATINUM = "PLATINUM";
    }

    /**
     * 风险等级
     */
    public static class RiskLevel {
        /** 低风险 */
        public static final String LOW = "LOW";
        /** 中风险 */
        public static final String MEDIUM = "MEDIUM";
        /** 高风险 */
        public static final String HIGH = "HIGH";
    }

    /**
     * 客户状态
     */
    public static class CustomerStatus {
        /** 活跃 */
        public static final String ACTIVE = "ACTIVE";
        /** 非活跃 */
        public static final String INACTIVE = "INACTIVE";
        /** 黑名单 */
        public static final String BLACKLIST = "BLACKLIST";
    }

    /**
     * 缓存相关
     */
    public static class Cache {
        /** 缓存键前缀 */
        public static final String CALL_INFO_PREFIX = "udesk:call:";
        /** 缓存过期时间（分钟） */
        public static final int EXPIRE_MINUTES = 30;
    }
}
