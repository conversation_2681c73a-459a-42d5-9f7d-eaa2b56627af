/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.google.common.collect.Lists;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.mapstruct.CouponConverter;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.UserCouponDetailReq;
import com.xinfei.vocmng.biz.rr.request.UserCouponReq;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.CouponService;
import com.xinfei.vocmng.itl.client.http.CouponClient;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/8/12 18:45
 * 优惠券 CouponServiceImpl
 */
@Slf4j
@Service
public class CouponServiceImpl implements CouponService {

    @Resource
    private CouponClient couponClient;

    @Override
    public ApiResponse<Paging<UserCouponDto>> getUserCoupon(UserCouponReq request) {
        try {
            CouponRequest<CouponRecordsRequest> baseRequest = buildUserCouponRecordsRequest(request);
            CouponResponse<PageResponse> response = couponClient.getUserCoupon(baseRequest);
            if (!response.isSuccess()) {
                log.error("query UserCoupon list failed, request={},response={}", request, response);
                return ApiResponse.fail("query UserCoupon list failed.");
            }

            return ApiResponse.success(convertUserCouponRecords(response.getResponse(), request));
        } catch (Exception e) {
            log.error("query UserCoupon  failed, request=" + request, e);
        }
        return ApiResponse.fail("query UserCoupon  failed.");
    }

    @Override
    public ApiResponse<List<UserCouponDetailDto>> getUserCouponDetail(UserCouponDetailReq request) {
        try {
            List<UserCouponDetailDto> userCouponDetailDtoList = request.getCouponIdList().stream()
                    .map(couponId -> getCouponDetail(request.getUserNo(), couponId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return ApiResponse.success(userCouponDetailDtoList);
        } catch (Exception e) {
            log.error("query getUserCouponDetail  failed, request=" + request, e);
        }
        return ApiResponse.fail("query getUserCouponDetail  failed.");
    }

    private UserCouponDetailDto getCouponDetail(Long userNo, Integer couponId) {
        try {
            CouponRequest<CouponDetailRequest> baseRequest = buildUserCouponDetailRequest(userNo, couponId);
            publicParameter(baseRequest);
            CouponResponse<CouponDetail> response = couponClient.getUserDetail(baseRequest);
            if (!response.isSuccess()) {
                log.error("Failed to get coupon detail, userNo={}, couponId={}, response={}",
                        userNo, couponId, response);
                return null;
            }
            return convertUserCouponDetail(response.getResponse());
        } catch (Exception e) {
            log.error("Error getting coupon detail, userNo={}, couponId={}", userNo, couponId, e);
            return null;
        }
    }

    private CouponRequest<CouponRecordsRequest> buildUserCouponRecordsRequest(UserCouponReq request) {
        CouponRequest<CouponRecordsRequest> result = new CouponRequest<>();
        CouponRecordsRequest couponRecordsRequest = new CouponRecordsRequest();
        couponRecordsRequest.setPage(request.getCurrentPage());
        couponRecordsRequest.setPage_size(request.getPageSize());
        couponRecordsRequest.setUser_id(request.getUserNo());
        couponRecordsRequest.setStatus(request.getStatus());
        result.setArgs(couponRecordsRequest);
        publicParameter(result);
        return result;
    }

    private CouponRequest<CouponDetailRequest> buildUserCouponDetailRequest(Long userNo,Integer couponId) {
        CouponRequest<CouponDetailRequest> result = new CouponRequest<>();
        CouponDetailRequest couponRecordsRequest = new CouponDetailRequest();
        couponRecordsRequest.setUser_id(userNo);
        couponRecordsRequest.setCoupon_id(couponId);
        result.setArgs(couponRecordsRequest);
        return result;
    }

    private static void publicParameter(CouponRequest result) {
        result.setUa(VocConstants.APP_NAME);
        result.setSign(VocConstants.APP_NAME);
        result.setTimestamp(System.currentTimeMillis());
    }

    private Paging<UserCouponDto> convertUserCouponRecords(PageResponse pageResponse, UserCouponReq request) {
        if (CollectionUtils.isEmpty(pageResponse.getList())) {
            return null;
        }

        Paging<UserCouponDto> result = new Paging<>();
        List<UserCouponDto> userCouponDtoList = Lists.newArrayListWithCapacity(pageResponse.getList().size());
        for (CouponRecordDetail detail : pageResponse.getList()) {
            UserCouponDto userSearchDTO = CouponConverter.INSTANCE.CouponRecordToUserCouponDto(detail);
            //减免科目：1 手续费，2 权益费，3通用 ,4:息费 5 免息券
            if ("1".equals(detail.getDiscountCategory())) {
                userSearchDTO.setDeductionUseDetailAmt(new BigDecimal(detail.getDiscountAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP));
            } else if ("2".equals(detail.getDiscountCategory())) {
                userSearchDTO.setDeductionRightsAmt(new BigDecimal(detail.getDiscountAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP));
            }
            userCouponDtoList.add(userSearchDTO);
        }
        result.setList(userCouponDtoList);
        result.setPageSize(pageResponse.getPage_info().getPage_size());
        result.setTotal(pageResponse.getPage_info().getCount());
        result.setTotalPage(pageResponse.getPage_info().getPage());
        result.setCurrentPage(request.getCurrentPage());
        return result;
    }

    private UserCouponDetailDto convertUserCouponDetail(CouponDetail couponDetail) {
        if (couponDetail == null) {
            return null;
        }
        UserCouponDetailDto userCouponDetailDto = CouponConverter.INSTANCE.CouponDetailToUserCouponDetailDto(couponDetail);
        if (!CollectionUtils.isEmpty(couponDetail.getPeriod_info())) {
            userCouponDetailDto.setPeriodInfo(periodCount(couponDetail));
        }
        userCouponDetailDto.setDiscountAmount(userCouponDetailDto.getDiscountAmount().divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP));
        userCouponDetailDto.setDescList(couponDetail.getDesc());

        return userCouponDetailDto;
    }

    private String periodCount(CouponDetail couponDetail) {
        //计算优惠券可用期数
        List<PeriodInfo> periodInfoList = couponDetail.getPeriod_info();
        Collections.sort(periodInfoList, Comparator.comparingInt(PeriodInfo::getCouponPeriodSpecific));
        StringBuilder result = new StringBuilder();
        Map<Integer, List<Integer>> grouped = periodInfoList.stream()
                .collect(Collectors.groupingBy(
                        PeriodInfo::getCouponPeriod,
                        Collectors.mapping(PeriodInfo::getCouponPeriodSpecific, Collectors.toList())
                ));

        grouped.forEach((period, specifics) -> {
            Collections.sort(specifics);
            result.append(period + " 期产品可用【" + formatRanges(specifics) + " 期】\n");
        });
        return result.toString();
    }

    private static String formatRanges(List<Integer> specifics) {
        StringBuilder result = new StringBuilder();
        int start = specifics.get(0);
        int end = specifics.get(0);

        for (int i = 1; i < specifics.size(); i++) {
            if (specifics.get(i) == end + 1) {
                end = specifics.get(i);
            } else {
                if (start == end) {
                    result.append(start).append(" ");
                } else {
                    result.append(start).append("-").append(end).append(" ");
                }
                start = specifics.get(i);
                end = specifics.get(i);
            }
        }

        if (start == end) {
            result.append(start);
        } else {
            result.append(start).append("-").append(end);
        }

        return result.toString().trim();
    }
}