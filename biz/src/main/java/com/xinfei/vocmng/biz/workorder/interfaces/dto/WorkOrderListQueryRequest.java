package com.xinfei.vocmng.biz.workorder.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单列表查询请求
 * 简化查询条件：转接资方、客户来电号码、是否创建工单、来电时间
 *
 * <AUTHOR>
 * @version $ WorkOrderListQueryRequest, v 0.1 2025/08/11 WorkOrderListQueryRequest Exp $
 */
@Data
@ApiModel("工单列表查询请求")
public class WorkOrderListQueryRequest {

    @ApiModelProperty("转接资方列表")
    private List<String> funderCodes;

    @ApiModelProperty("客户来电号码")
    private String customerPhone;

    @ApiModelProperty("是否创建工单（true-已创建工单，false-未创建工单，null-全部）")
    private Boolean hasWorkOrder;

    @ApiModelProperty("来电时间开始")
    private LocalDateTime callTimeStart;

    @ApiModelProperty("来电时间结束")
    private LocalDateTime callTimeEnd;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("页大小")
    private Integer pageSize = 20;

    @ApiModelProperty("排序字段")
    private String orderBy = "call_time";

    @ApiModelProperty("排序方向：ASC/DESC")
    private String orderDirection = "DESC";
}
