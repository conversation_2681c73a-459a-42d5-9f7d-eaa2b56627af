/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.EmployeeRoleMappingService;
import com.xinfei.vocmng.dal.mapper.EmployeeRoleMappingMapper;
import com.xinfei.vocmng.dal.po.EmployeeRoleMapping;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ EmployeeRoleMappingServiceImpl, v 0.1 2023/12/28 16:15 wancheng.qu Exp $
 */

@Service
public class EmployeeRoleMappingServiceImpl extends BaseService<EmployeeRoleMappingMapper, EmployeeRoleMapping> implements EmployeeRoleMappingService {

    @Override
    @Transactional
    public void saveBaths(List<EmployeeRoleMapping> mappingBatch) {
        saveBatch(mappingBatch);
    }
}