/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.component;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $ CacheDataLoader, v 0.1 2024/4/10 16:51 wancheng.qu Exp $
 */

@Component
public class CacheDataLoader implements ApplicationListener<ApplicationReadyEvent> {

    private final DictDataCache dictDataCache;

    public CacheDataLoader(DictDataCache dictDataCache) {
        this.dictDataCache = dictDataCache;
    }

    @Override
    public void onApplicationEvent(@NotNull ApplicationReadyEvent event) {
        dictDataCache.loadCacheData();
        dictDataCache.scheduleCacheRefresh();
    }
}
