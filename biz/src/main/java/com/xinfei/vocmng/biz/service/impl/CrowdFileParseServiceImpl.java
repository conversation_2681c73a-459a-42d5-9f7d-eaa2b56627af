package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.component.DataInsightOssClient;
import com.xinfei.vocmng.biz.rr.dto.CrowdLabelDto;
import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;
import com.xinfei.vocmng.biz.service.CrowdFileParseService;
import com.xinfei.vocmng.dal.po.CrowdLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人群文件解析服务实现
 *
 * <AUTHOR>
 * @version $ CrowdFileParseServiceImpl, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Service
@Slf4j
public class CrowdFileParseServiceImpl implements CrowdFileParseService {

    private static final String HEADER_DELIMITER = "|#DI_HEADER#|";
    private static final String FOOTER_DELIMITER = "|#DI_FOOTER#|";
    private static final String FIELD_SEPARATOR = ",";

    @Autowired
    private DataInsightOssClient dataInsightOssClient;

    @Override
    public List<CrowdUserDataDto> parseFile(String ossPath, String fileName) {
        List<CrowdUserDataDto> result = new ArrayList<>();

        try {

            // 读取文件内容
            List<String> lines = dataInsightOssClient.readFileLines(ossPath, fileName);
            if (CollectionUtils.isEmpty(lines)) {
                log.warn("文件内容为空: {}/{}", ossPath, fileName);
                return result;
            }

            // 解析文件
            String[] headers = null;
            for (int i = 0; i < lines.size(); i++) {
                String line = lines.get(i);

                if (StringUtils.isBlank(line)) {
                    continue;
                }

                // 第一行是表头
                if (i == 0) {
                    headers = parseHeaders(line);
                    if (headers == null) {
                        log.error("解析表头失败: {}", line);
                        return result;
                    }
                    continue;
                }

                // 解析数据行
                CrowdUserDataDto userData = parseDataLine(line, headers);
                if (userData != null) {
                    result.add(userData);
                }
            }

            log.info("成功解析人群文件，共{}条用户数据", result.size());

        } catch (Exception e) {
            log.error("解析人群文件失败: {}/{}", ossPath, fileName, e);
            throw new RuntimeException("解析人群文件失败", e);
        }

        return result;
    }

    @Override
    public List<CrowdLabelDto> parseCrowdLabelFile(String ossPath, String fileName) {
        List<CrowdLabelDto> result = new ArrayList<>();

        try {

            // 读取文件内容
            List<String> lines = dataInsightOssClient.readFileLines(ossPath, fileName);
            if (CollectionUtils.isEmpty(lines)) {
                log.warn("文件内容为空: {}/{}", ossPath, fileName);
                return result;
            }

            // 解析文件
            String[] headers = null;
            for (int i = 0; i < lines.size(); i++) {
                String line = lines.get(i);

                if (StringUtils.isBlank(line)) {
                    continue;
                }

                // 第一行是表头
                if (i == 0) {
                    headers = parseHeaders(line);
                    if (headers == null) {
                        log.error("解析表头失败: {}", line);
                        return result;
                    }
                    continue;
                }

                // 解析数据行
                CrowdLabelDto userData = parseCrowdLabelDataLine(line, headers);
                if (userData != null) {
                    result.add(userData);
                }
            }

            log.info("成功解析人群文件，共{}条用户数据", result.size());

        } catch (Exception e) {
            log.error("解析人群文件失败: {}/{}", ossPath, fileName, e);
            throw new RuntimeException("解析人群文件失败", e);
        }

        return result;
    }

    /**
     * 解析表头
     */
    private String[] parseHeaders(String headerLine) {
        try {
            String[] fields = headerLine.split(FIELD_SEPARATOR);
            if (fields.length < 2) {
                log.error("表头格式错误，字段数量不足: {}", headerLine);
                return null;
            }

            // 去除前后空格
            for (int i = 0; i < fields.length; i++) {
                fields[i] = fields[i].trim();
            }

            return fields;
        } catch (Exception e) {
            log.error("解析表头异常: {}", headerLine, e);
            return null;
        }
    }

    /**
     * 解析数据行
     */
    private CrowdUserDataDto parseDataLine(String dataLine, String[] headers) {
        try {
            // 检查是否包含分隔符
            if (!dataLine.contains(HEADER_DELIMITER) || !dataLine.contains(FOOTER_DELIMITER)) {
                log.warn("数据行格式错误，缺少分隔符: {}", dataLine);
                return null;
            }

            String[] fields = dataLine.split(FIELD_SEPARATOR);
            if (fields.length != headers.length) {
                log.warn("数据行字段数量与表头不匹配: 期望{}, 实际{}, 数据: {}",
                        headers.length, fields.length, dataLine);
                return null;
            }

            CrowdUserDataDto userDataDto = new CrowdUserDataDto();

            // 根据表头映射字段
            for (int i = 0; i < headers.length; i++) {
                String header = headers[i];
                String value = fields[i].trim();

                // 移除分隔符
                if (value.startsWith(HEADER_DELIMITER)) {
                    value = value.substring(HEADER_DELIMITER.length());
                }
                if (value.endsWith(FOOTER_DELIMITER)) {
                    value = value.substring(0, value.length() - FOOTER_DELIMITER.length());
                }

                // 映射字段
                mapFieldToDto(userDataDto, header, value);
            }

            // 验证必要字段
            if (StringUtils.isBlank(userDataDto.getUserNo())
                    || CollectionUtils.isEmpty(userDataDto.getWorkOrderNoList())
                    || Objects.isNull(userDataDto.getRefundType())
                    || Objects.isNull(userDataDto.getVipOrderNo())) {
                log.warn("必传字段为空，跳过该条数据: {}", dataLine);
                return null;
            }

            return userDataDto;

        } catch (Exception e) {
            log.error("解析数据行异常: {}", dataLine, e);
            return null;
        }
    }

    /**
     * 解析数据行
     */
    private CrowdLabelDto parseCrowdLabelDataLine(String dataLine, String[] headers) {
        try {
            // 检查是否包含分隔符
            if (!dataLine.contains(HEADER_DELIMITER) || !dataLine.contains(FOOTER_DELIMITER)) {
                log.warn("数据行格式错误，缺少分隔符: {}", dataLine);
                return null;
            }

            String[] fields = dataLine.split(FIELD_SEPARATOR);
            if (fields.length != headers.length) {
                log.warn("数据行字段数量与表头不匹配: 期望{}, 实际{}, 数据: {}",
                        headers.length, fields.length, dataLine);
                return null;
            }

            CrowdLabelDto crowdLabel = new CrowdLabelDto();

            // 根据表头映射字段
            for (int i = 0; i < headers.length; i++) {
                String header = headers[i];
                String value = fields[i].trim();

                // 移除分隔符
                if (value.startsWith(HEADER_DELIMITER)) {
                    value = value.substring(HEADER_DELIMITER.length());
                }
                if (value.endsWith(FOOTER_DELIMITER)) {
                    value = value.substring(0, value.length() - FOOTER_DELIMITER.length());
                }

                // 映射字段
                mapFieldToDto(crowdLabel, header, value);

            }

            // 验证必要字段
            if (StringUtils.isBlank(crowdLabel.getCustNo()) && StringUtils.isBlank(crowdLabel.getUserNo())) {
                log.warn("必传字段为空，跳过该条数据: {}", dataLine);
                return null;
            }

            return crowdLabel;

        } catch (Exception e) {
            log.error("解析数据行异常: {}", dataLine, e);
            return null;
        }
    }

    /**
     * 映射字段到DTO
     */
    private void mapFieldToDto(CrowdUserDataDto dto, String header, String value) {
        switch (header) {
            case "user_no":
                dto.setUserNo(value);
                break;
            case "if_pay_success":
                dto.setRefundType(Integer.valueOf(value));
                break;
            case "svc_vip_order_id":
                dto.setVipOrderNo(Long.valueOf(value));
                break;
            case "work_task_id":
                // 解析分号分隔的工单号字符串为List<Long>
                dto.setWorkOrderNoList(parseWorkOrderNoList(value));
                break;
            default:
                // 忽略未知字段
                break;
        }
    }

    /**
     * 映射字段到DTO
     */
    private void mapFieldToDto(CrowdLabelDto dto, String header, String value) {
        switch (header) {
            case "cust_no":
                dto.setCustNo(value);
                break;
            case "user_no":
                dto.setUserNo(value);
                break;
            case "op_type":
                dto.setOpType(value);
                break;
            default:
                // 忽略未知字段
                break;
        }
    }

    /**
     * 解析工单号列表
     * 将分号分隔的工单号字符串解析为List<Long>
     */
    private List<Long> parseWorkOrderNoList(String workOrderNoStr) {
        if (StringUtils.isBlank(workOrderNoStr)) {
            return new ArrayList<>();
        }

        try {
            return Arrays.stream(workOrderNoStr.split(";"))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.error("解析工单号列表失败，原始字符串: {}", workOrderNoStr, e);
            return new ArrayList<>();
        }
    }
}
