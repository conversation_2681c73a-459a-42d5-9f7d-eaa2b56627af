package com.xinfei.vocmng.biz.workorder.infrastructure.converter;

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderDataEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEventEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.*;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.dal.po.WorkOrderMiddlewarePO;
import com.xinfei.vocmng.dal.po.WorkOrderDataPO;
import com.xinfei.vocmng.dal.po.WorkOrderEventPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.apache.commons.lang3.StringUtils;

/**
 * 工单转换器 - MapStruct实现
 * 负责实体与PO之间的转换
 *
 * <AUTHOR>
 * @version $ WorkOrderMapStructConverter, v 0.1 2025/08/11 WorkOrderMapStructConverter Exp $
 */
@Mapper(componentModel = "spring")
public interface WorkOrderMapStructConverter {
    
    WorkOrderMapStructConverter INSTANCE = Mappers.getMapper(WorkOrderMapStructConverter.class);

    // ==================== WorkOrderEntity 转换 ====================

    /**
     * WorkOrderEntity 转 WorkOrderMiddlewarePO
     */
    @Mapping(target = "orderType", source = "orderType", qualifiedByName = "orderTypeToCode")
    @Mapping(target = "priority", source = "priority", qualifiedByName = "priorityToLevel")
    @Mapping(target = "status", source = "status", qualifiedByName = "statusToCode")
    @Mapping(target = "callTime", source = "callTime")
    WorkOrderMiddlewarePO entityToPO(WorkOrderEntity entity);

    /**
     * WorkOrderMiddlewarePO 转 WorkOrderEntity
     */
    @Mapping(target = "orderType", source = "orderType", qualifiedByName = "codeToOrderType")
    @Mapping(target = "priority", source = "priority", qualifiedByName = "levelToPriority")
    @Mapping(target = "status", source = "status", qualifiedByName = "codeToStatus")
    @Mapping(target = "callTime", source = "callTime")
    WorkOrderEntity poToEntity(WorkOrderMiddlewarePO po);

    // ==================== WorkOrderDataEntity 转换 ====================

    /**
     * WorkOrderDataEntity 转 WorkOrderDataPO
     */
    @Mapping(target = "customerInfo", source = "customerInfo", qualifiedByName = "customerInfoToJson")
    @Mapping(target = "businessData", source = "businessData", qualifiedByName = "businessDataToJson")
    @Mapping(target = "extendData", source = "extendData", qualifiedByName = "extendDataToJson")
    WorkOrderDataPO dataEntityToPO(WorkOrderDataEntity entity);

    /**
     * WorkOrderDataPO 转 WorkOrderDataEntity
     */
    @Mapping(target = "customerInfo", source = "customerInfo", qualifiedByName = "jsonToCustomerInfo")
    @Mapping(target = "businessData", source = "businessData", qualifiedByName = "jsonToBusinessData")
    @Mapping(target = "extendData", source = "extendData", qualifiedByName = "jsonToExtendData")
    WorkOrderDataEntity poToDataEntity(WorkOrderDataPO po);

    // ==================== WorkOrderEventEntity 转换 ====================

    /**
     * WorkOrderEventEntity 转 WorkOrderEventPO
     */
    @Mapping(target = "eventType", source = "eventType", qualifiedByName = "eventTypeToCode")
    @Mapping(target = "status", source = "status", qualifiedByName = "eventStatusToCode")
    @Mapping(target = "eventData", source = "eventData", qualifiedByName = "eventDataToJson")
    WorkOrderEventPO eventEntityToPO(WorkOrderEventEntity entity);

    /**
     * WorkOrderEventPO 转 WorkOrderEventEntity
     */
    @Mapping(target = "eventType", source = "eventType", qualifiedByName = "codeToEventType")
    @Mapping(target = "status", source = "status", qualifiedByName = "codeToEventStatus")
    @Mapping(target = "eventData", source = "eventData", qualifiedByName = "jsonToEventData")
    WorkOrderEventEntity poToEventEntity(WorkOrderEventPO po);



    // ==================== 枚举转换方法 ====================

    /**
     * WorkOrderType 转 Code
     */
    @Named("orderTypeToCode")
    default String orderTypeToCode(WorkOrderType orderType) {
        return orderType != null ? orderType.getCode() : null;
    }

    /**
     * Code 转 WorkOrderType
     */
    @Named("codeToOrderType")
    default WorkOrderType codeToOrderType(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        try {
            return WorkOrderType.fromCode(code);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Priority 转 Level
     */
    @Named("priorityToLevel")
    default Integer priorityToLevel(Priority priority) {
        return priority != null ? priority.getLevel() : null;
    }

    /**
     * Level 转 Priority
     */
    @Named("levelToPriority")
    default Priority levelToPriority(Integer level) {
        if (level == null) {
            return null;
        }
        try {
            return Priority.fromLevel(level);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * WorkOrderStatus 转 Code
     */
    @Named("statusToCode")
    default String statusToCode(WorkOrderStatus status) {
        return status != null ? status.getCode() : null;
    }

    /**
     * Code 转 WorkOrderStatus
     */
    @Named("codeToStatus")
    default WorkOrderStatus codeToStatus(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        try {
            return WorkOrderStatus.fromCode(code);
        } catch (Exception e) {
            return null;
        }
    }

    // ==================== JSON转换方法 ====================

    /**
     * CustomerInfo 转 JSON
     */
    @Named("customerInfoToJson")
    default String customerInfoToJson(CustomerInfo customerInfo) {
        if (customerInfo == null) {
            return null;
        }
        try {
            return JSON.toJSONString(customerInfo);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * JSON 转 CustomerInfo
     */
    @Named("jsonToCustomerInfo")
    default CustomerInfo jsonToCustomerInfo(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, CustomerInfo.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * BusinessData 转 JSON
     */
    @Named("businessDataToJson")
    default String businessDataToJson(BusinessData businessData) {
        if (businessData == null) {
            return null;
        }
        try {
            return JSON.toJSONString(businessData);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * JSON 转 BusinessData
     */
    @Named("jsonToBusinessData")
    default BusinessData jsonToBusinessData(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, BusinessData.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * ExtendData 转 JSON
     */
    @Named("extendDataToJson")
    default String extendDataToJson(ExtendData extendData) {
        if (extendData == null || extendData.isEmpty()) {
            return null;
        }
        try {
            return JSON.toJSONString(extendData);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * JSON 转 ExtendData
     */
    @Named("jsonToExtendData")
    default ExtendData jsonToExtendData(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, ExtendData.class);
        } catch (Exception e) {
            return null;
        }
    }

    // ==================== 事件和预警相关转换方法 ====================

    /**
     * WorkOrderEventType 转 Code
     */
    @Named("eventTypeToCode")
    default String eventTypeToCode(WorkOrderEventType eventType) {
        return eventType != null ? eventType.getCode() : null;
    }

    /**
     * Code 转 WorkOrderEventType
     */
    @Named("codeToEventType")
    default WorkOrderEventType codeToEventType(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        try {
            return WorkOrderEventType.fromCode(code);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * EventProcessStatus 转 Code
     */
    @Named("eventStatusToCode")
    default String eventStatusToCode(EventProcessStatus status) {
        return status != null ? status.getCode() : null;
    }

    /**
     * Code 转 EventProcessStatus
     */
    @Named("codeToEventStatus")
    default EventProcessStatus codeToEventStatus(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        try {
            return EventProcessStatus.fromCode(code);
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * EventData Map 转 JSON
     */
    @Named("eventDataToJson")
    default String eventDataToJson(java.util.Map<String, Object> eventData) {
        if (eventData == null || eventData.isEmpty()) {
            return null;
        }
        try {
            return JSON.toJSONString(eventData);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * JSON 转 EventData Map
     */
    @Named("jsonToEventData")
    default java.util.Map<String, Object> jsonToEventData(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, java.util.Map.class);
        } catch (Exception e) {
            return null;
        }
    }

    // ==================== 兼容性方法 ====================

    /**
     * 兼容旧方法名
     */
    default WorkOrderMiddlewarePO toDataObject(WorkOrderEntity entity) {
        return entityToPO(entity);
    }
}
