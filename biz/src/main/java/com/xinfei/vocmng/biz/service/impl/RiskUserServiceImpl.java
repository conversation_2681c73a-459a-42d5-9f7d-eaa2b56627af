/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinfei.vocmng.biz.function.UserValidator;
import com.xinfei.vocmng.biz.mapstruct.RiskUserConverter;
import com.xinfei.vocmng.biz.rr.dto.bill.RiskUserDto;
import com.xinfei.vocmng.biz.service.RiskUserService;
import com.xinfei.vocmng.dal.mapper.EasyComplaintUserMapper;
import com.xinfei.vocmng.dal.mapper.RiskUserMapper;
import com.xinfei.vocmng.dal.po.EasyComplaintUser;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/7/4 17:24
 * RiskUserServiceImpl
 */
@Slf4j
@Service
public class RiskUserServiceImpl implements RiskUserService {

    @Resource
    private RiskUserMapper riskUserMapper;
    @Resource
    private CisFacadeClientService cisFacadeClientService;
    @Resource
    private CisFacadeClient cisFacadeClient;
    @Resource
    private EasyComplaintUserMapper easyComplaintUserMapper;


    @Override
    public RiskUserDto getRiskUser(String mobile) {
        RiskUserDto riskUserDto= RiskUserConverter.INSTANCE.riskUserToRiskUserDto(riskUserMapper.getRiskUser(mobile));
        return riskUserDto;
    }

    @Override
    public Boolean getComplainUser(String mobile) {
        return Optional.ofNullable(cisFacadeClientService.batchDecrypt(mobile))
                .filter(StringUtils::isNotBlank)
                .map(m -> cisFacadeClient.queryUserList(m, null, null, 1, 30))
                .filter(pageResult -> CollectionUtils.isNotEmpty(pageResult.getList()))
                .map(pageResult -> pageResult.getList().stream()
                        .map(UserSearchDTO::getCustNo)
                        .map(Object::toString)
                        .collect(Collectors.toList()))
                .filter(CollectionUtils::isNotEmpty)
                .map(custNos -> easyComplaintUserMapper.exists(
                        new LambdaQueryWrapper<EasyComplaintUser>()
                                .in(EasyComplaintUser::getCustNo, custNos)
                                .eq(EasyComplaintUser::getIsDeleted, 0)
                                .eq(EasyComplaintUser::getUserGroup, "groupA")
                ))
                .orElse(Boolean.FALSE);
    }

}