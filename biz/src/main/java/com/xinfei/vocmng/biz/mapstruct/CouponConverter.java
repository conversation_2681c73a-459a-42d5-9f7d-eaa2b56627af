/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vocmng.itl.rr.CouponDetail;
import com.xinfei.vocmng.itl.rr.CouponRecordDetail;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR> 2024/8/13 11:19
 * CouponConverter
 */
@Mapper
public interface CouponConverter {

    CouponConverter INSTANCE = Mappers.getMapper(CouponConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }


    /**
     * 优惠券列表请求
     *
     * @param couponRecordDetail
     * @return
     */


    UserCouponDto CouponRecordToUserCouponDto(CouponRecordDetail couponRecordDetail);

    /**
     * 优惠券详情请求
     *
     * @param couponDetail
     * @return
     */

    UserCouponDetailDto CouponDetailToUserCouponDetailDto(CouponDetail couponDetail);
}