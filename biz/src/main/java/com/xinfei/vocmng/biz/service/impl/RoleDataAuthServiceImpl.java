/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.RoleDataAuthService;
import com.xinfei.vocmng.dal.mapper.RoleDataAuthMappingMapper;
import com.xinfei.vocmng.dal.po.RoleDataAuthMapping;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ RoleDataAuthServiceImpl, v 0.1 2023/12/26 18:03 wancheng.qu Exp $
 */
@Service
public class RoleDataAuthServiceImpl extends BaseService<RoleDataAuthMappingMapper,RoleDataAuthMapping> implements RoleDataAuthService {

    @Override
    @Transactional
    public void saveBatchs(List<RoleDataAuthMapping> list) {
        saveBatch(list);
    }
}