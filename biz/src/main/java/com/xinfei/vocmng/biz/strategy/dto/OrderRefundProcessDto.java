/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy.dto;

import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.vocmng.biz.model.resp.RefundResp;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version $ OrderRefundProcessDto, v 0.1 2025-05-27 15:28 junjie.yan Exp $
 */

public class OrderRefundProcessDto {

    private static final String OUT_PRINCIPAL = "Out_Principal";
    private static final String OUT_INT_AMT = "Out_intAmt";
    private static final String OUT_FEE1_AMT = "Out_Fee1Amt";
    private static final String OUT_FEE2_AMT = "Out_Fee2Amt";
    private static final String OUT_FEE6_AMT = "Out_Fee6Amt";
    private static final String OUT_OINT_AMT = "Out_OintAmt";
    private static final String OUT_FEE3_AMT = "Out_Fee3Amt";
    private static final String OUT_SETTLE_FEE = "Out_Settle_Fee";

    public static final Map<String, FeeSubjectEnum> NEW_FEE_MAPPING = new HashMap<>();

    static {
        NEW_FEE_MAPPING.put(OUT_PRINCIPAL, FeeSubjectEnum.PRIN_AMT);
        NEW_FEE_MAPPING.put(OUT_INT_AMT, FeeSubjectEnum.INT_AMT);
        NEW_FEE_MAPPING.put(OUT_FEE1_AMT, FeeSubjectEnum.FEE1);
        NEW_FEE_MAPPING.put(OUT_FEE2_AMT, FeeSubjectEnum.FEE2);
        NEW_FEE_MAPPING.put(OUT_FEE6_AMT, FeeSubjectEnum.FEE6);
        NEW_FEE_MAPPING.put(OUT_OINT_AMT, FeeSubjectEnum.OINT_AMT);
        NEW_FEE_MAPPING.put(OUT_FEE3_AMT, FeeSubjectEnum.FEE3);
        NEW_FEE_MAPPING.put(OUT_SETTLE_FEE, FeeSubjectEnum.FEE4);
    }

    public static final Map<String, Function<RefundResp, BigDecimal>> FEE_AMOUNT_EXTRACTORS = new HashMap<>();

    static {
        FEE_AMOUNT_EXTRACTORS.put(OUT_PRINCIPAL, RefundResp::getPrinAmt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_INT_AMT, RefundResp::getIntAmt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_FEE1_AMT, RefundResp::getFee1Amt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_FEE2_AMT, RefundResp::getFee2Amt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_FEE6_AMT, RefundResp::getFee6Amt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_OINT_AMT, RefundResp::getOintAmt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_FEE3_AMT, RefundResp::getFee3Amt);
        FEE_AMOUNT_EXTRACTORS.put(OUT_SETTLE_FEE, RefundResp::getFee4Amt); // Out_Settle_Fee -> getFee4Amt
    }

}