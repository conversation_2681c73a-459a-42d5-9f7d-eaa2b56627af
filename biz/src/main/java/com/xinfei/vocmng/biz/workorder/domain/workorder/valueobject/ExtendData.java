package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 扩展数据值对象
 *
 * <AUTHOR>
 * @version $ ExtendData, v 0.1 2025/07/30 ExtendData Exp $
 */
@Data
@Builder
public class ExtendData {

    /**
     * 扩展属性Map
     */
    @Builder.Default
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 添加扩展属性
     */
    public void addAttribute(String key, Object value) {
        if (Objects.isNull(attributes)) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    /**
     * 获取扩展属性
     */
    public Object getAttribute(String key) {
        if (Objects.isNull(attributes)) {
            return null;
        }
        return attributes.get(key);
    }

    /**
     * 获取字符串类型扩展属性
     */
    public String getStringAttribute(String key) {
        Object value = getAttribute(key);
        return Objects.nonNull(value) ? value.toString() : null;
    }

    /**
     * 移除扩展属性
     */
    public void removeAttribute(String key) {
        if (Objects.nonNull(attributes)) {
            attributes.remove(key);
        }
    }

    /**
     * 是否包含指定属性
     */
    public boolean hasAttribute(String key) {
        return Objects.nonNull(attributes) && attributes.containsKey(key);
    }

    /**
     * 获取所有属性键
     */
    public java.util.Set<String> getAttributeKeys() {
        return Objects.nonNull(attributes) ? attributes.keySet() : java.util.Collections.emptySet();
    }

    /**
     * 清空所有扩展属性
     */
    public void clearAttributes() {
        if (Objects.nonNull(attributes)) {
            attributes.clear();
        }
    }

    /**
     * 是否为空
     */
    public boolean isEmpty() {
        return Objects.isNull(attributes) || attributes.isEmpty();
    }
}
