package com.xinfei.vocmng.biz.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.model.enums.*;
import com.xinfei.vocmng.biz.rr.dto.CustomerDetailDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerRequest;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.util.IDCardUtil;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.EmployeeDepartment;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.itl.constants.MQConstants;
import com.xinfei.vocmng.itl.rr.AgentsResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImSessionsResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsResponse;
import com.xinfei.vocmng.itl.rr.acsdatacore.ACSFollowData;
import com.xinfei.vocmng.itl.rr.acsdatacore.ACSPushData;
import com.xinfei.vocmng.itl.rr.acsdatacore.TextRecordListReq;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ ACSIMDataPushService, v 0.1 2025/3/5 17:16 shaohui.chen Exp $
 */
@Service
@Slf4j
public class ACSIMDataPushService extends AbstractDataPushService<ACSPushData, TextRecordListReq> {

    @Resource
    private UserLabelService userLabelService;
    @Override
    protected TextRecordListReq assemblePayload(ACSPushData data) {
        TextRecordListReq req = new TextRecordListReq();
        // 固定值
        req.setAppKey(vocConfig.getAcsAppKey());
        req.setSystemCode(vocConfig.getSystemCode());
        // ----------------------------
        // 1. 构造 SessionRecordRequest
        // ----------------------------
        ImSessionsResponse sessionResp = data.getImSessionsResponse();
        if (Objects.nonNull(sessionResp) && sessionResp.getSource().contains("微信")) {
            return null;
        }
        TextRecordListReq.SessionRecordRequest sessionReq = getSessionRecordRequest(sessionResp);
        req.setSessionRecordRequest(sessionReq);
        // ----------------------------
        // 2. 构造 TextRecordListReq.textRecordList
        // ----------------------------
        List<TextRecordListReq.TextRecordRequest> textRecordList = new ArrayList<>();
        List<ImSessionsDetailsResponse> detailsList = data.getImSessionsDetailsResponseList();
        if (Objects.nonNull(sessionResp) && Objects.nonNull(sessionResp.getSubSessionId())) {
            detailsList = detailsList.stream()
                    .filter(detail -> Objects.equals(detail.getSubSessionId(), sessionResp.getSubSessionId()))
                    .collect(Collectors.toList());
        } else {
            detailsList = Collections.emptyList();
        }
        if (CollectionUtils.isNotEmpty(detailsList)) {
            for (ImSessionsDetailsResponse detail : detailsList) {
                if (Objects.isNull(detail)) {
                    continue;
                }
                TextRecordListReq.TextRecordRequest record = new TextRecordListReq.TextRecordRequest();
                // appKey 固定
                record.setAppKey(vocConfig.getAcsAppKey());
                // createdTime：取自 created_at（LocalDateTime 转 Date）
                record.setCreatedTime(fromLocalDateTime(detail.getCreatedAt()));

                // sessionId：使用会话中的 session_id
                if (Objects.nonNull(detail.getSubSessionId())) {
                    record.setSessionId("#h" + convertToBase36(detail.getSubSessionId()));
                }
                // msgId：取 detail.id 转为 String
                record.setMsgId(Objects.nonNull(detail.getBizId()) ? detail.getBizId().toString() : null);
                // channel：固定值 "udesk"
                record.setChannel(VocConstants.UDesk);
                // agentId、agentName：使用会话中的客服信息
                record.setAgentId(Objects.nonNull(sessionResp.getAgentId()) ? String.valueOf(sessionResp.getAgentId()) : null);
                record.setAgentName(sessionResp.getAgentNickName());
                // sendType：根据 detail.sender 判断；sender 为 "customer" 则取 1（接收），为 "agent" 或 "sys" 则取 0（发送）
                if (StringUtils.isNotBlank(detail.getSender())) {
                    record.setSendType(UdeskSenderEnum.getMySideCode(detail.getSender()));
                }
                // sendTime：取自 detail.createdAt，转换为 Date
                record.setSendTime(fromLocalDateTime(detail.getCreatedAt()));
                // content：取自 detail.content
                if (StringUtils.isNotBlank(detail.getContent())) {
                    JSONObject outer = JSON.parseObject(detail.getContent());
                    String type = outer.getString("type");
                    record.setContentType(type);
                    String innerJson = outer.getString("data");
                    if (StringUtils.isNotBlank(innerJson)) {
                        JSONObject inner = JSON.parseObject(innerJson);
                        String content = inner.getString("content");
                        record.setContent(content);
                    }
                }
                // type：固定值 2 （udesk-IM内容）
                record.setType(2);
                // 将该记录添加到列表中
                textRecordList.add(record);
            }
        }
        req.setTextRecordList(textRecordList);

        return req;

    }

    @Override
    protected void sendData(TextRecordListReq payload) {
        log.info("send data to TextRecordListReq service, payload: {}", JsonUtil.toJson(payload));
        SendResult sendResult = mqTemplate.syncSend(MQConstants.ACS_DATA_CORE.TP_ACSDATACORE_TEXT_RECORD_MSG_TOPIC, payload);
        if (Objects.nonNull(sendResult) && Objects.equals(SendStatus.SEND_OK, sendResult.getSendStatus())) {
            log.info("send data to TextRecordListReq service success, msgId:{}", sendResult.getMsgId());
        } else {
            log.error("send data to TextRecordListReq service error, sendResult:{}", JsonUtil.toJson(sendResult));
        }
    }

    private Date fromLocalDateTime(LocalDateTime ldt) {
        return ldt == null ? null : Timestamp.valueOf(ldt);
    }

    private TextRecordListReq.SessionRecordRequest getSessionRecordRequest(ImSessionsResponse sessionResp) {
        TextRecordListReq.SessionRecordRequest sessionReq = new TextRecordListReq.SessionRecordRequest();
        if (Objects.nonNull(sessionResp)) {
            // sessionId：转换为字符串

            sessionReq.setSessionId(Objects.nonNull(sessionResp.getSubSessionId()) ? "#h" + convertToBase36(sessionResp.getSubSessionId()) : null);
            sessionReq.setStartTime(sessionResp.getCreatedAt());
            sessionReq.setEndTime(sessionResp.getClosedAt());
            sessionReq.setAgentName(sessionResp.getAgentNickName());
            sessionReq.setTotalCount(sessionResp.getConversationsNumToday());
            sessionReq.setAgentMsgCount(sessionResp.getAgentMsgNum());
            sessionReq.setCustomerMsgCount(sessionResp.getCustomerMsgNum());
            sessionReq.setBizExtInfo(buildFlowData(sessionResp, sessionReq));
            sessionReq.setCreateTime(sessionResp.getCreatedAt());
            sessionReq.setUpdateTime(sessionResp.getClosedAt());
        }
        return sessionReq;
    }

    private String buildFlowData(ImSessionsResponse session , TextRecordListReq.SessionRecordRequest sessionRecordRequest) {
        ACSFollowData acsFollowData = new ACSFollowData();
        // =========================
        //   1) 用户基本信息（客服系统）
        // =========================
        // 示例：如果有 customerId，可调用外部服务获取用户信息
        Long customerId = session.getCustomerId();
        String userName = StringUtils.EMPTY;
        String userNo = StringUtils.EMPTY;
        String encodePhone = StringUtils.EMPTY;
        String originalPhone = StringUtils.EMPTY;
        // 性别
        String gender = StringUtils.EMPTY;
        List<String> userTagUdeskLabelList = new ArrayList<>();
        // 注册APP、账号状态
        List<String> registerApp = new ArrayList<>();
        List<String> accountStatus = new ArrayList<>();
        List<String> userNoList = new ArrayList<>();
        if (Objects.nonNull(customerId)) {
            // 根据 customerId 查询用户姓名、用户编号
            ImCustomerDetailsQueryResponse imCustomerDetailsQueryResponse = udeskClientService.getCustomerDetailsWithOutException("id", String.valueOf(customerId));
            if (Objects.nonNull(imCustomerDetailsQueryResponse) && Objects.nonNull(imCustomerDetailsQueryResponse.getCustomer())) {
                // 获取用户标签
                if (CollectionUtils.isNotEmpty(imCustomerDetailsQueryResponse.getCustomer().getTags())) {
                    userTagUdeskLabelList.addAll(imCustomerDetailsQueryResponse.getCustomer()
                            .getTags()
                            .stream()
                            .map(ImCustomerDetailsResponse.Tag::getName)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList()));
                }
                // 获取用户自定义字段 uid 的值
                String uidValue = null;
                try {
                    String customFieldsStr = imCustomerDetailsQueryResponse.getCustomer().getCustomFields();
                    if (StringUtils.isNotBlank(customFieldsStr)) {
                        // 将String转换为Map
                        Map<String, Object> customFieldsMap = JSONObject.parseObject(customFieldsStr, new TypeReference<Map<String, Object>>() {
                        });
                        // 从转换后的Map中获取TextField_1500643的值
                        if (customFieldsMap != null && customFieldsMap.containsKey("TextField_1500643")) {
                            Object fieldValue = customFieldsMap.get("TextField_1500643");
                            uidValue = fieldValue != null ? fieldValue.toString() : null;
                            log.info("Found uid value: {}", uidValue);
                        }
                    }
                } catch (Exception e) {
                    log.error("Error parsing customFields string to map", e);
                }
                if (StringUtils.isNotBlank(uidValue)) {
                    userNo = uidValue;
                } else {
                    // 获取用户详细信息
                    userNo = imCustomerDetailsQueryResponse.getCustomer().getWebToken();
                }
                if (StringUtils.isNotBlank(userNo)) {
                    CustomerDetailDto customerDetail = customerService
                            .getCustomerDetail(GetCustomerRequest.builder().userNo(userNo).build());

                    if (Objects.nonNull(customerDetail)) {
                        // 设置基本信息
                        userName = StringUtils.defaultIfBlank(customerDetail.getName(), StringUtils.EMPTY);
                        originalPhone = StringUtils.defaultIfBlank(customerDetail.getTel(), StringUtils.EMPTY);
                        encodePhone = StringUtils.defaultIfBlank(customerDetail.getEnCodeTel(), StringUtils.EMPTY);
                        // 设置性别
                        gender = StringUtils.isNotBlank(customerDetail.getIdNoTrans())
                            ? GenderEnum.fromCode(IDCardUtil.getGenderFromIdCard(customerDetail.getIdNoTrans())).getDescription()
                            : StringUtils.EMPTY;
                        // 设置账号状态
                        if (Objects.nonNull(customerDetail.getStatus())) {
                            AccountStatusEnum accountStatusEnum = AccountStatusEnum.fromCode(customerDetail.getStatus());
                            if (Objects.nonNull(accountStatusEnum)) {
                                accountStatus.add(accountStatusEnum.getDescription());
                            }
                        }
                        // 获取注册APP信息
                        if (StringUtils.isNotBlank(originalPhone)) {
                            List<UserNoDTO> userNoDTOS = cisFacadeClient.queryUserNoByMobile(originalPhone);
                            if (CollectionUtils.isNotEmpty(userNoDTOS)) {
                                Set<String> apps = userNoDTOS.stream()
                                        .map(UserNoDTO::getApp)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());
                                Set<String> userNos = userNoDTOS.stream()
                                        .map(UserNoDTO::getUserNo)
                                        .filter(Objects::nonNull)
                                        .map(String::valueOf)
                                        .collect(Collectors.toSet());
                                if (CollectionUtils.isNotEmpty(apps)) {
                                    registerApp.addAll(apps);
                                }
                                if (CollectionUtils.isNotEmpty(userNos)) {
                                    userNoList.addAll(userNos);
                                }
                            }
                        }
                    }
                }
            }
        }
        acsFollowData.setUserName(userName);
        acsFollowData.setUserNo(userNo);
        acsFollowData.setRegisterApp(registerApp);
        acsFollowData.setAccountStatus(accountStatus);
        acsFollowData.setGender(gender);
        acsFollowData.setUserNoList(userNoList);

        // =========================
        //   2) 客户打标（客服系统）
        // =========================
        // 示例：查询风险标签、易投诉等
        List<String> userTagLabelList = new ArrayList<>();
        List<LabelDto> labelDtos = userLabelService.getUserLabel(userNo.toString());
        if (CollectionUtils.isNotEmpty(labelDtos)) {
            userTagLabelList.addAll(labelDtos.stream()
                    .map(LabelDto::getName)
                    .collect(Collectors.toList()));
        }

        acsFollowData.setUserTagLabelList(userTagLabelList);

        // =========================
        //   3) 其他（客服需计算）
        // =========================
        // 工单列表页、客户详情页等
        // 客户详情页：对 userNo 或手机号 base64 加密
        if (StringUtils.isNotBlank(originalPhone)) {
            String base64Phone = Base64.getEncoder().encodeToString(originalPhone.getBytes(StandardCharsets.UTF_8));
            String customerDetailPageUrl = vocConfig.getCustomerDetailURL() + base64Phone;
            String workOrderDetailUrl = vocConfig.getWorkOrderDetailURL() + base64Phone;
            acsFollowData.setCustomerDetailPageUrl(customerDetailPageUrl);
            acsFollowData.setWorkOrderListPageUrl(workOrderDetailUrl);
        }

        // =========================
        //   4) 坐席信息（客服系统）
        // =========================
        // 通过 agentId 查询坐席名称、组织ID、组织名称
        if (Objects.nonNull(session.getAgentId())) {
            acsFollowData.setUDeskAgentId(session.getAgentId());
            Map<Integer, AgentsResponse> agentsMap = udeskClientService.getAgentsMap();
            AgentsResponse agent = agentsMap.get(session.getAgentId().intValue());
            if (Objects.nonNull(agent)) {
                String encodeMobileLocal = cisFacadeClientService.getEncodeMobileLocal(agent.getCellphone());
                List<EmployeeDepartment> deptList =
                        departmentMapper.getEmployeeDepartmentByUserEncodePhones(Collections.singletonList(encodeMobileLocal));
                if (CollectionUtils.isNotEmpty(deptList)) {
                    EmployeeDepartment employeeDepartment = deptList.get(0);
                    acsFollowData.setSeatsOrganId(String.valueOf(employeeDepartment.getDepartmentId()));
                    acsFollowData.setSeatsOrganName(employeeDepartment.getDepartmentName());
                }
                List<Employee> employeeList =
                        employeeMapper.getEmployeeByUserEncodePhones(Collections.singletonList(encodeMobileLocal));
                if (CollectionUtils.isNotEmpty(employeeList)) {
                    Employee employee = employeeList.get(0);
                    acsFollowData.setSeatsName(employee.getName());
                    sessionRecordRequest.setAgentId(String.valueOf(employee.getId()));
                }else{
                    sessionRecordRequest.setAgentId(Objects.nonNull(session.getAgentId()) ? String.valueOf(session.getAgentId()) : null);
                }
            }
        }

        // =========================
        //   5) Udesk 公共数据（从 Udesk 拉取）
        // =========================
        if (Objects.nonNull(session.getAgentId())) {
            List<ImUserGroupsResponse> allUserGroups = udeskClientService.getAllUserGroups();
            List<String> seatesGroupList = new ArrayList<>();

            Long currentAgentId = session.getAgentId();
            if (Objects.nonNull(currentAgentId) && CollectionUtils.isNotEmpty(allUserGroups)) {
                for (ImUserGroupsResponse group : allUserGroups) {
                    if (CollectionUtils.isNotEmpty(group.getAgents())) {
                        for (ImUserGroupsResponse.Agents agent1 : group.getAgents()) {
                            if (currentAgentId.equals(agent1.getId())) {
                                seatesGroupList.add(group.getName());
                                break; // 找到一个组后即可退出该组内循环
                            }
                        }
                    }
                }
            }
            acsFollowData.setSeatsGroupList(seatesGroupList);
        }
        acsFollowData.setUserTagUdeskLabelList(userTagUdeskLabelList);

        // =========================
        //   6) Udesk-聊天（人工）
        // =========================
        acsFollowData.setUDeskConversationId(Objects.nonNull(session.getSubSessionId()) ? "#h" + convertToBase36(session.getSubSessionId()) : null);
        acsFollowData.setUDeskMainConversationId(session.getSessionId());
        acsFollowData.setUDeskConversationStartTime(LocalDateTimeUtils.format(session.getCreatedAt()));
        acsFollowData.setUDeskConversationEndTime(LocalDateTimeUtils.format(session.getClosedAt()));
        acsFollowData.setUDeskLastMsgSender(SenderEnum.getDescriptionByCode(session.getLastResponse()));
        acsFollowData.setUDeskFirstResponseTime(session.getRespSeconds());
        // 对话结束方式(多选) -> close_method
        List<String> endWayList = StringUtils.isNotBlank(session.getCloseMethod())
                ? Collections.singletonList(CloseMethodEnum.getDescriptionByCode(session.getCloseMethod()))
                : Collections.emptyList();
        acsFollowData.setUDeskConversationEndWayList(endWayList);
        // 满意度评价(多选) -> getOptionName
        List<String> satisfactionList = StringUtils.isNotBlank(session.getOptionName())
                ? Collections.singletonList(session.getOptionName())
                : Collections.emptyList();
        acsFollowData.setUDeskCallSatisfactionEvaluationList(satisfactionList);

        // =========================
        //   7) 序列化为 JSON
        // =========================
        return JsonUtil.toJson(acsFollowData);
    }

    public static String convertToBase36(long number) {
        final String digits = "0123456789abcdefghijklmnopqrstuvwxyz";  // 36进制字符集
        StringBuilder result = new StringBuilder();

        // 将数字转换为36进制
        while (number > 0) {
            result.insert(0, digits.charAt((int) (number % 36)));
            number /= 36;
        }

        return result.toString();  // 返回转换后的字符串
    }
}