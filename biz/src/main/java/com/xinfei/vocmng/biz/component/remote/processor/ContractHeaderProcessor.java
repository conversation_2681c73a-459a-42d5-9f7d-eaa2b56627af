package com.xinfei.vocmng.biz.component.remote.processor;

import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;
import com.xinfei.vocmng.itl.model.header.BaseHeader;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 合同系统请求头处理
 *
 * <AUTHOR>
 * @version $ ContractHeaderProcessor, v 0.1 2023/12/23 14:46 qu.lu Exp $
 */
@Slf4j
@Component
@ProcessRule(service = ThirdPartyServiceEnum.CONTRACT)
public class ContractHeaderProcessor extends AbstractHeaderProcessRule{

    @Override
    protected BaseHeader buildHeader(RequestTemplate template) {
        return new BaseHeader();
    }

    @Override
    public <T> void process(RequestTemplate template) {
        headerProcess(template);
    }
}
