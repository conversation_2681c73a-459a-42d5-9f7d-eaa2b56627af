/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.enums.AmtTypeEnum;
import com.xinfei.vocmng.biz.model.resp.AmountAdjustmentRecordResp;
import com.xinfei.vocmng.biz.service.AccountAmountService;
import com.xinfei.vocmng.biz.service.AccountService;
import com.xinfei.vocmng.biz.util.MoneyUtil;
import com.xinfei.vocmng.itl.client.feign.impl.AmsFeignService;
import io.kyoto.pillar.ams.rest.dto.falcon.req.AccountAmtLogRequest;
import io.kyoto.pillar.ams.rest.dto.falcon.resp.AccountAmountLogResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ AccountAmountServiceImpl, v 0.1 2024/8/12 18:10 you.zhang Exp $
 */
@Slf4j
@Service
public class AccountAmountServiceImpl implements AccountAmountService {
    @Resource
    private AmsFeignService amsFeignService;
    @Override
    public List<AmountAdjustmentRecordResp> queryAdjustmentRecords(String app, String customerNo, String adjustType) {

        AccountAmtLogRequest accountAmtLogRequest = new AccountAmtLogRequest();
        accountAmtLogRequest.setApp(app);
        accountAmtLogRequest.setCustNo(customerNo);
        List<AccountAmountLogResponse> accountAmountLogResponses = amsFeignService.queryAccountAmtLog(accountAmtLogRequest);
        if (CollectionUtils.isEmpty(accountAmountLogResponses)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isNotEmpty(adjustType)) {
            accountAmountLogResponses = accountAmountLogResponses.stream()
                    .filter(accountAmountLogResponse -> adjustType.equals(accountAmountLogResponse.getAmtType()))
                    .collect(Collectors.toList());
        }

        return accountAmountLogResponses.stream()
                .map(accountAmountLogResponse -> AmountAdjustmentRecordResp.builder()
                        .changeType(accountAmountLogResponse.getChangeType())
                        .afterAmount(MoneyUtil.f2Y(BigDecimal.valueOf(accountAmountLogResponse.getAfterAmount())))
                        .beforeAmount(MoneyUtil.f2Y(BigDecimal.valueOf(accountAmountLogResponse.getBeforeAmount())))
                        .changeAmt(MoneyUtil.f2Y(BigDecimal.valueOf(accountAmountLogResponse.getChangeAmt())))
                        .adjustType(accountAmountLogResponse.getAmtType())
                        .adjustTypeName(Optional.ofNullable(AmtTypeEnum.fromCode(accountAmountLogResponse.getAmtType()))
                                .map(AmtTypeEnum::getMsg)
                                .orElse(accountAmountLogResponse.getAmtType()))
                        .businessTime(accountAmountLogResponse.getBusinessTime())
                        .build())
                .sorted(Comparator.comparing(AmountAdjustmentRecordResp::getBusinessTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }
}
