package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单转接状态枚举
 * 用于海尔消金工单转接流程
 *
 * <AUTHOR>
 * @version $ TransferStatus, v 0.1 2025/08/11 TransferStatus Exp $
 */
@Getter
@AllArgsConstructor
public enum TransferStatus {

    /**
     * 待转接
     */
    PENDING("PENDING", "待转接"),

    /**
     * 已转接
     */
    TRANSFERRED("TRANSFERRED", "已转接"),

    /**
     * 转接完成
     */
    COMPLETED("COMPLETED", "转接完成"),

    /**
     * 转接失败
     */
    FAILED("FAILED", "转接失败");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 转接状态枚举
     */
    public static TransferStatus fromCode(String code) {
        for (TransferStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的转接状态码: " + code);
    }

    /**
     * 判断是否为最终状态
     *
     * @return true表示最终状态，false表示中间状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }

    /**
     * 判断是否可以转接
     *
     * @return true表示可以转接，false表示不可以转接
     */
    public boolean canTransfer() {
        return this == PENDING;
    }
}
