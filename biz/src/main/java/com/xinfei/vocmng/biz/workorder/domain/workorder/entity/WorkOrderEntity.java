package com.xinfei.vocmng.biz.workorder.domain.workorder.entity;

import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.*;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 工单实体
 * 工单的核心业务实体
 *
 * <AUTHOR>
 * @version $ WorkOrderEntity, v 0.1 2025/07/30 WorkOrderEntity Exp $
 */
@Data
@Builder
public class WorkOrderEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 资方编码
     */
    private String funderCode;

    /**
     * 海尔消金通话ID
     */
    private String callId;

    /**
     * Udesk通话ID
     */
    private String udeskCallId;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 事件类型：CALL_CONNECTED/CALL_FAILED
     */
    private String eventType;

    /**
     * 客服ID
     */
    private String agentId;

    /**
     * 接线时间
     */
    private LocalDateTime connectTime;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 关联小结ID
     */
    private Long summaryId;

    /**
     * 工单类型
     */
    private WorkOrderType orderType;

    /**
     * 优先级
     */
    private Priority priority;

    /**
     * 工单状态
     */
    private WorkOrderStatus status;

    /**
     * 原工单系统ID
     */
    private Long originalWorkOrderId;

    /**
     * 来电时间（海尔消金来电时间）
     */
    private LocalDateTime callTime;

    /**
     * 转接时间
     */
    private LocalDateTime transferTime;

    /**
     * 转接结果：SUCCESS/FAIL
     */
    private String transferResult;

    /**
     * 版本号（乐观锁）
     */
    private Integer version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 验证工单实体数据
     */
    public void validate() {
        if (StringUtils.isBlank(orderNo)) {
            throw new IllegalArgumentException("工单编号不能为空");
        }
        
        if (StringUtils.isBlank(funderCode)) {
            throw new IllegalArgumentException("资方编码不能为空");
        }
        
        if (Objects.isNull(orderType)) {
            throw new IllegalArgumentException("工单类型不能为空");
        }
        
        if (Objects.isNull(priority)) {
            throw new IllegalArgumentException("优先级不能为空");
        }
        
        if (Objects.isNull(status)) {
            throw new IllegalArgumentException("工单状态不能为空");
        }
    }
}
