package com.xinfei.vocmng.biz.workorder.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单状态更新请求参数
 *
 * <AUTHOR>
 * @version $ WorkOrderStatusUpdateRequest, v 0.1 2025/08/08 WorkOrderStatusUpdateRequest Exp $
 */
@Data
@ApiModel("工单状态更新请求")
public class WorkOrderStatusUpdateRequest {

    @ApiModelProperty(value = "海尔消金通话ID", required = true)
    private String haierCallId;

    @ApiModelProperty(value = "工单编号")
    @NotBlank(message = "工单号不能为空")
    private String workOrderNo;

    @ApiModelProperty(value = "跟进时间，格式：yyyy-MM-dd HH:mm:ss")
    private String processTime;

    @ApiModelProperty(value = "工单状态（任务状态）", required = true)
    @NotNull(message = "工单状态不能为空")
    private Integer workOrderStatus;

    @ApiModelProperty(value = "客户状态")
    private String customerStatus;

    @ApiModelProperty(value = "事件类型：CREATED/FEEDBACK/CLOSED", required = true)
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

}
