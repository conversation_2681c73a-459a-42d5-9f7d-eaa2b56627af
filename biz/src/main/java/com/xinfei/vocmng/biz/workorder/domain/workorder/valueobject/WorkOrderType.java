package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Getter;

/**
 * 工单类型枚举
 *
 * <AUTHOR>
 * @version $ WorkOrderType, v 0.1 2025/07/30 WorkOrderType Exp $
 */
@Getter
public enum WorkOrderType {

    /**
     * 投诉
     */
    COMPLAINT("COMPLAINT", "投诉"),

    /**
     * 咨询
     */
    CONSULTATION("CONSULTATION", "咨询"),

    /**
     * 退款
     */
    REFUND("REFUND", "退款"),

    /**
     * 申诉
     */
    APPEAL("APPEAL", "申诉");

    private final String code;
    private final String description;

    WorkOrderType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static WorkOrderType fromCode(String code) {
        for (WorkOrderType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的工单类型: " + code);
    }
}
