/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.xinfei.vocmng.biz.component.DictDataCache;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.*;
import com.xinfei.vocmng.biz.service.*;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.biz.util.UniqueIdentifierGenerator;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.req.EmployeeComReq;
import com.xinfei.vocmng.dal.dto.resp.EmployeeComResp;
import com.xinfei.vocmng.dal.mapper.*;
import com.xinfei.vocmng.dal.po.*;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ AccountServiceImpl, v 0.1 2023/12/20 14:34 wancheng.qu Exp $
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    @Resource
    private EmployeeMapper employeeMapper;
    @Resource
    private EmployeeRoleMappingMapper employeeRoleMappingMapper;
    @Resource
    private IssueCategoryConfigMapper issueCategoryConfigMapper;
    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private EmployeeService employeeService;
    @Resource
    private DataAuthMapper dataAuthMapper;
    @Resource
    private ResourceMapper resourceMapper;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private ResourceRoleMappingMapper resourceRoleMappingMapper;
    @Resource
    private RoleDataAuthMappingMapper roleDataAuthMappingMapper;
    @Resource
    private ResourceRoleMappingService resourceRoleMappingService;
    @Resource
    private RoleDataAuthService roleDataAuthService;
    @Resource
    private LabelCategoryConfigMapper labelCategoryConfigMapper;
    @Resource
    private LabelConfigMapper labelConfigMapper;
    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;
    @Resource
    private LoginService loginService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OperateLogMapper operateLogMapper;
    @Resource
    private ControlAuthMapper controlAuthMapper;
    @Resource
    private ControlDataAuthMappingService controlDataAuthMappingService;
    @Resource
    private ControlDataAuthMappingMapper controlDataAuthMappingMapper;
    @Resource
    private FeeStrategyConfigMappingMapper feeStrategyConfigMappingMapper;
    @Resource
    private FeeStrategyConfigMappingService feeStrategyConfigMappingService;
    @Resource
    private DictConfigMapper dictConfigMapper;
    @Resource
    private DictDetailMapper dictDetailMapper;
    @Autowired
    private DictDetailService detailService;
    @Autowired
    private DictDataCache dictDataCache;
    @Autowired
    private CisFacadeClientService cisFacadeClientService;
    @Autowired
    private VocConfig vocConfig;


    @Override
    public List<DepartmentResp> queryDepartment(DepartmentReq req) {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(req.getName()), Department::getName, req.getName())
                .eq(Department::getIsDeleted, 0);

        List<Department> departments = departmentMapper.selectList(wrapper);

        return Optional.ofNullable(departments)
                .map(list -> list.stream()
                        .map(this::convertToDepartmentResp)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private DepartmentResp convertToDepartmentResp(Department department) {
        DepartmentResp d = new DepartmentResp();
        d.setId(department.getId());
        d.setName(department.getName());
        d.setParentDepartmentId(department.getParentDepartmentId());
        d.setLevel(department.getLevel());
        d.setDirector(employeeService.getUserNameForIdentify(department.getDirector()));
        d.setDirectorIdentify(department.getDirector());
        d.setDepartmentType(department.getDepartmentType());
        return d;
    }

    @Override
    @Transactional
    public Boolean updateDepartment(DepartmentReq req) {
        if (Objects.isNull(req.getId())) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_DEPARTMENTID_ERROR);
        }
        Department d = new Department();
        d.setId(req.getId());
        if (Objects.equals(1, req.getIsDeleted())) {
            //删除组织1. 支持删除组织，删除组织时，校验是否组织内无人，若组织内存在员工，提醒删除失败。“组织内存在员工，删除失败”*删除组织时，若存在下级组织，需要同步判断是否存在员工，若存在，也需要提示删除失败。“下级组织还存在员工，删除失败”
            List<Long> ids = getAllChildren(req.getId());
            LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Employee::getDepartmentId, ids)
                    .eq(Employee::getIsDeleted, 0);
            if (employeeMapper.selectCount(wrapper) > 0) {
                throw new IgnoreException(TechplayErrDtlEnum.HAD_DEPARTMENTID_ERROR);
            }
            d.setIsDeleted(1);
            departmentMapper.updateById(d);

        } else {
            //修改组织
            verifyDepartName(req.getName(), req.getId());
            d.setName(req.getName());
            d.setDirector(req.getDirector());
            d.setUpdatedTime(LocalDateTime.now());
            d.setDepartmentType(req.getDepartmentType());
            departmentMapper.updateById(d);
        }
        return true;
    }

    private List<Long> getAllChildren(Long id) {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(
                        Department::getId,
                        Department::getParentDepartmentId
                )
                .eq(Department::getIsDeleted, 0);

        List<Department> departments = departmentMapper.selectList(wrapper);
        Map<Long, List<Department>> departmentMap = buildDepartmentMap(departments);
        List<Department> ids = findSubDepartmentsRecursive(id, departmentMap);
        if (CollectionUtils.isEmpty(ids)) {
            return Arrays.asList(id);
        }
        List<Long> did = ids.stream().map(Department::getId).collect(Collectors.toList());
        did.add(id);
        return did;

    }

    private Map<Long, List<Department>> buildDepartmentMap(List<Department> allDepartments) {
        Map<Long, List<Department>> departmentMap = new HashMap<>();

        for (Department department : allDepartments) {
            Long parentId = department.getParentDepartmentId();
            departmentMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(department);
        }

        return departmentMap;
    }

    private List<Department> findSubDepartmentsRecursive(Long id, Map<Long, List<Department>> departmentMap) {
        List<Department> result = new ArrayList<>();
        List<Department> subDepartments = departmentMap.getOrDefault(id, new ArrayList<>());

        for (Department subDepartment : subDepartments) {
            result.add(subDepartment);
            result.addAll(findSubDepartmentsRecursive(subDepartment.getId(), departmentMap));
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean addDepartment(DepartmentReq req) {
        verifyDepartName(req.getName(), null);
        Department d = new Department();
        d.setName(req.getName());
        d.setParentDepartmentId(req.getParentDepartmentId());
        d.setLevel(req.getLevel());
        d.setDirector(req.getDirector());
        d.setCreatedTime(LocalDateTime.now());
        d.setUpdatedTime(LocalDateTime.now());
        d.setDepartmentType(req.getDepartmentType());
        departmentMapper.insert(d);
        return true;
    }

    private void verifyDepartName(String name, Long id) {
        if (StringUtils.isBlank(name)) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_DEPARTMENT_ERROR);
        }
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getName, name)
                .ne(Objects.nonNull(id), Department::getId, id)
                .eq(Department::getIsDeleted, 0);
        Department department = departmentMapper.selectOne(wrapper);
        if (Objects.nonNull(department)) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_DEPARTMENT_NAME_ERROR);
        }
    }

    @Override
    public PageResultResponse<RoleResp> queryRole(RoleReq req) {
        IPage<Role> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(req.getName())) {
            wrapper.and(q -> {
                for (String roleName : req.getName()) {
                    q.or().like(Role::getName, roleName);
                }
            });
        }
        wrapper.eq(Role::getIsDeleted, 0);
        IPage<Role> pageList = roleMapper.selectPage(page, wrapper);
        if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<RoleResp> respList = pageList.getRecords().stream().map(t -> {
                RoleResp rr = new RoleResp();
                rr.setId(t.getId());
                rr.setName(t.getName());
                rr.setDescription(t.getDescription());
                rr.setCreatedTime(t.getCreatedTime());
                rr.setUpdatedTime(t.getUpdatedTime());
                rr.setResourceList(getRoleResource(t.getId()));
                rr.setDataList(getRoleData(t.getId()));
                rr.setControlList(getRoleControlList(t.getId()));
                rr.setFeeStrategyConfigs(getFeeStrategyConfigs(t.getId()));
                rr.setCreatedUser(employeeService.getUserNameForIdentify(t.getCreateUserIdentify()));
                rr.setUpdateUser(employeeService.getUserNameForIdentify(t.getUpdateUserIdentify()));
                return rr;
            }).collect(Collectors.toList());

            return new PageResultResponse<>(respList, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        }
        return new PageResultResponse<>();
    }

    @Override
    public List<FeeStrategyConfig> getFeeStrategyConfig() {
        return getFeeStrategyConfigs(UserContextHolder.getUserContext().getId());
    }

    private List<ControlReq> getRoleControlList(Long id) {
        LambdaQueryWrapper<ControlDataAuthMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ControlDataAuthMapping::getRoleId, id)
                .eq(ControlDataAuthMapping::getIsDeleted, 0);

        List<ControlDataAuthMapping> cm = controlDataAuthMappingMapper.selectList(wrapper);

        return Optional.ofNullable(cm)
                .map(list -> list.stream()
                        .map(t -> {
                            ControlReq cr = new ControlReq();
                            cr.setId(t.getControlId());
                            cr.setControlValue(t.getControlValue());
                            return cr;
                        })
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    private List<FeeStrategyConfig> getFeeStrategyConfigs(Long roleId) {
        //查找角色对应的费用策略
        LambdaQueryWrapper<FeeStrategyConfigMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FeeStrategyConfigMapping::getRoleId, roleId)
                .eq(FeeStrategyConfigMapping::getIsDel, 0);
        List<FeeStrategyConfigMapping> fscm = feeStrategyConfigMappingMapper.selectList(wrapper);

        //获取新费控的字典信息
        LambdaQueryWrapper<DictConfig> dictConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictConfigLambdaQueryWrapper.eq(DictConfig::getIsDeleted, 0)
                .eq(DictConfig::getName, "fee_strategy_config");
        List<DictConfig> dictConfigs = dictConfigMapper.selectList(dictConfigLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(dictConfigs)) {
            return new ArrayList<>();
        }

        //查找新费控相对应的字典明细
        LambdaQueryWrapper<DictDetail> dictDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictDetailLambdaQueryWrapper.eq(DictDetail::getIsDeleted, 0)
                .eq(DictDetail::getDictConfigId, dictConfigs.get(0).getId());
        List<DictDetail> res = dictDetailMapper.selectList(dictDetailLambdaQueryWrapper);

        List<FeeStrategyConfig> feeStrategyConfigs = new ArrayList<>();
        for (DictDetail detail : res) {
            FeeStrategyConfig feeStrategyConfig = new FeeStrategyConfig();
            feeStrategyConfig.setDictDetailId(detail.getId());
            feeStrategyConfig.setSceneName(detail.getDictValue());
            FeeStrategyConfigMapping feeStrategyConfigMapping = fscm.stream().filter(t -> t.getDictDetailId().equals(detail.getId())).findFirst().orElse(null);
            if (feeStrategyConfigMapping != null) {
                feeStrategyConfig.setStrategyId(feeStrategyConfigMapping.getStrategyId());
                feeStrategyConfig.setCanBreakOut(feeStrategyConfigMapping.getCanBreakOut());
                feeStrategyConfig.setNeedComplaintChannel(feeStrategyConfigMapping.getNeedComplaintChannel());
            }
            feeStrategyConfigs.add(feeStrategyConfig);
        }
        return feeStrategyConfigs;
    }

    private List<Long> getRoleResource(Long id) {
        LambdaQueryWrapper<ResourceRoleMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResourceRoleMapping::getRoleId, id)
                .eq(ResourceRoleMapping::getIsDeleted, 0);

        List<ResourceRoleMapping> mappings = resourceRoleMappingMapper.selectList(wrapper);

        return Optional.ofNullable(mappings)
                .map(list -> list.stream()
                        .map(ResourceRoleMapping::getResourceId)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    private List<Long> getRoleData(Long id) {
        LambdaQueryWrapper<RoleDataAuthMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RoleDataAuthMapping::getRoleId, id)
                .eq(RoleDataAuthMapping::getIsDeleted, 0);

        List<RoleDataAuthMapping> mappings = roleDataAuthMappingMapper.selectList(wrapper);

        return Optional.ofNullable(mappings)
                .map(list -> list.stream()
                        .map(RoleDataAuthMapping::getDataId)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    @Override
    @Transactional
    public Boolean updateRole(RoleReq req) {
        if (Objects.isNull(req.getId())) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_DEPARTMENTID_ERROR);
        }
        if (Objects.equals(1, req.getIsDeleted())) {
            LambdaQueryWrapper<EmployeeRoleMapping> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EmployeeRoleMapping::getRoleId, req.getId())
                    .eq(EmployeeRoleMapping::getIsDeleted, 0);
            if (employeeRoleMappingMapper.selectCount(wrapper) > 0) {
                throw new IgnoreException(TechplayErrDtlEnum.ROLE_EFFECTIVE_ERROR);
            }
            Role r = new Role();
            r.setId(req.getId());
            r.setIsDeleted(1);
            r.setUpdatedTime(LocalDateTime.now());
            r.setUpdateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
            roleMapper.updateById(r);
            updateRoleAuth(req.getId());
        } else {
            //修改角色
            updateRoleAuth(req.getId());
            if (CollectionUtils.isNotEmpty(req.getDataList())) {
                List<RoleDataAuthMapping> rlist = req.getDataList().stream().map(t -> {
                    RoleDataAuthMapping rm = new RoleDataAuthMapping();
                    rm.setRoleId(req.getId());
                    rm.setDataId(t);
                    rm.setCreatedTime(LocalDateTime.now());
                    rm.setUpdatedTime(LocalDateTime.now());
                    return rm;
                }).collect(Collectors.toList());
                roleDataAuthService.saveBatchs(rlist);
            }
            if (CollectionUtils.isNotEmpty(req.getResourceList())) {
                List<ResourceRoleMapping> rrlist = req.getResourceList().stream().map(t -> {
                    ResourceRoleMapping rr = new ResourceRoleMapping();
                    rr.setRoleId(req.getId());
                    rr.setResourceId(t);
                    rr.setCreatedTime(LocalDateTime.now());
                    rr.setUpdatedTime(LocalDateTime.now());
                    return rr;
                }).collect(Collectors.toList());
                resourceRoleMappingService.batchSave(rrlist);
            }
            if (CollectionUtils.isNotEmpty(req.getControlList())) {
                List<ControlDataAuthMapping> rlist = req.getControlList().stream().map(t -> {
                    ControlDataAuthMapping cm = new ControlDataAuthMapping();
                    cm.setRoleId(req.getId());
                    cm.setControlId(t.getId());
                    cm.setControlValue(t.getControlValue());
                    cm.setCreatedTime(LocalDateTime.now());
                    cm.setUpdatedTime(LocalDateTime.now());
                    return cm;
                }).collect(Collectors.toList());
                controlDataAuthMappingService.saveBatchs(rlist);
            }
            if (CollectionUtils.isNotEmpty(req.getFeeStrategyConfigs())) {
                List<FeeStrategyConfig> feeStrategyConfigs = req.getFeeStrategyConfigs().stream()
                        .filter(this::isValidFeeStrategyConfig)
                        .collect(Collectors.toList());
                List<FeeStrategyConfigMapping> rlist = feeStrategyConfigs.stream().map(t -> {
                    FeeStrategyConfigMapping fscm = new FeeStrategyConfigMapping();
                    fscm.setDictDetailId(t.getDictDetailId());
                    fscm.setRoleId(req.getId());
                    fscm.setStrategyId(t.getStrategyId());
                    fscm.setNeedComplaintChannel(t.getNeedComplaintChannel());
                    fscm.setCanBreakOut(t.getCanBreakOut());
                    fscm.setCreatedTime(LocalDateTime.now());
                    fscm.setUpdatedTime(LocalDateTime.now());
                    return fscm;
                }).collect(Collectors.toList());
                feeStrategyConfigMappingService.saveBatchs(rlist);
            }
        }
        //更新用户cache
        loginService.addUserCache(getEmpByRoleId(req.getId()));
        return true;
    }

    private boolean isValidFeeStrategyConfig(FeeStrategyConfig config) {
        return Objects.nonNull(config.getDictDetailId()) &&
                StringUtils.isNotBlank(config.getStrategyId());
    }

    private List<String> getEmpByRoleId(Long role) {
        return employeeRoleMappingMapper.getUserIdentifyByRoleId(role);
    }

    private void updateRoleAuth(Long id) {
        LambdaUpdateWrapper<RoleDataAuthMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RoleDataAuthMapping::getRoleId, id)
                .set(RoleDataAuthMapping::getIsDeleted, 1)
                .set(RoleDataAuthMapping::getUpdatedTime, LocalDateTime.now());
        roleDataAuthMappingMapper.update(null, updateWrapper);
        LambdaUpdateWrapper<ResourceRoleMapping> uwr = new LambdaUpdateWrapper<>();
        uwr.eq(ResourceRoleMapping::getRoleId, id)
                .set(ResourceRoleMapping::getIsDeleted, 1)
                .set(ResourceRoleMapping::getUpdatedTime, LocalDateTime.now());
        resourceRoleMappingMapper.update(null, uwr);
        LambdaUpdateWrapper<ControlDataAuthMapping> cdm = new LambdaUpdateWrapper<>();
        cdm.eq(ControlDataAuthMapping::getRoleId, id)
                .set(ControlDataAuthMapping::getIsDeleted, 1)
                .set(ControlDataAuthMapping::getUpdatedTime, LocalDateTime.now());
        controlDataAuthMappingMapper.update(null, cdm);
        LambdaUpdateWrapper<FeeStrategyConfigMapping> fscm = new LambdaUpdateWrapper<>();
        fscm.eq(FeeStrategyConfigMapping::getRoleId, id)
                .set(FeeStrategyConfigMapping::getIsDel, 1)
                .set(FeeStrategyConfigMapping::getUpdatedTime, LocalDateTime.now());
        feeStrategyConfigMappingMapper.update(null, fscm);
    }

    @Override
    @Transactional
    public Boolean addRole(RoleReq roleReq) {
        if (StringUtils.isBlank(roleReq.getAddName()) || StringUtils.isBlank(roleReq.getDescription())) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_ROLE_NAME_ERROR);
        }
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getName, roleReq.getAddName())
                .eq(Role::getIsDeleted, 0);
        if (roleMapper.selectCount(wrapper) > 0) {
            throw new IgnoreException(TechplayErrDtlEnum.HAS_ROLE_NAME_ERROR);
        }
        Role r = new Role();
        r.setName(roleReq.getAddName());
        r.setDescription(roleReq.getDescription());
        r.setCreateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        r.setUpdateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        r.setCreatedTime(LocalDateTime.now());
        r.setUpdatedTime(LocalDateTime.now());
        roleMapper.insert(r);
        if (Objects.isNull(r.getId())) {
            throw new IgnoreException(TechplayErrDtlEnum.ROLE_NAME_ERROR);
        }
        if (CollectionUtils.isNotEmpty(roleReq.getResourceList())) {
            List<ResourceRoleMapping> rlist = roleReq.getResourceList().stream().map(t -> {
                ResourceRoleMapping rrm = new ResourceRoleMapping();
                rrm.setRoleId(r.getId());
                rrm.setResourceId(t);
                rrm.setCreatedTime(LocalDateTime.now());
                rrm.setUpdatedTime(LocalDateTime.now());
                return rrm;
            }).collect(Collectors.toList());
            resourceRoleMappingService.batchSave(rlist);
        }
        if (CollectionUtils.isNotEmpty(roleReq.getDataList())) {
            List<RoleDataAuthMapping> rlist = roleReq.getDataList().stream().map(t -> {
                RoleDataAuthMapping rd = new RoleDataAuthMapping();
                rd.setRoleId(r.getId());
                rd.setDataId(t);
                rd.setCreatedTime(LocalDateTime.now());
                rd.setUpdatedTime(LocalDateTime.now());
                return rd;
            }).collect(Collectors.toList());
            roleDataAuthService.saveBatchs(rlist);
        }
        if (CollectionUtils.isNotEmpty(roleReq.getControlList())) {
            List<ControlDataAuthMapping> rlist = roleReq.getControlList().stream().map(t -> {
                ControlDataAuthMapping cm = new ControlDataAuthMapping();
                cm.setRoleId(r.getId());
                cm.setControlId(t.getId());
                cm.setControlValue(t.getControlValue());
                cm.setCreatedTime(LocalDateTime.now());
                cm.setUpdatedTime(LocalDateTime.now());
                return cm;
            }).collect(Collectors.toList());
            controlDataAuthMappingService.saveBatchs(rlist);
        }
        if (CollectionUtils.isNotEmpty(roleReq.getFeeStrategyConfigs())) {
            List<FeeStrategyConfigMapping> rlist = roleReq.getFeeStrategyConfigs().stream().map(t -> {
                FeeStrategyConfigMapping fscm = new FeeStrategyConfigMapping();
                fscm.setDictDetailId(t.getDictDetailId());
                fscm.setRoleId(r.getId());
                fscm.setStrategyId(t.getStrategyId() == null ? "" : t.getStrategyId());
                fscm.setNeedComplaintChannel(t.getNeedComplaintChannel());
                fscm.setCanBreakOut(t.getCanBreakOut());
                fscm.setCreatedTime(LocalDateTime.now());
                fscm.setUpdatedTime(LocalDateTime.now());
                return fscm;
            }).collect(Collectors.toList());
            feeStrategyConfigMappingService.saveBatchs(rlist);
        }

        return true;
    }

    @Override
    public List<ResourceResp> queryMenuList() {
        LambdaQueryWrapper<com.xinfei.vocmng.dal.po.Resource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(com.xinfei.vocmng.dal.po.Resource::getIsDeleted, 0);
        List<com.xinfei.vocmng.dal.po.Resource> res = resourceMapper.selectList(wrapper);
        return Optional.ofNullable(res)
                .map(list -> list.stream()
                        .map(this::convertToResource)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private ResourceResp convertToResource(com.xinfei.vocmng.dal.po.Resource resource) {
        ResourceResp r = new ResourceResp();
        r.setId(resource.getId());
        r.setName(resource.getName());
        r.setParentId(resource.getParentId());
        r.setOrderIndex(resource.getOrderIndex());
        r.setPermissionIdentify(resource.getPermissionIdentify());
        r.setCode(resource.getCode());
        r.setPath(resource.getPath());
        r.setType(resource.getType());

        return r;
    }

    @Override
    public List<DataAuthResp> queryDataList() {
        LambdaQueryWrapper<DataAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataAuth::getIsDeleted, 0);
        List<DataAuth> res = dataAuthMapper.selectList(wrapper);
        return Optional.ofNullable(res)
                .map(list -> list.stream()
                        .map(this::convertToData)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private DataAuthResp convertToData(DataAuth dataAuth) {
        DataAuthResp da = new DataAuthResp();
        da.setId(dataAuth.getId());
        da.setData(DataPermissionType.getNameByCode(dataAuth.getData()));
        da.setDataType(dataAuth.getDataType());

        return da;
    }

    @Override
    public PageResultResponse<EmployeeResp> queryEmployeeList(EmployeeReq employeeReq) {
        EmployeeComReq req = new EmployeeComReq();
        IPage<EmployeeComResp> pageList;
        BeanUtil.copyProperties(employeeReq, req);
        Page<EmployeeComResp> page = new Page<>(employeeReq.getCurrentPage(), employeeReq.getPageSize());

        if (StringUtils.isNotBlank(req.getMobile())) {
            req.setMobile(cisFacadeClientService.getEncodeMobileLocal(req.getMobile()));
        }
        pageList = employeeMapper.selectEmployeeResultsByConditionsEncry(req, page);

        List<EmployeeResp> employeeRespList = convertToEmployeeRespList(pageList.getRecords());
        return new PageResultResponse<>(employeeRespList, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
    }

    private List<EmployeeResp> convertToEmployeeRespList(List<EmployeeComResp> employeeComRespList) {
        if (employeeComRespList == null) {
            return Collections.emptyList();
        }
        return employeeComRespList.stream()
                .map(employeeComResp -> {
                    EmployeeResp employeeResp = new EmployeeResp();
                    BeanUtil.copyProperties(employeeComResp, employeeResp);
                    if (vocConfig.isWysFlag()) {
                        employeeResp.setMobile(cisFacadeClientService.batchDecrypt(employeeComResp.getMobile()));
                    }
                    if (vocConfig.isSsoOpenFlag()) {
                        employeeResp.setSourceType(cisFacadeClientService.getSsoUserSourceType(employeeComResp.getSsoUserId()));
                    }
                    return employeeResp;
                })
                .collect(Collectors.toList());
    }


    @Override
    @Transactional
    public Boolean addEmployee(EmployeeReq req) {
        if (StringUtils.isBlank(req.getMobile()) || StringUtils.isBlank(req.getName())
                || Objects.isNull(req.getDepartmentId()) || Objects.isNull(req.getRoleId())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        if (StringUtils.isNotBlank(req.getName()) && req.getName().length() > 50) {
            throw new IgnoreException(TechplayErrDtlEnum.NAME_LENGTH_ERROR);
        }
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        if (vocConfig.isWysFlag()) {
            wrapper.eq(Employee::getMobileEncrypted, cisFacadeClientService.getEncodeMobileLocal(req.getMobile()));
        } else {
            wrapper.eq(Employee::getMobile, req.getMobile());
        }
        wrapper.eq(Employee::getIsDeleted, 0);
        if (employeeMapper.selectCount(wrapper) > 0) {
            throw new IgnoreException(TechplayErrDtlEnum.USER_EXISTS_ERROR);
        }
        Employee e = new Employee();
        e.setUserIdentify(UniqueIdentifierGenerator.getUserIdentify());
        e.setName(req.getName());
        e.setMobile(req.getMobile());
        e.setRealTimeAssistance(req.getRealTimeAssistance());
        e.setMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(req.getMobile()));
        e.setDepartmentId(req.getDepartmentId());
        e.setCreatedTime(LocalDateTime.now());
        e.setUpdatedTime(LocalDateTime.now());
        try {
            Triple<Boolean, String, Long> pair = cisFacadeClientService.syncUserData(e.getName(), e.getMobile(), e.getUserIdentify(), UserContextHolder.getUserIdentify(), 0);
            if (!pair.getLeft()) {
                throw new TechplayException(TechplayErrDtlEnum.SSO_REGISTER_ERROR, e.getMobile());
            }
            e.setSsoUserId(pair.getRight());
        } catch (Exception ex) {
            log.warn("addEmployee has sso error", ex);
            throw new TechplayException(TechplayErrDtlEnum.SSO_REGISTER_ERROR, e.getMobile());
        }

        employeeMapper.insert(e);
        EmployeeRoleMapping er = new EmployeeRoleMapping();
        er.setUserIdentify(e.getUserIdentify());
        er.setRoleId(req.getRoleId());
        er.setCreatedTime(LocalDateTime.now());
        er.setUpdatedTime(LocalDateTime.now());
        employeeRoleMappingMapper.insert(er);

        return true;
    }

    @Override
    @Transactional
    public Boolean updateEmployee(EmployeeReq req) {
        if (StringUtils.isBlank(req.getUserIdentify())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        Long uid = getUserId(req.getUserIdentify());
        redisUtils.del(LoginUserConstants.USER__KEY + req.getUserIdentify());
        LambdaUpdateWrapper<Employee> uwr = new LambdaUpdateWrapper<>();
        uwr.eq(Employee::getUserIdentify, req.getUserIdentify()).eq(Employee::getIsDeleted, 0);
        uwr.set(Employee::getUpdatedTime, LocalDateTime.now());
        if (Objects.equals(1, req.getState())) {
            //修改员工状态
            uwr.set(Employee::getState, 1);
            employeeMapper.update(null, uwr);
            cisFacadeClientService.updateUserStatus(uid, 1, UserContextHolder.getUserIdentify());
            return true;
        }

        if (StringUtils.isBlank(req.getMobile()) || StringUtils.isBlank(req.getName()) || StringUtils.isBlank(req.getUserIdentify())
                || Objects.isNull(req.getDepartmentId()) || Objects.isNull(req.getRoleId())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        uwr.set(Employee::getRealTimeAssistance, req.getRealTimeAssistance());
        uwr.set(Employee::getName, req.getName());
        uwr.set(Employee::getMobile, req.getMobile());
        uwr.set(Employee::getMobileEncrypted, cisFacadeClientService.getEncodeMobileLocal(req.getMobile()));
        uwr.set(Employee::getDepartmentId, req.getDepartmentId());
        if (!Objects.equals(2, req.getSourceType())) {
            cisFacadeClientService.updateSsoUser(uid, req.getName(), req.getMobile(), UserContextHolder.getUserIdentify());
        }
        employeeMapper.update(null, uwr);
        LambdaUpdateWrapper<EmployeeRoleMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmployeeRoleMapping::getUserIdentify, req.getUserIdentify()).eq(EmployeeRoleMapping::getIsDeleted, 0);
        EmployeeRoleMapping employeeRoleMapping = new EmployeeRoleMapping();
        employeeRoleMapping.setIsDeleted(1);
        employeeRoleMapping.setUpdatedTime(LocalDateTime.now());
        employeeRoleMappingMapper.update(employeeRoleMapping, updateWrapper);

        EmployeeRoleMapping er = new EmployeeRoleMapping();
        er.setUserIdentify(req.getUserIdentify());
        er.setRoleId(req.getRoleId());
        er.setCreatedTime(LocalDateTime.now());
        er.setUpdatedTime(LocalDateTime.now());
        employeeRoleMappingMapper.insert(er);

        return true;
    }

    private Long getUserId(String userIdentify) {
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(userIdentify), Employee::getUserIdentify, userIdentify)
                .eq(Employee::getIsDeleted, 0);
        Employee employee = employeeMapper.selectOne(wrapper);
        return employee.getSsoUserId();
    }


    @Override
    @Transactional
    public Boolean batchUpdateEmployee(EmployeeListReq req) {
        if (CollectionUtils.isEmpty(req.getUserIdentify())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        Employee updateEntity = new Employee();
        updateEntity.setUpdatedTime(LocalDateTime.now());
        if (Objects.equals(1, req.getIsDeleted())) {
            updateEntity.setIsDeleted(1);
            req.getUserIdentify().forEach(
                    t -> {
                        cisFacadeClientService.updateUserStatus(getUserId(t), 1, UserContextHolder.getUserIdentify());
                    }
            );
        } else if (Objects.nonNull(req.getDepartmentId())) {
            updateEntity.setDepartmentId(req.getDepartmentId());
        }
        LambdaUpdateWrapper<Employee> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Employee::getUserIdentify, req.getUserIdentify()).eq(Employee::getIsDeleted, 0);
        employeeMapper.update(updateEntity, updateWrapper);
        return true;
    }

    @Override
    public void downloadEmp(HttpServletResponse response) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = null;
        try {
            fileName = URLEncoder.encode("批量创建账号", "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.error("encode fileName error", e);
        }
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), EmpExcelReq.class).sheet("模版").doWrite(dataEmp());
        } catch (Exception e) {
            log.error("EasyExcel.write error", e);
        }
    }

    private List<EmpExcelReq> dataEmp() {
        EmpExcelReq e = new EmpExcelReq();
        return Arrays.asList(e);
    }


    @Override
    public Boolean uploadEmp(MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), EmpExcelReq.class, new ReadListener<EmpExcelReq>() {
            private static final int BATCH_COUNT = 20000;
            private static final int invokeSize = 500;
            private List<EmpExcelReq> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(EmpExcelReq empExcelReq, AnalysisContext analysisContext) {
                cachedDataList.add(empExcelReq);
                if (cachedDataList.size() >= BATCH_COUNT) {
                    throw new IgnoreException(TechplayErrDtlEnum.TOO_MANY_DATA_ERROR);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                List<MutablePair<List<EmployeeRoleMapping>, List<Employee>>> resData = new ArrayList<>();
                Map<String, String> mtu = new HashMap<>();
                if (CollectionUtils.isNotEmpty(cachedDataList)) {
                    Set<String> dept = cachedDataList.stream().map(EmpExcelReq::getDepartment).collect(Collectors.toSet());
                    Set<String> role = cachedDataList.stream().map(EmpExcelReq::getRole).collect(Collectors.toSet());
                    Set<String> mobile = cachedDataList.stream().map(EmpExcelReq::getMobile).collect(Collectors.toSet());
                    verifyData(dept, role, mobile, cachedDataList);
                    Map<String, Long> rid = getRoleIdList(new ArrayList<>(role));
                    Map<String, Long> did = getDeptIdList(new ArrayList<>(dept));
                    List<List<EmpExcelReq>> batches = Lists.partition(cachedDataList, invokeSize);
                    for (List<EmpExcelReq> batch : batches) {
                        List<Employee> eList = batch.stream().map(t -> {
                            Employee e = new Employee();
                            e.setUserIdentify(UniqueIdentifierGenerator.getUserIdentify());
                            mtu.put(t.getMobile(), e.getUserIdentify());
                            e.setName(t.getName());
                            e.setMobile(t.getMobile());
                            e.setMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(t.getMobile()));
                            e.setDepartmentId(did.get(t.getDepartment()));
                            e.setCreatedTime(LocalDateTime.now());
                            e.setUpdatedTime(LocalDateTime.now());
                            try {
                                Triple<Boolean, String, Long> pair = cisFacadeClientService.syncUserData(e.getName(), e.getMobile(), e.getUserIdentify(), UserContextHolder.getUserIdentify(), 0);
                                if (!pair.getLeft()) {
                                    throw new TechplayException(TechplayErrDtlEnum.SSO_REGISTER_ERROR, e.getMobile());
                                }
                                e.setSsoUserId(pair.getRight());
                            } catch (Exception ex) {
                                log.warn("addEmployee has sso error", ex);
                                throw new TechplayException(TechplayErrDtlEnum.SSO_REGISTER_ERROR, e.getMobile());
                            }
                            return e;
                        }).collect(Collectors.toList());
                        List<EmployeeRoleMapping> rlist = batch.stream().map(t -> {
                            EmployeeRoleMapping e = new EmployeeRoleMapping();
                            e.setUserIdentify(mtu.get(t.getMobile()));
                            e.setRoleId(rid.get(t.getRole()));
                            e.setCreatedTime(LocalDateTime.now());
                            e.setUpdatedTime(LocalDateTime.now());
                            return e;
                        }).collect(Collectors.toList());
                        MutablePair<List<EmployeeRoleMapping>, List<Employee>> lp = new MutablePair<>();
                        lp.setLeft(rlist);
                        lp.setRight(eList);
                        resData.add(lp);
                    }
                    employeeService.saveData(resData);
                } else {
                    throw new IgnoreException(TechplayErrDtlEnum.NODATA_ERROR);
                }
            }

        }).sheet().doRead();
        return true;
    }


    private void verifyData(Set<String> dept, Set<String> role, Set<String> mobile, List<EmpExcelReq> batch) {
        if (CollectionUtils.isNotEmpty(dept)) {
            List<String> dn = employeeService.findNotExistingDepartmentNames(new ArrayList<>(dept));
            if (CollectionUtils.isNotEmpty(dn)) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "组织名不存在: ".concat(dn.toString()));
            }
        }
        if (CollectionUtils.isNotEmpty(role)) {
            List<String> ro = employeeService.findNotExistingRoleNames(new ArrayList<>(role));
            if (CollectionUtils.isNotEmpty(ro)) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "角色名不存在: ".concat(ro.toString()));
            }
        }
        if (CollectionUtils.isEmpty(mobile) || mobile.size() < batch.size()) {
            throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "excel中手机号有重复，请检查");
        }
        verifyField(batch);
        if (CollectionUtils.isNotEmpty(mobile)) {
            List<String> mo = employeeService.findNotExistingMobileNames(new ArrayList<>(mobile));
            if (CollectionUtils.isNotEmpty(mo)) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "重复手机号如下: ".concat(mo.toString()));
            }
        }
    }

    private Map<String, Long> getRoleIdList(List<String> name) {
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Role::getName, name).eq(Role::getIsDeleted, 0);
        List<Role> roles = roleMapper.selectList(wrapper);
        return Optional.ofNullable(roles)
                .map(roleList -> roleList.stream()
                        .collect(Collectors.toMap(Role::getName, Role::getId)))
                .orElse(new HashMap<>());
    }

    private Map<String, Long> getLabelTypeId(List<String> name) {
        LambdaQueryWrapper<LabelCategoryConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(LabelCategoryConfig::getName, name).eq(LabelCategoryConfig::getIsDeleted, 0);
        List<LabelCategoryConfig> roles = labelCategoryConfigMapper.selectList(wrapper);
        return Optional.ofNullable(roles)
                .map(roleList -> roleList.stream()
                        .collect(Collectors.toMap(LabelCategoryConfig::getName, LabelCategoryConfig::getId)))
                .orElse(new HashMap<>());
    }

    private Map<String, Long> getDeptIdList(List<String> name) {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Department::getName, name).eq(Department::getIsDeleted, 0);
        List<Department> roles = departmentMapper.selectList(wrapper);
        return Optional.ofNullable(roles)
                .map(roleList -> roleList.stream()
                        .collect(Collectors.toMap(Department::getName, Department::getId)))
                .orElse(new HashMap<>());
    }

    private void verifyField(List<EmpExcelReq> batch) {
        batch.stream().forEach(t -> {
            if (StringUtils.isBlank(t.getDepartment()) || StringUtils.isBlank(t.getRole()) || StringUtils.isBlank(t.getName()) || StringUtils.isBlank(t.getMobile())) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "这条数据存在缺失值 :" + JsonUtil.toJson(t));
            }
        });
    }

    @Override
    public PageResultResponse<LabelResp> queryLabel(LabelReq req) {
        IPage<LabelConfig> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<LabelConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollectionUtils.isNotEmpty(req.getCategoryId()), LabelConfig::getCategoryId, req.getCategoryId());
        wrapper.in(CollectionUtils.isNotEmpty(req.getIds()), LabelConfig::getId, req.getIds());
        wrapper.like(StringUtils.isNotBlank(req.getName()), LabelConfig::getName, req.getName());
        wrapper.eq(LabelConfig::getIsDeleted, 0);
        wrapper.orderByDesc(LabelConfig::getCreatedTime);
        wrapper.notLike(LabelConfig::getMarkingMethod, "SYS");
        IPage<LabelConfig> pageList = labelConfigMapper.selectPage(page, wrapper);
        if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<LabelResp> res = pageList.getRecords().stream().map(
                    t -> {
                        LabelResp cr = new LabelResp();
                        cr.setId(t.getId());
                        cr.setType(getLabelName(t.getCategoryId(), null));
                        cr.setName(t.getName());
                        cr.setColor(t.getColor());
                        cr.setSolution(t.getSolution());
                        cr.setUpdatedTime(t.getUpdatedTime());
                        cr.setUpdateUser(employeeService.getUserNameForIdentify(t.getUserIdentify()));
                        cr.setDisplayState(t.getDisplayState());
                        return cr;
                    }
            ).collect(Collectors.toList());
            return new PageResultResponse<>(res, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());

        }
        return new PageResultResponse<>();
    }

    private String getLabelName(Long id, String name) {
        LambdaQueryWrapper<LabelCategoryConfig> lqw = Wrappers.lambdaQuery();
        lqw.select(LabelCategoryConfig::getName);
        lqw.eq(LabelCategoryConfig::getIsDeleted, 0);
        lqw.eq(Objects.nonNull(id), LabelCategoryConfig::getId, id);
        lqw.eq(StringUtils.isNotBlank(name), LabelCategoryConfig::getName, name);
        return Optional.ofNullable(labelCategoryConfigMapper.selectOne(lqw))
                .map(LabelCategoryConfig::getName)
                .orElse(null);
    }

    private void getLabelByNameExists(String name) {
        LambdaQueryWrapper<LabelConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LabelConfig::getName, name)
                .eq(LabelConfig::getIsDeleted, 0);
        if (labelConfigMapper.selectCount(wrapper) > 0) {
            throw new IgnoreException(TechplayErrDtlEnum.LABEL_NAME_EXISTS_ERROR);
        }
    }

    @Override
    @Transactional
    public Boolean addLabel(LabelReq labelReq) {
        Long categoryId = labelReq.getId();
        if (Objects.equals(1, labelReq.getType())) {
            if (StringUtils.isBlank(labelReq.getCategory()) || StringUtils.isBlank(labelReq.getName())) {
                throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
            }
            String labelType = getLabelName(null, labelReq.getCategory());
            if (StringUtils.isNotBlank(labelType)) {
                throw new IgnoreException(TechplayErrDtlEnum.LABEL_EXISTS_ERROR);
            }
            getLabelByNameExists(labelReq.getName());
            LabelCategoryConfig lc = new LabelCategoryConfig();
            lc.setName(labelReq.getCategory());
            lc.setCreatedTime(LocalDateTime.now());
            lc.setUpdatedTime(LocalDateTime.now());
            labelCategoryConfigMapper.insert(lc);
            categoryId = lc.getId();
        } else if (Objects.equals(2, labelReq.getType())) {
            getLabelByNameExists(labelReq.getName());
        }
        if (Objects.isNull(categoryId)) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        if (StringUtils.isEmpty(labelReq.getName()) || labelReq.getName().length() > 10) {
            throw new IgnoreException(TechplayErrDtlEnum.LABEL_NAME_LENGTH_ERROR);
        }
        LabelConfig lf = new LabelConfig();
        lf.setCategoryId(categoryId);
        lf.setName(labelReq.getName());
        lf.setSolution(labelReq.getSolution());
        lf.setColor(labelReq.getColor());
        lf.setUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        lf.setCreatedTime(LocalDateTime.now());
        lf.setUpdatedTime(LocalDateTime.now());
        labelConfigMapper.insert(lf);
        return true;
    }

    @Override
    @Transactional
    public Boolean updateLabel(LabelReq labelReq) {
        if (Objects.isNull(labelReq.getLabelId())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        LabelConfig la = new LabelConfig();
        la.setId(labelReq.getLabelId());
        if (Objects.nonNull(labelReq.getDisplayState())) {
            la.setDisplayState(labelReq.getDisplayState());
        }
        if (Objects.equals(1, labelReq.getIsDeleted())) {
            // 删除标签
            la.setIsDeleted(1);
            UserLabelMapping um = new UserLabelMapping();
            um.setIsDeleted(1);
            um.setUpdateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
            um.setUpdatedTime(LocalDateTime.now());
            userLabelMappingMapper.update(
                    um,
                    new LambdaUpdateWrapper<UserLabelMapping>().eq(UserLabelMapping::getLabelId, labelReq.getLabelId())
            );

        } else {
            if (StringUtils.isBlank(labelReq.getColor())) {
                throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
            }
            la.setColor(labelReq.getColor());

            if (StringUtils.isNotBlank(labelReq.getSolution())) {
                la.setSolution(labelReq.getSolution());
            }

        }
        la.setUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        la.setUpdatedTime(LocalDateTime.now());
        labelConfigMapper.updateById(la);
        return true;
    }

    @Override
    public PageResultResponse<LabelUserResp> queryLabelUser(LabelUserReq req) {
        IPage<UserLabelMapping> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<UserLabelMapping> lqw = Wrappers.lambdaQuery();
        lqw.select(
                UserLabelMapping::getId,
                UserLabelMapping::getMobile,
                UserLabelMapping::getMobileEncrypted,
                UserLabelMapping::getCreatedTime,
                UserLabelMapping::getCreateUserIdentify
        );
        lqw.eq(Objects.nonNull(req.getId()), UserLabelMapping::getLabelId, req.getId());
        if (vocConfig.isWysFlag()) {
            lqw.eq(StringUtils.isNotBlank(req.getMobile()), UserLabelMapping::getMobileEncrypted, cisFacadeClientService.getEncodeMobileLocal(req.getMobile()));
        } else {
            lqw.eq(StringUtils.isNotBlank(req.getMobile()), UserLabelMapping::getMobile, req.getMobile());
        }
        lqw.eq(UserLabelMapping::getIsDeleted, 0);
        IPage<UserLabelMapping> pageList = userLabelMappingMapper.selectPage(page, lqw);
        if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<LabelUserResp> res = pageList.getRecords().stream().map(
                    t -> {
                        LabelUserResp cr = new LabelUserResp();
                        cr.setId(t.getId());
                        if (vocConfig.isWysFlag()) {
                            cr.setMobile(cisFacadeClientService.batchDecrypt(t.getMobileEncrypted()));
                        } else {
                            cr.setMobile(t.getMobile());
                        }
                        cr.setCreatedTime(t.getCreatedTime());
                        cr.setCreatedUser(employeeService.getUserNameForIdentify(t.getCreateUserIdentify()));
                        return cr;
                    }
            ).collect(Collectors.toList());
            return new PageResultResponse<>(res, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());

        }
        return new PageResultResponse<>();
    }

    @Override
    @Transactional
    public Boolean updateLabelUser(LabelUserReq labelUserReq) {
        if (Objects.isNull(labelUserReq.getId()) || Objects.isNull(labelUserReq.getIsDeleted()) || labelUserReq.getIsDeleted() != 1) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }

        int result = userLabelMappingMapper.delete(
                new LambdaUpdateWrapper<UserLabelMapping>()
                        .eq(UserLabelMapping::getId, labelUserReq.getId())
        );

        if (result != 1) {
            throw new IgnoreException(TechplayErrDtlEnum.USER_LABEL_DELETED_ERROR);
        }

        return true;
    }

    @Override
    public void downloadLabel(HttpServletResponse response) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = null;
        try {
            fileName = URLEncoder.encode("批量导入标签", "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.error("encode fileName error", e);
        }
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), LabelExcelReq.class).sheet("模版").doWrite(dataLabel());
        } catch (Exception e) {
            log.error("EasyExcel.write error", e);
        }
    }

    private List<LabelExcelReq> dataLabel() {
        LabelExcelReq e = new LabelExcelReq();
        return Arrays.asList(e);
    }

    @Override
    public Boolean uploadLabel(MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), LabelExcelReq.class, new ReadListener<LabelExcelReq>() {
            private static final int BATCH_COUNT = 20000;
            private static final int invokeSize = 500;
            private List<LabelExcelReq> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(LabelExcelReq empExcelReq, AnalysisContext analysisContext) {
                cachedDataList.add(empExcelReq);
                if (cachedDataList.size() >= BATCH_COUNT) {
                    throw new IgnoreException(TechplayErrDtlEnum.TOO_MANY_DATA_ERROR);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                List<List<UserLabelMapping>> res = new ArrayList<>();
                Map<String, Long> nameIds = new HashMap<>();
                if (CollectionUtils.isNotEmpty(cachedDataList)) {
                    Set<String> type = cachedDataList.stream().map(LabelExcelReq::getLabelType).collect(Collectors.toSet());
                    Set<String> name = cachedDataList.stream().map(LabelExcelReq::getLabelName).collect(Collectors.toSet());
                    Map<String, Long> typeIds = getLabelTypeId(new ArrayList<>(type));
                    nameIds = getLabelIdList(new ArrayList<>(name));
                    verifyAndGetId(type, name);
                    verifyTypeAndName(cachedDataList, typeIds);
                }
                List<List<LabelExcelReq>> batches = Lists.partition(cachedDataList, invokeSize);
                for (List<LabelExcelReq> batch : batches) {
                    Map<String, Long> finalNameIds = nameIds;
                    List<UserLabelMapping> cl = batch.stream().map(t -> {
                        UserLabelMapping um = new UserLabelMapping();
                        um.setMobile(t.getMobile());
                        um.setMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(t.getMobile()));
                        um.setLabelId(finalNameIds.get(t.getLabelName()));
                        um.setCreatedTime(LocalDateTime.now());
                        um.setUpdatedTime(LocalDateTime.now());
                        um.setCreateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
                        um.setUpdateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
                        return um;
                    }).collect(Collectors.toList());
                    res.add(cl);
                    employeeService.saveLabelEmp(res);
                }
            }

        }).sheet().doRead();
        return true;
    }

    private void verifyTypeAndName(List<LabelExcelReq> cachedDataList, Map<String, Long> typeIds) {
        cachedDataList.stream().forEach(t -> {
            if (StringUtils.isBlank(t.getLabelName()) || StringUtils.isBlank(t.getLabelType()) || StringUtils.isBlank(t.getMobile())) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "该条数据缺失必要字段：".concat(JsonUtil.toJson(t)));
            }
            //验证标签和类型是否一致
            Long ca = typeIds.get(t.getLabelType());
            LambdaQueryWrapper<LabelConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(LabelConfig::getCategoryId, ca).eq(LabelConfig::getName, t.getLabelName()).eq(LabelConfig::getIsDeleted, 0);
            if (labelConfigMapper.selectCount(wrapper) == 0) {
                throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "该条数据标签类型和标签名不匹配：".concat(JsonUtil.toJson(t)));
            }
        });
    }

    private void verifyAndGetId(Set<String> type, Set<String> name) {
        List<String> s = employeeService.findNotExistingLabelType(new ArrayList<>(type));
        if (CollectionUtils.isNotEmpty(s)) {
            throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "如下标签不存在: " + s.toString());
        }

        List<String> nameList = employeeService.findNotExistingLabelName(new ArrayList<>(name));
        if (CollectionUtils.isNotEmpty(nameList)) {
            throw new IgnoreException(TechplayErrDtlEnum.IMP_DATA_ERROR, "如下标签名不存在: " + nameList.toString());
        }
    }

    private Map<String, Long> getLabelIdList(List<String> name) {
        LambdaQueryWrapper<LabelConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(LabelConfig::getName, name).eq(LabelConfig::getIsDeleted, 0);
        List<LabelConfig> roles = labelConfigMapper.selectList(wrapper);
        return Optional.ofNullable(roles)
                .map(roleList -> roleList.stream()
                        .collect(Collectors.toMap(LabelConfig::getName, LabelConfig::getId)))
                .orElse(new HashMap<>());
    }

    @Override
    public List<IssueCategoryConfig> queryIssList(IssueCategoryConfig req) {
        LambdaQueryWrapper<IssueCategoryConfig> lqw = Wrappers.lambdaQuery();
        lqw.select(
                IssueCategoryConfig::getId,
                IssueCategoryConfig::getName,
                IssueCategoryConfig::getParentId,
                IssueCategoryConfig::getLevel,
                IssueCategoryConfig::getIsHide
        );
        lqw.eq(Objects.nonNull(req.getParentId()), IssueCategoryConfig::getParentId, req.getParentId());
        lqw.eq(Objects.nonNull(req.getId()), IssueCategoryConfig::getId, req.getId());
        lqw.eq(Objects.nonNull(req.getLevel()), IssueCategoryConfig::getLevel, req.getLevel());
        lqw.eq(IssueCategoryConfig::getIsDeleted, 0);
        return issueCategoryConfigMapper.selectList(lqw);
    }

    @Override
    public LoginResp getUserResource() {
        String userIdentify = UserContextHolder.getUserContext().getUserIdentify();
        List<String> resourceAuth = employeeMapper.getUserWithResourceByUserIdentify(userIdentify);
        LoginResp l = new LoginResp();
        l.setResource(resourceAuth);
        return l;
    }

    @Override
    public List<LabelCategoryConfig> queryLabelType() {
        LambdaQueryWrapper<LabelCategoryConfig> lqw = Wrappers.lambdaQuery();
        lqw.select(
                LabelCategoryConfig::getId,
                LabelCategoryConfig::getName
        );
        lqw.eq(LabelCategoryConfig::getIsDeleted, 0);
        return labelCategoryConfigMapper.selectList(lqw);
    }

    @Override
    @Transactional
    public Boolean addOperateLog(OperateLog op) {
        try {
            operateLogMapper.insert(op);
        } catch (Exception e) {
            log.error("addOperateLog error", e);
            return false;
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean addUserLabel(LabelReq labelReq) {
        if (StringUtils.isBlank(labelReq.getMobile()) || CollectionUtils.isEmpty(labelReq.getIds())) {
            throw new IgnoreException(TechplayErrDtlEnum.FIELD_ERROR);
        }
        try {
            labelReq.getIds().forEach(t -> {
                UserLabelMapping um = new UserLabelMapping();
                um.setMobile(labelReq.getMobile());  //todo @Deprecated
                um.setMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(labelReq.getMobile()));
                um.setLabelId(t);
                um.setCreatedTime(LocalDateTime.now());
                um.setUpdatedTime(LocalDateTime.now());
                um.setCreateUserIdentify(UserContextHolder.getUserIdentify());
                um.setUpdateUserIdentify(UserContextHolder.getUserIdentify());
                userLabelMappingMapper.insert(um);
            });
        } catch (Exception e) {
            throw new IgnoreException(TechplayErrDtlEnum.MOBILE_LABEL_ERROR);
        }
        return true;
    }

    @Override
    public List<ControlAuth> queryControlList() {
        LambdaQueryWrapper<ControlAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ControlAuth::getIsDeleted, 0);
        List<ControlAuth> res = controlAuthMapper.selectList(wrapper);
        return Optional.ofNullable(res).orElse(Collections.emptyList());
    }

    @Override
    public PageResultResponse<DictResp> queryDictList(DictReq req) {
        IPage<DictConfig> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<DictConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(req.getId()), DictConfig::getId, req.getId())
                .eq(StringUtils.isNotBlank(req.getName()), DictConfig::getName, req.getName())
                .eq(DictConfig::getIsDeleted, 0);
        IPage<DictConfig> pageList = dictConfigMapper.selectPage(page, wrapper);
        if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<DictResp> respList = pageList.getRecords().stream().map(t -> {
                DictResp rr = new DictResp();
                rr.setId(t.getId());
                rr.setName(t.getName());
                rr.setDescription(t.getDescription());
                rr.setCreatedTime(t.getCreatedTime());
                rr.setUpdateTime(t.getUpdatedTime());
                rr.setCreateUser(employeeService.getUserNameForIdentify(t.getCreateUser()));
                rr.setUpdateUser(employeeService.getUserNameForIdentify(t.getUpdateUser()));
                rr.setDetail(getDictDetail(t.getId()));
                return rr;
            }).collect(Collectors.toList());

            return new PageResultResponse<>(respList, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        }
        return new PageResultResponse<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean opertaDict(DictResp req) {
        switch (req.getType()) {
            case 1:
                DictConfig dictConfig = createDictConfig(req);
                dictConfigMapper.insert(dictConfig);
                saveDictDetails(dictConfig, req.getDetail());
                req.setId(dictConfig.getId());
                break;

            case 2:
                List<DictDetail> dictDetails = dictDetailMapper.queryByDictConfigId(req.getId());
                List<Long> updateIds = req.getDetail().stream().map(DictDetail::getId).collect(Collectors.toList());

                //新增的字典明细
                List<DictDetail> insertList = req.getDetail().stream().filter(t -> t.getId() == null).collect(Collectors.toList());
                saveDictDetails(new DictConfig(req.getId()), insertList);

                //删除的字典明细
                List<DictDetail> deleteList = dictDetails.stream().filter(t -> !updateIds.contains(t.getId())).collect(Collectors.toList());
                deleteList.forEach(t -> {
                    dictDetailMapper.update(
                            null,
                            Wrappers.<DictDetail>lambdaUpdate()
                                    .eq(DictDetail::getDictConfigId, req.getId())
                                    .set(DictDetail::getIsDeleted, 1)
                    );
                });

                //修改的字典明细
                List<DictDetail> updateList = req.getDetail().stream().filter(t -> t.getId() != null).collect(Collectors.toList());
                for (DictDetail detail : updateList) {
                    dictDetailMapper.updateById(detail);
                }

//                Optional.ofNullable(req.getId())
//                        .ifPresent(id -> {
//                            dictDetailMapper.update(
//                                    null,
//                                    Wrappers.<DictDetail>lambdaUpdate()
//                                            .eq(DictDetail::getDictConfigId, id)
//                                            .set(DictDetail::getIsDeleted, 1)
//                            );
//                            saveDictDetails(new DictConfig(id), req.getDetail());
//                        });
                break;

            case 3:
                Optional.ofNullable(req.getId())
                        .ifPresent(id -> {
                            dictConfigMapper.deleteById(id);
                            dictDetailMapper.delete(
                                    Wrappers.<DictDetail>lambdaQuery()
                                            .eq(DictDetail::getDictConfigId, id)
                            );
                        });
                break;

            default:
                throw new IgnoreException(TechplayErrDtlEnum.DATABASE_ERROR);
        }
        dictDataCache.refreshCache(req.getId());
        return true;
    }

    @Override
    public LoginResp getUserInfo() {
        String userIdentify = UserContextHolder.getUserIdentify();
        Employee e = employeeMapper.getDepartmentIdByUserIdentify(userIdentify);
        Department department = departmentMapper.selectById(e.getDepartmentId());
        Integer departmentType = department == null ? 1 : department.getDepartmentType();
        return new LoginResp(null, e.getName(), userIdentify, departmentType, e.getId(), e.getRealTimeAssistance(), e.getSsoUserId(), e.getMobile());
    }

    private DictConfig createDictConfig(DictResp req) {
        LambdaQueryWrapper<DictConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictConfig::getName, req.getName())
                .eq(DictConfig::getIsDeleted, 0);
        if (dictConfigMapper.selectCount(wrapper) > 0) {
            throw new IgnoreException(TechplayErrDtlEnum.DATA_EXISTS_ERROR);
        }
        DictConfig dictConfig = new DictConfig();
        dictConfig.setName(req.getName());
        dictConfig.setDescription(req.getDescription());
        String userIdentify = UserContextHolder.getUserIdentify();
        LocalDateTime now = LocalDateTime.now();
        dictConfig.setCreateUser(userIdentify);
        dictConfig.setUpdateUser(userIdentify);
        dictConfig.setCreatedTime(now);
        dictConfig.setUpdatedTime(now);
        return dictConfig;
    }

    /**
     * @param null:
     * @return null
     * <AUTHOR>
     * @description hasDuplicates 保证同一父字典无重复，selectCount保证全局无重复
     * @date 2024/4/11 11:45
     */
    public void saveDictDetails(DictConfig dictConfig, List<DictDetail> details) {
        if (dictConfig.getId() != null && !details.isEmpty()) {
            String userIdentify = UserContextHolder.getUserIdentify();
            LocalDateTime now = LocalDateTime.now();
            List<String> names = details.stream().map(DictDetail::getDictKey).collect(Collectors.toList());
            boolean hasDuplicates = names.stream()
                    .distinct()
                    .count() != details.size();
            if (hasDuplicates) {
                throw new IgnoreException(TechplayErrDtlEnum.DATA_EXISTS_ERROR);
            }
            LambdaQueryWrapper<DictDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(DictDetail::getDictKey, names)
                    .eq(DictDetail::getDictConfigId, dictConfig.getId())
                    .eq(DictDetail::getIsDeleted, 0);
            if (dictDetailMapper.selectCount(wrapper) > 0) {
                throw new IgnoreException(TechplayErrDtlEnum.DATA_EXISTS_ERROR);
            }
            List<DictDetail> detailList = details.stream().map(detail -> {
                DictDetail newDetail = new DictDetail();
                newDetail.setDictConfigId(dictConfig.getId());
                newDetail.setDictKey(detail.getDictKey());
                newDetail.setDictValue(detail.getDictValue());
                newDetail.setUserIdentify(userIdentify);
                newDetail.setCreatedTime(now);
                newDetail.setUpdatedTime(now);
                return newDetail;
            }).collect(Collectors.toList());

            detailService.saveBatchs(detailList);
        }
    }

    private List<DictDetail> getDictDetail(Long id) {
        LambdaQueryWrapper<DictDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictDetail::getIsDeleted, 0)
                .eq(DictDetail::getDictConfigId, id);
        List<DictDetail> res = dictDetailMapper.selectList(wrapper);
        return Optional.ofNullable(res).orElse(Collections.emptyList());
    }
}