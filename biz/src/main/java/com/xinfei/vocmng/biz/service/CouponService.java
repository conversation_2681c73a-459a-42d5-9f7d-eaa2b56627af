/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.UserCouponDetailReq;
import com.xinfei.vocmng.biz.rr.request.UserCouponReq;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;

import java.util.List;

/**
 * <AUTHOR> 2024/8/12 18:42
 * 优惠卷 CouponService
 */

public interface CouponService {

    ApiResponse<Paging<UserCouponDto>> getUserCoupon(UserCouponReq userCouponReq);

    ApiResponse<List<UserCouponDetailDto>> getUserCouponDetail(UserCouponDetailReq userCouponDetailReq);

}