/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.google.common.collect.Lists;
import com.xinfei.supervip.interfaces.model.admin.dto.RightsReceiveLogAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.ReceiveDetailDto;
import com.xinfei.vipcore.facade.rr.dto.ReceiveLogAdminDto;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.mapstruct.VipCardConverter;
import com.xinfei.vocmng.biz.rr.dto.MemberCardUseInfoDto;
import com.xinfei.vocmng.biz.rr.dto.ReceiveDetailsDto;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardUsedListRequest;
import com.xinfei.vocmng.biz.service.CardUsageService;
import com.xinfei.vocmng.itl.MemberInterestFeignClient;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.itl.rr.MemberCardUseInfo;
import com.xinfei.vocmng.itl.rr.MemberCardUsedRequest;
import com.xinfei.vocmng.itl.rr.MemberInterestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CardUsageServiceImpl, v 0.1 2025-04-29 17:52 junjie.yan Exp $
 */
@Component
@Slf4j
public class CardUsageServiceImpl implements CardUsageService {

    @Resource
    private VipFacadeClientImpl vipFacadeClient;

    @Autowired
    private MemberInterestFeignClient memberInterestFeignClient;

    @Autowired
    private VocConfig vocConfig;

    @Override
    public List<MemberCardUseInfoDto> queryCardUsedInfo(QueryMemberCardUsedListRequest request) {
        List<MemberCardUseInfoDto> memberCardUseInfoDtos = new ArrayList<>();
        List<ReceiveLogAdminDto> receiveLogAdmins = new ArrayList<>();
        if (request.getType() == 1 || request.getType() == 2) {
            MemberCardUsedRequest usedRequest = buildRequest(request);
            MemberInterestResponse<List<MemberCardUseInfo>> response = memberInterestFeignClient.queryMemberCardUsedInfo(usedRequest);
            if (!response.isSuccess()) {
                log.error("query card used info failed, request={}, response={}", usedRequest, response);
                return Collections.emptyList();
            }
            memberCardUseInfoDtos.addAll(convertMemberCardUseInfo(response.getData()));
            return memberCardUseInfoDtos;
        } else if (request.getType() == 3) {
            receiveLogAdmins = vipFacadeClient.receiveLogAdmin(request.getCardId(), 1);
        } else if (request.getType() == 4 && vocConfig.getIsSuperVipCard()) {
            List<RightsReceiveLogAdminDTO> rightsReceiveLog = vipFacadeClient.queryVipRightsReceiveLogList(request.getCardId());
            if (!CollectionUtils.isEmpty(rightsReceiveLog)) {
                rightsReceiveLog.stream()
                        .map(VipCardConverter.INSTANCE::receiveLogAdminDtoToRightsLogDTO)
                        .forEach(receiveLogAdmins::add);
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(receiveLogAdmins)) {
            for (ReceiveLogAdminDto receiveLogAdminDto : receiveLogAdmins) {
                MemberCardUseInfoDto memberCardUseInfo = new MemberCardUseInfoDto();
                BeanUtils.copyProperties(receiveLogAdminDto, memberCardUseInfo);
                List<ReceiveDetailsDto> receiveDetailList = new ArrayList<>();
                BigDecimal itemPriceSum = BigDecimal.ZERO;
                BigDecimal itemCostSum = BigDecimal.ZERO;
                for (ReceiveDetailDto receiveDetailDto : receiveLogAdminDto.getReceiveDetailList()) {
                    ReceiveDetailsDto receiveDetailsDto = new ReceiveDetailsDto();
                    BeanUtils.copyProperties(receiveDetailDto, receiveDetailsDto);
                    if (receiveDetailDto.getItemPrice() != null) {
                        receiveDetailsDto.setItemPrice(convert(receiveDetailDto.getItemPrice()));
                        itemPriceSum = itemPriceSum.add(receiveDetailsDto.getItemPrice());
                    }
                    if (receiveDetailDto.getItemCost() != null) {
                        receiveDetailsDto.setItemCost(convert(receiveDetailDto.getItemCost()));
                        itemCostSum = itemCostSum.add(receiveDetailsDto.getItemCost());
                    }
                    receiveDetailList.add(receiveDetailsDto);
                }
                memberCardUseInfo.setItemPriceSum(itemPriceSum);
                memberCardUseInfo.setItemCostSum(itemCostSum);
                memberCardUseInfo.setReceiveDetailList(receiveDetailList);
                memberCardUseInfoDtos.add(memberCardUseInfo);
            }
        }
        return memberCardUseInfoDtos;
    }

    private static BigDecimal convert(Integer amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal originAmount = new BigDecimal(amount);
        return originAmount.divide(new BigDecimal(100));
    }

    private MemberCardUsedRequest buildRequest(QueryMemberCardUsedListRequest request) {
        MemberCardUsedRequest result = new MemberCardUsedRequest();
        BeanUtils.copyProperties(request, result);

        return result;
    }

    private List<MemberCardUseInfoDto> convertMemberCardUseInfo(List<MemberCardUseInfo> useInfoList) {
        if (CollectionUtils.isEmpty(useInfoList)) {
            return Collections.emptyList();
        }

        List<MemberCardUseInfoDto> result = Lists.newArrayListWithCapacity(useInfoList.size());
        MemberCardUseInfoDto dto;
        for (MemberCardUseInfo cardUseInfo : useInfoList) {
            dto = new MemberCardUseInfoDto();
            BeanUtils.copyProperties(cardUseInfo, dto);
            result.add(dto);
        }

        return result;
    }

}