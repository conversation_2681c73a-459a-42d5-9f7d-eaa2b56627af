/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 担保费模版计算
 *
 * <AUTHOR>
 * @version $ GuaranteeReductionStrategy, v 0.1 2024/3/27 18:35 wancheng.qu Exp $
 */
@Service
@Slf4j
public class GuaranteeReductionStrategy implements CostCalculationStrategy<BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal> {

    @Override
    public ControlRes<BigDecimal, BigDecimal> calculateAmount(ControlItemValue<BigDecimal, BigDecimal, BigDecimal> values, Object... params) {

        BigDecimal o = values.getO();
        BigDecimal t = values.getT();
        BigDecimal h = values.getH();

        // 根据提供的逻辑计算金额
        BigDecimal init = BigDecimal.ZERO;
        BigDecimal exempt = BigDecimal.ZERO;
        BigDecimal calculation = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof PlanDetailDto) {
                if (i == 0) {
                    init = ((PlanDetailDto) params[i]).getGuaranteeFee();
                }

                if (i == 1) {
                    exempt = ((PlanDetailDto) params[i]).getGuaranteeFee();
                }
            }
            if (params[i] instanceof CalculateFee) {
                if (i == 2) {
                    calculation = ((CalculateFee) params[i]).getCalGuaranteeFee();
                }
            }
        }

        log.info("calculateAmount process担保费:初始金额:" + init + "历史减免:" + exempt + "试算值:" + calculation + "上限百分比：" + o + "下限百分比：" + t);

        ControlRes<BigDecimal, BigDecimal> cr = new ControlRes<>();

        BigDecimal lower = init.multiply(t).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upper = init.multiply(o).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        upper = upper.subtract(exempt);

        if (lower.compareTo(upper) > 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        BigDecimal hb = h.setScale(2, RoundingMode.HALF_UP);
        if (hb.compareTo(calculation) >= 0) {
            hb = calculation;
        }

        if (hb.compareTo(upper) >= 0) {
            cr.setLeft(lower);
            cr.setRight(upper);
        } else if (hb.compareTo(upper) < 0 && hb.compareTo(lower) >= 0) {
            cr.setLeft(lower);
            cr.setRight(hb);
        } else if (hb.compareTo(lower) < 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
        }

        return cr;

        // 添加额外参数
//        for (Object param : params) {
//            if (param instanceof Number) {
//                BigDecimal paramBigDecimal = new BigDecimal(param.toString());
//                result = result.add(paramBigDecimal);
//            }
//        }
    }
}