package com.xinfei.vocmng.biz.workorder.domain.workorder.entity;

import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.BusinessData;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.CustomerInfo;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExtendData;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 工单数据实体
 * 存储工单的客户信息、业务数据和扩展数据
 *
 * <AUTHOR>
 * @version $ WorkOrderDataEntity, v 0.1 2025/08/11 WorkOrderDataEntity Exp $
 */
@Data
@Builder
public class WorkOrderDataEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 客户信息
     */
    private CustomerInfo customerInfo;

    /**
     * 业务数据
     */
    private BusinessData businessData;

    /**
     * 扩展数据
     */
    private ExtendData extendData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 验证工单数据实体
     */
    public void validate() {
        if (Objects.isNull(workOrderId)) {
            throw new IllegalArgumentException("工单ID不能为空");
        }
        
        if (Objects.isNull(customerInfo)) {
            throw new IllegalArgumentException("客户信息不能为空");
        }
        
        // 验证业务数据
        if (Objects.nonNull(businessData)) {
            businessData.validate();
        }
    }
}
