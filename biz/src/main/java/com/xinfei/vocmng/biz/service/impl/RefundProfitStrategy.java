/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ PenaltyReductionStrategy, v 0.1 2024/3/27 18:37 wancheng.qu Exp $
 */
@Service
@Slf4j
public class RefundProfitStrategy {

    public ControlRes<BigDecimal, BigDecimal> calculateAmount(BigDecimal o, BigDecimal t, Object... params) {

        BigDecimal init = BigDecimal.ZERO;
        BigDecimal exempt = BigDecimal.ZERO;
        BigDecimal calculation = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof BigDecimal) {
                if (i == 0) {
                    init = (BigDecimal) params[i];
                }

                if (i == 1) {
                    exempt = (BigDecimal) params[i];
                }

                if (i == 2) {
                    calculation = (BigDecimal) params[i];
                }
            }
        }
        log.info("RefundProfitStrategy 实还金额:" + init + "历史抵扣退款:" + exempt + "可退金额:" + calculation);
        return ReductionStrategyUtil.calculateAmount(t, o, init, exempt, calculation);
    }
}