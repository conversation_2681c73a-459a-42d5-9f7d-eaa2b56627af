/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.component;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PreDestroy;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @version $ SlidingWindowRateLimiter, v 0.1 2024/3/13 20:08 wancheng.qu Exp $
 */
@Component
public class SlidingWindowRateLimiter {

    private final ConcurrentHashMap<String, UserRateLimiter> limiters = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler;

    public SlidingWindowRateLimiter(@Value("${rateLimiter.cleanupPeriod:1}") long cleanupPeriod,
                                    @Value("${rateLimiter.schedulerThreads:1}") int schedulerThreads) {
        scheduler = Executors.newScheduledThreadPool(schedulerThreads);
        scheduler.scheduleAtFixedRate(this::cleanup, cleanupPeriod, cleanupPeriod, TimeUnit.HOURS);
    }


    public boolean tryAcquire(String userId) {
        return limiters.computeIfAbsent(userId, key -> new UserRateLimiter()).tryAcquire();
    }

    private void cleanup() {
        limiters.entrySet().removeIf(entry -> entry.getValue().isInactive());
    }

    private static class UserRateLimiter {
        private final Deque<Long> window = new ArrayDeque<>();
        private final ReentrantLock lock = new ReentrantLock();
        private static final long WINDOW_SIZE = 20000;
        private static final int MAX_REQUESTS = 1; // 窗口大小内允许的最大请求数
        private volatile long lastActiveTime = System.currentTimeMillis(); // 记录最后活跃时间

        public boolean tryAcquire() {
            lock.lock();
            try {
                long now = System.currentTimeMillis();
                lastActiveTime = now;
                while (!window.isEmpty() && now - window.peek() > WINDOW_SIZE) {
                    window.poll();
                }
                if (window.size() < MAX_REQUESTS) {
                    window.offer(now);
                    return true;
                }
                return false;
            } finally {
                lock.unlock();
            }
        }

        public boolean isInactive() {
            return System.currentTimeMillis() - lastActiveTime > WINDOW_SIZE;
        }
    }

    @PreDestroy
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
                // 恢复中断状态
                Thread.currentThread().interrupt();
            }
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }


}
