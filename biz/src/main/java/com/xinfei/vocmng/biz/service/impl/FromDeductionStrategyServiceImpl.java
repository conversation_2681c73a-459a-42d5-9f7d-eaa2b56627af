/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.FeeStrategyService;
import com.xinfei.vocmng.biz.strategy.dto.EmptyStrategyInput;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ FeeStrategyServiceImpl, v 0.1 2024-12-04 16:16 junjie.yan Exp $
 */
@Slf4j
@Component
public class FromDeductionStrategyServiceImpl extends FeeStrategyService<EmptyStrategyInput> {

    @Override
    public Map<String, Object> getValue(Map<String, String> dictKeys, EmptyStrategyInput strategyInput) {
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String> dictKey : dictKeys.entrySet()) {
            Object value = null;
            switch (dictKey.getKey()) {
                case "Customer_Service_Group"://客服角色
                    value = UserContextHolder.getUserContext().getRole();
                    break;
                default:
                    break;
            }
            if (value != null) {
                map.put(dictKey.getKey(), value);
            }
        }
        return map;
    }
}