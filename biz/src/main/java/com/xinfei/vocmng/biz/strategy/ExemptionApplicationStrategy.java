package com.xinfei.vocmng.biz.strategy;

import com.xinfei.vocmng.biz.rr.CalculationContext;
import com.xinfei.vocmng.biz.rr.response.ExemptionResponse;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
public interface ExemptionApplicationStrategy {
    /**
     * 计算并将结果应用到 Exemption 对象上
     * @param context 计算所需的上下文信息
     * @param exemption 需要被更新的 Exemption 对象
     */
    void apply(CalculationContext context, ExemptionResponse exemption);
}
