/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.template;

import com.xinfei.vocmng.biz.log.QueryDigestLog;

/**
 * 查询模板回调接口
 *
 * <AUTHOR>
 * @version $ QueryCallBack, v 0.1 2023/8/28 20:31 <PERSON>yan.Huang Exp $
 */
public interface QueryCallBack {

    /**
     * 检查输入参数。
     */
    void checkParameter();

    /**
     * 执行查询。
     */
    void doQuery();

    /**
     * 创建摘要日志
     *
     * @return 摘要日志
     */
    QueryDigestLog buildDigestLog();
}
