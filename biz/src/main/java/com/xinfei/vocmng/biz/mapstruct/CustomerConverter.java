/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.cisaggs.facade.rr.dto.ContactDTO;
import com.xinfei.cisaggs.facade.rr.dto.FamilyEduDTO;
import com.xinfei.cisaggs.facade.rr.dto.JobDTO;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.itl.rr.dto.BankCardDto;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.ext.info.model.Contact;
import com.xyf.ext.info.model.FamilyEdu;
import com.xyf.ext.info.model.Job;
import com.xyf.user.auth.dto.response.OcrFaceImagesResponse;
import groovy.util.logging.Slf4j;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface CustomerConverter {

    CustomerConverter INSTANCE = Mappers.getMapper(CustomerConverter.class);

    CustomerDto userToCustomer(UserSearchDTO userSearchDTO);

    CustomerDetailDto userToCustomerDetail(UserSearchDTO userSearchDTO);

    BankDto bankCardDtoToBankDto(BankCardDto bankCardDto);

    @Mapping(target = "bindTime", ignore = true)
    @Mapping(source = "bindTime", target = "bindCardTime")
    BankDto bankCardResponseToBankDto(BankCardResponse bankCardDto);

    FamilyEduDto familyEduToFamilyEduDto(FamilyEdu familyEdu);

    FamilyEduDto newFamilyEduToFamilyEduDto(FamilyEduDTO familyEdu);

    JobDto jobToJobDto(Job job);

    JobDto newJobToJobDto(JobDTO job);

    ContactDto contactToContactDto(Contact contact);

    ContactDto newContactToContactDto(ContactDTO contact);

    @Mapping(source = "idCardFront", target = "idCardFront", qualifiedByName = "baseEncode")
    @Mapping(source = "idCardBack", target = "idCardBack", qualifiedByName = "baseEncode")
    @Mapping(source = "idCardHead", target = "idCardHead", qualifiedByName = "baseEncode")
    @Mapping(source = "faceBest", target = "faceBest", qualifiedByName = "baseEncode")
    ImagesDto imagesResponseToImagesDto(OcrFaceImagesResponse imagesResponse);

    @Named("baseEncode")
    static String image2Base64(String imgHttpUrl) {
        byte[] imgBytes = readRemoteImg(imgHttpUrl);
        if (null == imgBytes) {
            return null;
        }
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(imgBytes);
    }

    static byte[] readRemoteImg(String imgHttpUrl) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        BufferedInputStream bis = null;
        HttpURLConnection httpUrl = null;
        URL url;
        byte[] buf = new byte[1024];
        int size;
        try {
            url = new URL(imgHttpUrl);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            bis = new BufferedInputStream(httpUrl.getInputStream());
            while ((size = bis.read(buf)) != -1) {
                baos.write(buf, 0, size);
            }
            return baos.toByteArray();
        } catch (Exception e) {
//            log.error("readRemoteImg fail",e);
        } finally {
            try {
                bis.close();
                baos.close();
                httpUrl.disconnect();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return null;
    }


    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    @AfterMapping
    default void logOutStatus(UserSearchDTO userSearchDTO, @MappingTarget CustomerDto customerDto) {
        if (userSearchDTO != null && userSearchDTO.getOperateStatus() != null && userSearchDTO.getOperateStatus() == 2) {
            customerDto.setStatus(userSearchDTO.getOperateStatus());
        }
    }

    @AfterMapping
    default void logOutStatus(UserSearchDTO userSearchDTO, @MappingTarget CustomerDetailDto customerDetailDto) {
        if (userSearchDTO != null && userSearchDTO.getOperateStatus() != null && userSearchDTO.getOperateStatus() == 2) {
            customerDetailDto.setStatus(userSearchDTO.getOperateStatus());
        }
    }

}