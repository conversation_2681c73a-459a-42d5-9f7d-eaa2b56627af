/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.biz.rr.request.RepaymentProcessReq;
import com.xinfei.vocmng.biz.service.FeeStrategyService;
import com.xinfei.vocmng.biz.strategy.dto.ToDeductionStrategyInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ FeeStrategyServiceImpl, v 0.1 2024-12-04 16:16 junjie.yan Exp $
 */
@Slf4j
@Component
public class ToDeductionStrategyServiceImpl extends FeeStrategyService<ToDeductionStrategyInput> {

    @Override
    public Map<String, Object> getValue(Map<String, String> dictKeys, ToDeductionStrategyInput strategyInput) {
        if (strategyInput == null || StringUtils.isBlank(strategyInput.getLoanNo())) {
            throw new IllegalArgumentException("ToDeductionStrategy: loanNo 不能为空");
        }

        String loanNo = strategyInput.getLoanNo();
        RepaymentProcessReq req = strategyInput.getRepaymentProcessReq();

        ManageOrderDetailDTO orderDetailDTO = getOrderInfoByLoanNo(loanNo);

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String> dictKey : dictKeys.entrySet()) {
            Object value = null;
            switch (dictKey.getKey()) {
                case "Investors"://资方
                    value = StringUtils.isEmpty(orderDetailDTO.getFundSource()) ? "" : orderDetailDTO.getFundSource();
                    break;
                case "Settle"://是否结清
                    if (req != null) {
                        value = req.getRepayType() == 2;
                    }
                    break;
                default:
                    break;
            }
            if (value != null) {
                map.put(dictKey.getKey(), value);
            }
        }

        return map;
    }

    @Override
    public String customizeKeyValues(Map<String, Object> dictKeys, ToDeductionStrategyInput strategyInput) {
        return strategyInput.getLoanNo();
    }

}