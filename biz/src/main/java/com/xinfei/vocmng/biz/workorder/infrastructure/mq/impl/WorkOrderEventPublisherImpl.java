package com.xinfei.vocmng.biz.workorder.infrastructure.mq.impl;

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.biz.workorder.infrastructure.mq.WorkOrderEventPublisher;
import com.xinfei.vocmng.itl.constants.MQConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 工单事件发布器实现类
 * 负责将领域事件发布到MQ
 *
 * <AUTHOR>
 * @version $ WorkOrderEventPublisherImpl, v 0.1 2025/07/30 WorkOrderEventPublisherImpl Exp $
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WorkOrderEventPublisherImpl implements WorkOrderEventPublisher {

    private final RocketMQTemplate rocketMQTemplate;

    @Override
    public void publishEvent(WorkOrderEvent event) {
        if (Objects.isNull(event)) {
            log.warn("工单事件为空，跳过发布");
            return;
        }
        WorkOrderEventType eventType = event.getEventType();
        switch (eventType) {
            case CREATED:
//                publishWorkOrderCreateEvent(event);
                break;
            case STATUS_CHANGED:
                publishWorkOrderStatusChangeEvent(event);
                break;
            default:
//                log.warn("未知的事件类型: {}", eventType);
                break;
        }
    }

//    @Override
//    public void publishWorkOrderCreateEvent(WorkOrderEvent event) {
//        if (Objects.isNull(event)) {
//            log.warn("工单创建事件为空，跳过发布");
//            return;
//        }
//        String messageBody = JSON.toJSONString(event);
//        String messageKey = generateMessageKey(event);
//        // 统一使用STATUS_TOPIC，通过事件类型区分
//        String destination = MQConstants.WORK_ORDER.STATUS_TOPIC + ":" + MQConstants.WORK_ORDER.CREATE_TAG;
//
//        SendResult sendResult = rocketMQTemplate.syncSend(destination,
//                MessageBuilder.withPayload(messageBody).setHeader("KEYS", messageKey).build());
//
//        log.info("发布工单创建事件成功, eventId: {}, orderNo: {}, msgId: {}",
//                event.getEventId(), event.getOrderNo(), sendResult.getMsgId());
//
//    }

    @Override
    public void publishWorkOrderStatusChangeEvent(WorkOrderEvent event) {
        if (Objects.isNull(event)) {
            log.warn("工单状态变更事件为空，跳过发布");
            return;
        }
        String messageBody = JSON.toJSONString(event);
        // 统一使用STATUS_TOPIC，通过事件类型区分
        String destination = MQConstants.WORK_ORDER.STATUS_TOPIC + ":" + MQConstants.WORK_ORDER.STATUS_CHANGE_TAG;

        SendResult sendResult = rocketMQTemplate.syncSend(destination,
                MessageBuilder.withPayload(messageBody).build());

        log.info("发布工单状态变更事件成功, eventId: {}, orderNo: {}, msgId: {}",
                event.getEventId(), event.getOrderNo(), sendResult.getMsgId());
    }

}
