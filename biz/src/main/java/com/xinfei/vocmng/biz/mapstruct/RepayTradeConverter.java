/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.repaytrade.facade.rr.dto.ReductionDetailDto;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.vocmng.biz.rr.dto.HuttaPlanDetailDto;
import com.xinfei.vocmng.biz.rr.dto.RepayReductionDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR> 2024/7/16 下午2:36
 * RepayTradeConverter
 */
@Mapper
public interface RepayTradeConverter {

    RepayTradeConverter INSTANCE = Mappers.getMapper(RepayTradeConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    RepayReductionDto ReductionDetailDtoToRepayReductionDto(ReductionDetailDto reductionDetailDto);
}