/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vipcore.facade.rr.dto.RefundListDto;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardRefundRecordDto;
import com.xinfei.vocmng.itl.rr.DiversionOrderInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface DiversionOrderConverter {
    DiversionOrderConverter INSTANCE = Mappers.getMapper(DiversionOrderConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    DiversionOrderDto diversionOrderToDiversionOrderDto(DiversionOrderInfo diversionOrderInfo);
}