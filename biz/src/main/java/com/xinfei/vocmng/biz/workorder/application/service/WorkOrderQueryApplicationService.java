package com.xinfei.vocmng.biz.workorder.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.biz.workorder.interfaces.dto.WorkOrderListQueryRequest;
import com.xinfei.vocmng.biz.workorder.interfaces.dto.WorkOrderListQueryResponse;
import com.xinfei.vocmng.dal.mapper.WorkOrderMiddlewareMapper;
import com.xinfei.vocmng.dal.po.WorkOrderMiddlewarePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工单查询应用服务
 * 提供工单列表查询等功能
 *
 * <AUTHOR>
 * @version $ WorkOrderQueryApplicationService, v 0.1 2025/08/11 WorkOrderQueryApplicationService Exp $
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkOrderQueryApplicationService {

    private final WorkOrderMiddlewareMapper workOrderMiddlewareMapper;

    /**
     * 查询工单列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public PageResultResponse<WorkOrderListQueryResponse> queryWorkOrderList(WorkOrderListQueryRequest request) {
        log.info("开始查询工单列表, request: {}", request);
        // 构建查询条件
        LambdaQueryWrapper<WorkOrderMiddlewarePO> queryWrapper = buildQueryWrapper(request);

        // 构建分页参数
        Page<WorkOrderMiddlewarePO> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 设置排序
        if (StringUtils.isNotBlank(request.getOrderBy())) {
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.desc(request.getOrderBy()));
            } else {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.asc(request.getOrderBy()));
            }
        }

        // 执行查询
        IPage<WorkOrderMiddlewarePO> pageResult = workOrderMiddlewareMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<WorkOrderListQueryResponse> responseList = pageResult.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // 构建分页结果
        PageResultResponse<WorkOrderListQueryResponse> result = new PageResultResponse<>();
        result.setCurrentPage(pageResult.getCurrent());
        result.setPageSize(pageResult.getSize());
        result.setTotal(pageResult.getTotal());
        result.setTotalPage(pageResult.getPages());
        result.setList(responseList);

        return result;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WorkOrderMiddlewarePO> buildQueryWrapper(WorkOrderListQueryRequest request) {
        LambdaQueryWrapper<WorkOrderMiddlewarePO> queryWrapper = new LambdaQueryWrapper<>();

        // 转接资方列表
        if (CollectionUtils.isNotEmpty(request.getFunderCodes())) {
            queryWrapper.in(WorkOrderMiddlewarePO::getFunderCode, request.getFunderCodes());
        }

        // 客户来电号码
        if (StringUtils.isNotBlank(request.getCustomerPhone())) {
            queryWrapper.like(WorkOrderMiddlewarePO::getCustomerPhone, request.getCustomerPhone());
        }

        // 是否创建工单
        if (Objects.nonNull(request.getHasWorkOrder())) {
            if (request.getHasWorkOrder()) {
                // 已创建工单：work_order_no不为空
                queryWrapper.isNotNull(WorkOrderMiddlewarePO::getOrderNo);
            } else {
                // 未创建工单：work_order_no为空
                queryWrapper.isNull(WorkOrderMiddlewarePO::getOrderNo);
            }
        }

        // 来电时间范围
        if (Objects.nonNull(request.getCallTimeStart())) {
            queryWrapper.ge(WorkOrderMiddlewarePO::getCallTime, request.getCallTimeStart());
        }
        if (Objects.nonNull(request.getCallTimeEnd())) {
            queryWrapper.le(WorkOrderMiddlewarePO::getCallTime, request.getCallTimeEnd());
        }

        return queryWrapper;
    }

    /**
     * 转换PO为响应DTO
     */
    private WorkOrderListQueryResponse convertToResponse(WorkOrderMiddlewarePO po) {
        WorkOrderListQueryResponse response = new WorkOrderListQueryResponse();

        response.setId(po.getId());
        response.setFunderCode(po.getFunderCode());
        response.setCustomerPhone(po.getCustomerPhone());
        response.setCallId(po.getCallId());
        response.setCallTime(po.getCallTime());
        response.setHasWorkOrder(StringUtils.isNotBlank(po.getOrderNo()));
        response.setWorkOrderNo(po.getOrderNo());
        response.setStatus(po.getStatus());
        response.setStatusDesc(getStatusDescription(po.getStatus()));
        response.setEventType(po.getEventType());
        response.setAgentId(po.getAgentId());
        response.setConnectTime(po.getConnectTime());
        response.setFailReason(po.getFailReason());
        response.setCreateTime(po.getCreateTime());

        return response;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String statusCode) {
        if (StringUtils.isBlank(statusCode)) {
            return "未知";
        }
        
        try {
            WorkOrderStatus status = WorkOrderStatus.fromCode(statusCode);
            return status.getDescription();
        } catch (Exception e) {
            return statusCode;
        }
    }
}
