package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryRemarkResp;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.model.resp.EagleEyeDataResp;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface CommunicateSummaryService {

    PageResultResponse<CommunicateSummaryResp> list(CommunicateSummaryReq communicateSummaryReq);

    String getUserNo(GetUserNoReq getUserNoReq);

    CommunicateSummaryDetailRsp detail(CommunicateSummaryDetailReq detailReq);

    Long create(CommunicateSummaryCreateReq communicateSummaryCreateReq);

    PageResultResponse<CommunicateSummaryRemarkResp> remarkList(CommunicateSummaryRemarkReq communicateSummaryRemarkReq);

    Boolean createRemark(CommunicateRemarkCreateReq communicateRemarkCreateReq);

    String getIss(Long id);

    EagleEyeDataResp getEagleEyeData(EagleEyeDataReq eagleEyeDataReq);
}
