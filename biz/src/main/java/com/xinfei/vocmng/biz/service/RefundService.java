/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.repaytrade.facade.rr.message.RefundNotifyMsg;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.LendQueryClientService;
import com.xinfei.vocmng.itl.rr.dto.CreateTaskDto;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.IdNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ SseService, v 0.1 2024/2/6 19:10 wancheng.qu Exp $
 */
@Service
@Slf4j
public class RefundService {

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClient;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private LendQueryClientService lendQueryClientService;

    @Value("${refund.taskType}")
    private Integer refundType;

    @Value("${refund.subTaskType}")
    private Integer subRefundType;

    public void refundFailed(RefundNotifyMsg t) {
        log.info("RefundNotifyMsg={}", JsonUtil.toJson(t));
        CreateTaskDto createTaskDto = new CreateTaskDto();
        createTaskDto.setIsCustomize(true);
        createTaskDto.setCustomizeSceneId(refundType);
        createTaskDto.setCustomizeChannel(subRefundType);
        createTaskDto.setTaskTypeId(subRefundType);
        createTaskDto.setQuestionTypeId(0);
        createTaskDto.setOrderNumber(lendQueryClientService.getOrderNoByLoanNo(t.getLoanNo()).getLoanReqNo());
        createTaskDto.setRefundAmount(t.getRefundAmt());
        if (StringUtils.isNotEmpty(t.getOfflineRefundPayType())) {
            String entryMethod = "";
            if ("BANK_CARD".equals(t.getOfflineRefundPayType())) {
                entryMethod = "银行卡";
            } else if ("ALIPAY".equals(t.getOfflineRefundPayType())) {
                entryMethod = "支付宝";
            }
            createTaskDto.setEntryMethod(entryMethod);
        }

        createTaskDto.setAccountCredited(t.getOfflineRefundAccount());
        createTaskDto.setEmergencyStatus(3);
        createTaskDto.setComment(t.getFailedReason());

        try {
            if (StringUtils.isNotEmpty(t.getCustNo())) {
                IdNoDTO idNoDTO = cisFacadeClient.queryIdNoByCustNo(t.getCustNo());
                createTaskDto.setIdCard(idNoDTO.getIdNo());
            }
        } catch (Exception e) {
            log.warn("RefundService queryIdNoByCustNo failed");
        }
        workOrderFeignClient.createTask(createTaskDto);
    }

    public void offlineRefundFailed(RefundNotifyMsg t) {
        CreateTaskDto createTaskDto = new CreateTaskDto();
        createTaskDto.setTaskTypeId(20);
        createTaskDto.setQuestionTypeId(20);
        if (StringUtils.isNotEmpty(t.getOfflineRefundPayType())) {
            String payType = "";
            if ("bankcard".equals(t.getOfflineRefundPayType())) {
                payType = "银行卡";
            } else if ("zfb".equals(t.getOfflineRefundPayType())) {
                payType = "支付宝";
            }
            createTaskDto.setEntryMethod(payType);
        }
        if (StringUtils.isNotEmpty(t.getOfflineRefundAccount())) {
            createTaskDto.setAccountLastFour(t.getOfflineRefundAccount().substring(t.getOfflineRefundAccount().length() - 5, t.getOfflineRefundAccount().length() - 1));
        }
        createTaskDto.setEmergencyStatus(3);
        createTaskDto.setComment(t.getFailedReason());
        workOrderFeignClient.createTask(createTaskDto);
    }


}
