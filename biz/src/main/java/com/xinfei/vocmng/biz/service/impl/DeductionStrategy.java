/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version $ PenaltyReductionStrategy, v 0.1 2024/3/27 18:37 wancheng.qu Exp $
 */
@Service
@Slf4j
public class DeductionStrategy implements CostCalculationStrategy<BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal> {

    @Override
    public ControlRes<BigDecimal, BigDecimal> calculateAmount(ControlItemValue<BigDecimal, BigDecimal, BigDecimal> values, Object... params) {
        BigDecimal o = values.getO();
        BigDecimal t = values.getT();

        BigDecimal amount = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof BigDecimal) {
                if (i == 0) {
                    amount = ((BigDecimal) params[i]);
                }
            }
        }
        BigDecimal lower = amount.multiply(t).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upper = amount.multiply(o).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        ControlRes<BigDecimal, BigDecimal> cr = new ControlRes<>();
        cr.setLeft(lower);
        cr.setRight(upper);

        return cr;
    }
}