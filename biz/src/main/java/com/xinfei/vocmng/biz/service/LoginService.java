package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.req.LoginSmsReq;
import com.xinfei.vocmng.biz.model.req.SendSmsReq;
import com.xinfei.vocmng.biz.model.resp.DingTalkRedirectResp;
import com.xinfei.vocmng.biz.model.resp.LoginResp;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.dal.po.Employee;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public interface LoginService {


    Boolean send(SendSmsReq req);

    LoginResp loginBySms(LoginSmsReq loginSmsReq);

    DingTalkRedirectResp authorizeUrl();

    LoginResp loginByDingTalk(String authCode, String state);

    Boolean logout();

    UserInfo getUserInfo(String identify);

    //Integer getUserState(String identify);

    void addUserCache(List<String> identify);

    void updateUserCache(Employee e);

    String getUserIdentifyCache(String jwt);

    Boolean delRedisKey(String key);

    String syncUserData();

    void syncQualityStaff();

    void syncQualityOrg();
}
