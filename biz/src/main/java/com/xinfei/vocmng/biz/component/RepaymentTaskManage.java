/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.component;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.xinfei.vocmng.util.trace.TraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.HashSet;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 还款任务
 *
 * <AUTHOR>
 * @version $ RepaymentTaskManage, v 0.1 2024/3/28 14:10 wancheng.qu Exp $
 */
@Configuration
@EnableScheduling
@Slf4j
public abstract class RepaymentTaskManage implements SchedulingConfigurer {
    private String cron;
    private ScheduledThreadPoolExecutor executorService;
    private final String coreThreadKey = "repayment.core.thread.count";
    private final String maxThreadKey = "repayment.max.thread.count";

    protected abstract String getCron();

    protected abstract void processTask();

    public RepaymentTaskManage() {
        // 初始化线程池
        int coreThreadCount = getCoreThreadCountFromApollo();
        int maxThreadCount = getMaxThreadCountFromApollo();
        executorService = new ScheduledThreadPoolExecutor(
                coreThreadCount,
                new CustomizableThreadFactory("RepaymentTaskManage-"),
                new ThreadPoolExecutor.DiscardPolicy()
        );
        executorService.setMaximumPoolSize(maxThreadCount);
        monitorThreadPool();
    }

    /**
     * 增加traceId
     */
    protected void process(){
        TraceUtil.initTraceId();
        try {
            processTask();
        } finally {
            TraceUtil.clearTraceId();
        }
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(executorService);
        taskRegistrar.addTriggerTask(
                this::process,
                triggerContext -> {
                    if (StringUtils.isEmpty(cron)) {
                        cron = getCron();
                    }
                    return new CronTrigger(cron).nextExecutionTime(triggerContext);
                }
        );

        Config config = ConfigService.getAppConfig();
        config.addChangeListener(configChangeEvent -> {
            if (configChangeEvent.isChanged(coreThreadKey) || configChangeEvent.isChanged(maxThreadKey)) {
                updateThreadPoolConfig();
            }
        }, new HashSet<>(Arrays.asList(coreThreadKey, maxThreadKey)));
    }

    private int getCoreThreadCountFromApollo() {
        return Integer.parseInt(ConfigService.getAppConfig().getProperty(coreThreadKey, "3"));
    }

    private int getMaxThreadCountFromApollo() {
        return Integer.parseInt(ConfigService.getAppConfig().getProperty(maxThreadKey, "4"));
    }

    private void updateThreadPoolConfig() {
        if (executorService != null) {
            int coreThreadCount = getCoreThreadCountFromApollo();
            int maxThreadCount = getMaxThreadCountFromApollo();
            executorService.setCorePoolSize(coreThreadCount);
            executorService.setMaximumPoolSize(maxThreadCount);
            log.info("updateThreadPoolConfig success core:{}, max:{}", coreThreadCount, maxThreadCount);
        }
    }

    private void monitorThreadPool() {
        ScheduledExecutorService monitoringExecutor = Executors.newSingleThreadScheduledExecutor();
        monitoringExecutor.scheduleAtFixedRate(() -> {
            ThreadPoolExecutor pool = (ThreadPoolExecutor) executorService;
            int pz = pool.getQueue().size();
            log.info("RepaymentTaskManage monitor Active threads:{},Completed tasks:{},Queue size: {}, Total tasks: {},Pool size: {}", pool.getActiveCount(), pool.getCompletedTaskCount(), pz, pool.getTaskCount(), pool.getPoolSize());
            if (pz > 10) {
                log.warn("RepaymentTaskManage too many,Queue size: {}", pz);
            }
        }, 0, 2, TimeUnit.MINUTES);
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    private static class CustomizableThreadFactory implements ThreadFactory {
        private final String threadNamePrefix;

        CustomizableThreadFactory(String threadNamePrefix) {
            AtomicInteger threadNumber = new AtomicInteger(1);
            this.threadNamePrefix = threadNamePrefix + threadNumber.getAndIncrement();
        }

        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, threadNamePrefix);
        }
    }

}