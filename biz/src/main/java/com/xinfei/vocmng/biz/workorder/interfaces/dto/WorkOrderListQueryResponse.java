package com.xinfei.vocmng.biz.workorder.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单列表查询响应
 * 简化显示字段：转接资方、客户来电号码、是否创建工单、来电时间
 *
 * <AUTHOR>
 * @version $ WorkOrderListQueryResponse, v 0.1 2025/08/11 WorkOrderListQueryResponse Exp $
 */
@Data
@ApiModel("工单列表查询响应")
public class WorkOrderListQueryResponse {

    @ApiModelProperty("记录ID")
    private Long id;

    @ApiModelProperty("转接资方")
    private String funderCode;

    @ApiModelProperty("客户来电号码")
    private String customerPhone;

    @ApiModelProperty("海尔消金通话ID")
    private String callId;

    @ApiModelProperty("来电时间")
    private LocalDateTime callTime;

    @ApiModelProperty("是否创建工单")
    private Boolean hasWorkOrder;

    @ApiModelProperty("工单编号（如果已创建）")
    private String workOrderNo;

    @ApiModelProperty("工单状态")
    private String status;

    @ApiModelProperty("工单状态描述")
    private String statusDesc;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("客服ID")
    private String agentId;

    @ApiModelProperty("接线时间")
    private LocalDateTime connectTime;

    @ApiModelProperty("失败原因")
    private String failReason;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
