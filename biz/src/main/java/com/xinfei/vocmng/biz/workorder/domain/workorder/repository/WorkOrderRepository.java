package com.xinfei.vocmng.biz.workorder.domain.workorder.repository;

import com.xinfei.vocmng.biz.workorder.domain.workorder.aggregate.WorkOrderAggregate;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;

import java.util.List;
import java.util.Optional;

/**
 * 工单仓储接口
 * 定义工单聚合的持久化操作
 *
 * <AUTHOR>
 * @version $ WorkOrderRepository, v 0.1 2025/07/30 WorkOrderRepository Exp $
 */
public interface WorkOrderRepository {

    /**
     * 保存工单聚合
     */
    WorkOrderAggregate save(WorkOrderAggregate aggregate);

    /**
     * 根据工单编号查找工单聚合（仅用于状态更新）
     */
    Optional<WorkOrderAggregate> findByOrderNo(String orderNo);

    /**
     * 更新工单聚合
     */
    WorkOrderAggregate update(WorkOrderAggregate aggregate);

    /**
     * 检查工单编号是否存在
     */
    boolean existsByOrderNo(String orderNo);

    /**
     * 根据海尔消金通话ID查找工单聚合
     */
    Optional<WorkOrderAggregate> findByCallId(String callId);

    /**
     * 根据Udesk通话ID查找工单聚合
     */
    Optional<WorkOrderAggregate> findByUdeskCallId(String udeskCallId);

    /**
     * 根据小结ID查找工单聚合
     */
    Optional<WorkOrderAggregate> findBySummaryId(Long summaryId);

    /**
     * 检查海尔消金通话ID是否存在
     */
    boolean existsByCallId(String callId);

    /**
     * 检查Udesk通话ID是否存在
     */
    boolean existsByUdeskCallId(String udeskCallId);
}
