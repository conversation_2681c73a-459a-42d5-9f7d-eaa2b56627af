package com.xinfei.vocmng.biz.workorder.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.workorder.application.service.WorkOrderEventProcessService;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;
import com.xinfei.vocmng.itl.constants.MQConstants;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 工单事件消费者
 * 负责消费工单创建和状态变更事件，通知资方系统
 *
 * <AUTHOR>
 * @version $ WorkOrderStatusChangeConsumer, v 0.1 2025/07/30 WorkOrderStatusChangeConsumer Exp $
 */
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = MQConstants.WORK_ORDER.STATUS_TOPIC,
        consumerGroup = MQConstants.WORK_ORDER.STATUS_CONSUMER_GROUP,
        selectorExpression = MQConstants.WORK_ORDER.STATUS_CHANGE_TAG
)
public class WorkOrderStatusChangeConsumer extends AbstractMessageListener<WorkOrderEvent> {

    private final WorkOrderEventProcessService workOrderEventProcessService;

    @Override
    protected void execute(WorkOrderEvent event) {
        log.info("处理工单事件, eventType: {}, orderNo: {}, funderCode: {}",
                event.getEventType(), event.getOrderNo(), event.getFunderCode());

        // 调用Service层处理工单事件
        workOrderEventProcessService.processWorkOrderEvent(event);

        log.info("工单事件处理完成, eventType: {}, orderNo: {}",
                event.getEventType(), event.getOrderNo());
    }
}
