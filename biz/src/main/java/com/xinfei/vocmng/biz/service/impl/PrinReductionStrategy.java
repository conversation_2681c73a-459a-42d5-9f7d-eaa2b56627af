/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.service.CostCalculationStrategy;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ PenaltyReductionStrategy, v 0.1 2024/3/27 18:37 wancheng.qu Exp $
 */
@Service
@Slf4j
public class PrinReductionStrategy implements CostCalculationStrategy<BigDecimal, BigDecimal, BigDecimal, BigDecimal, BigDecimal> {

    @Override
    public ControlRes<BigDecimal, BigDecimal> calculateAmount(ControlItemValue<BigDecimal, BigDecimal, BigDecimal> values, Object... params) {
        BigDecimal o = values.getO();
        BigDecimal t = values.getT();

        BigDecimal init = BigDecimal.ZERO;
        BigDecimal exempt = BigDecimal.ZERO;
        BigDecimal calculation = BigDecimal.ZERO;

        for (int i = 0; i < params.length; i++) {
            if (params[i] instanceof PlanDetailDto) {
                if (i == 0) {
                    init = ((PlanDetailDto) params[i]).getPrinAmt();
                }

                if (i == 1) {
                    exempt = ((PlanDetailDto) params[i]).getPrinAmt();
                }
            }
            if (params[i] instanceof CalculateFee) {
                if (i == 2) {
                    calculation = ((CalculateFee) params[i]).getCalPrinAmt();
                }
            }
        }
        log.info("calculateAmount process本金:初始金额:" + init + "历史减免:" + exempt + "试算值:" + calculation + "上限百分比：" + o + "下限百分比：" + t);
        return ReductionStrategyUtil.calculateAmount(t, o, init, exempt, calculation);
    }
}