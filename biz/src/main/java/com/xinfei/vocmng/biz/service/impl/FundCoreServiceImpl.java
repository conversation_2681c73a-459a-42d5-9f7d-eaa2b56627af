/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.fundcore.facade.api.request.IsCanLogoutQuotaRequest;
import com.xinfei.vocmng.biz.rr.request.LogoutQuotaRequest;
import com.xinfei.vocmng.biz.service.FundCoreService;
import com.xinfei.vocmng.itl.client.feign.FundCoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/8/23 10:55
 *
 */
@Service
@Slf4j
public class FundCoreServiceImpl implements FundCoreService {

    @Resource
    private FundCoreClient fundCoreClient;

    @Override
    public Boolean logoutQuotaCheck(String orderNumber) {
        IsCanLogoutQuotaRequest isCanLogoutQuotaRequest = new IsCanLogoutQuotaRequest();
        isCanLogoutQuotaRequest.setOrderNumber(orderNumber);
        return fundCoreClient.logoutQuotaCheck(isCanLogoutQuotaRequest);
    }

    @Override
    public Boolean logoutQuota(LogoutQuotaRequest request) {
        com.xinfei.fundcore.facade.api.request.LogoutQuotaRequest isCanLogoutQuotaRequest = new com.xinfei.fundcore.facade.api.request.LogoutQuotaRequest();
        isCanLogoutQuotaRequest.setOrderNumber(request.getOrderNo());
        return fundCoreClient.logoutQuota(isCanLogoutQuotaRequest);
    }
}