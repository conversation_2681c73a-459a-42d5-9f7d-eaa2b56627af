package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.rr.request.QueryContractListRequest;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;

import java.util.List;

/**
 * 合同服务
 *
 * <AUTHOR>
 * @version $ ContractService, v 0.1 2023/12/23 16:12 qu.lu Exp $
 */
public interface ContractService {
    /**
     * 根据营收订单号查询合同列表信息
     *
     * @param request
     * @return
     */
    List<ContractDetailDto> queryContractDetailList(QueryContractListRequest request);
}
