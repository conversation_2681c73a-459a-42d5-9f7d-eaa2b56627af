package com.xinfei.vocmng.biz.component.remote.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.xinfei.vocmng.itl.model.header.BaseHeader;
import feign.RequestTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/19
 */
public abstract class AbstractHeaderProcessRule extends AbstractProcessRule{

    /**
     * 请求Header通用处理类，子类负责请求头构建
     * @param template
     */
    protected  void headerProcess(RequestTemplate template){
        BaseHeader t = buildHeader(template);
        Map<String,String> headerMap = toMap(t);
        if(CollectionUtil.isEmpty(headerMap)){
            return;
        }

        for (Map.Entry<String,String> entry : headerMap.entrySet()){
            template.header(entry.getKey(),entry.getValue());
        }
    }

    /**
     * 请求头构建的抽象类，由继承的子类负责实现
     * @return
     */
    protected abstract BaseHeader buildHeader(RequestTemplate template);
}
