/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.FeeStrategyService;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundStrategyInput;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ FeeStrategyServiceImpl, v 0.1 2024-12-04 16:16 junjie.yan Exp $
 */
@Slf4j
@Component
public class OrderRefundStrategyServiceImpl extends FeeStrategyService<OrderRefundStrategyInput> {

    @Resource
    private UserLabelService userLabelService;

    @Override
    public Map<String, Object> getValue(Map<String, String> dictKeys, OrderRefundStrategyInput strategyInput) {

        if (strategyInput == null || StringUtils.isBlank(strategyInput.getLoanNo())) {
            throw new IllegalArgumentException("OrderStrategy: loanNo 不能为空");
        }
        String loanNo = strategyInput.getLoanNo();

        ManageOrderDetailDTO orderDetailDTO = getOrderInfoByLoanNo(loanNo);
        Long userNo = orderDetailDTO.getUserNo();
        LoanPlanResponse loanPlanResponse = getLoanInfo(loanNo);

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String> dictKey : dictKeys.entrySet()) {
            Object value = null;
            switch (dictKey.getKey()) {
                case "Principal"://借款本金金额
                    value = orderDetailDTO.getLoanAmt();
                    break;
                case "Order_Type"://订单类型
                    value = orderDetailDTO.getOrderType();
                    break;
                case "First_Loan"://是否首贷
                    value = isReLoan(userNo);
                    break;
                case "Period"://期数
                    value = orderDetailDTO.getTerm();
                    break;
                case "Investors"://资方
                    value = StringUtils.isEmpty(orderDetailDTO.getFundSource()) ? "" : orderDetailDTO.getFundSource();
                    break;
                case "app":
                    value = orderDetailDTO.getApp();
                    break;
                case "inner_app":
                    value = orderDetailDTO.getInnerApp();
                    break;
                case "Overdue"://是否逾期
                    value = "OD".equals(loanPlanResponse.getStatus());
                    break;
                case "Bad_Debt"://是否坏账
                    value = loanPlanResponse.getOverdueDays() > 180;
                    break;
                case "Repaired_Guarantee_Fee"://已还担保费
                    value = loanPlanResponse.getLoanFeeDetail().getActRepayPlan().getFee1Amt().add(loanPlanResponse.getLoanFeeDetail().getActRepayPlan().getFee2Amt());
                    break;
                case "Customer_Service_Group"://客服角色
                    value = UserContextHolder.getUserContext().getRole();
                    break;
                case "CS_Label"://用户标签
                    if (userNo != null) {
                        List<LabelDto> labelDtos = userLabelService.getUserLabel(String.valueOf(userNo));
                        value = labelDtos.stream()
                                .map(dto -> "[" + dto.getName() + "]")
                                .collect(Collectors.joining());
                    }
                    break;
                default:
                    break;
            }
            if (value != null) {
                map.put(dictKey.getKey(), value);
            }
        }

        return map;
    }

    @Override
    public String customizeKeyValues(Map<String, Object> dictKeys, OrderRefundStrategyInput strategyInput) {
        return strategyInput.getLoanNo();
    }
}