package com.xinfei.vocmng.biz.interceptor;

import apollo.com.google.common.collect.Lists;
import com.xinfei.vocmng.util.interceptor.GateInterceptor;
import com.xinfei.vocmng.util.interceptor.MonitorInterceptor;
import com.xinfei.vocmng.util.interceptor.TraceInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ WebConfig, v 0.1 2025/3/19 16:53 shaohui.chen Exp $
 */
@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private TraceInterceptor traceInterceptor;

    @Resource
    private GateInterceptor gateInterceptor;

    @Resource
    private MonitorInterceptor monitorInterceptor;


    /**
     * 健康检查、swagger、error页面排除
     */
    public static List<String> COMMON_EXCLUDE_PATH_PATTERNS =
            Lists.newArrayList(
                    "/v2/api-docs/**",
                    "/static/**",
                    "/webjars/**",
                    "/favicon.ico",
                    "/doc.html",
                    "/actuator/**",
                    "/swagger-resources/**",
                    "/swagger-ui/**",
                    "/error");

    /**
     * 登陆态排除接口
     */
//    public static List<String> AUTH_EXCLUDE_PATH_PATTERNS =
//            Lists.newArrayList(
//                    "/v2/api-docs/**",
//                    "/static/**",
//                    "/webjars/**",
//                    "/favicon.ico",
//                    "/doc.html",
//                    "/actuator/**",
//                    "/swagger-resources/**",
//                    "/swagger-ui/**",
//                    "/error",
//                    "/api/c/**");


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInterceptor).excludePathPatterns(COMMON_EXCLUDE_PATH_PATTERNS);
        registry.addInterceptor(gateInterceptor).excludePathPatterns(COMMON_EXCLUDE_PATH_PATTERNS);
        registry.addInterceptor(monitorInterceptor).excludePathPatterns(COMMON_EXCLUDE_PATH_PATTERNS);
    }


//    @Bean
//    public FilterRegistrationBean corsFilter() {
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        CorsConfiguration config = new CorsConfiguration();
//        config.setAllowCredentials(true);
//        log.info("corsConfigList : '{}'", allowedOrigin);
//        for (String str : allowedOrigin.split(Separator.SPLIT_COMMA)) {
//            config.addAllowedOrigin(str);
//        }
//        config.addAllowedHeader(allowedHeader);
//        config.addAllowedMethod(allowedMethod);
//        source.registerCorsConfiguration(allowedPath, config);
//        FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
//        bean.setOrder(0);
//        return bean;
//    }


}
