/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.FeeStrategyConfigMappingService;
import com.xinfei.vocmng.dal.mapper.FeeStrategyConfigMappingMapper;
import com.xinfei.vocmng.dal.po.FeeStrategyConfigMapping;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ FeeStrategyConfigMappingServiceImpl, v 0.1 2024-12-02 15:12 junjie.yan Exp $
 */
@Service
public class FeeStrategyConfigMappingServiceImpl extends BaseService<FeeStrategyConfigMappingMapper, FeeStrategyConfigMapping> implements FeeStrategyConfigMappingService {

    @Override
    public void saveBatchs(List<FeeStrategyConfigMapping> list) {
        saveBatch(list);
    }
}