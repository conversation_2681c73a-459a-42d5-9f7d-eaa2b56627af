/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.resp.AmountAdjustmentRecordResp;
import io.kyoto.pillar.ams.rest.dto.falcon.resp.AccountAmountLogResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ AccountAmountService, v 0.1 2024/8/12 18:08 you.zhang Exp $
 */
public interface AccountAmountService {

    List<AmountAdjustmentRecordResp> queryAdjustmentRecords(String app, String customerNo, String adjustType);
}
