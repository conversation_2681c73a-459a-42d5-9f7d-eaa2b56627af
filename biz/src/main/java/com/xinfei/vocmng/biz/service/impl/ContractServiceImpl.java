package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.rr.request.QueryContractListRequest;
import com.xinfei.vocmng.biz.service.ContractService;
import com.xinfei.vocmng.itl.ContractFeignClient;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ContractServiceImpl, v 0.1 2023/12/23 16:13 qu.lu Exp $
 */
@Slf4j
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractFeignClient contractFeignClient;

    @Override
    public List<ContractDetailDto> queryContractDetailList(QueryContractListRequest request) {
        return null;
    }
}
