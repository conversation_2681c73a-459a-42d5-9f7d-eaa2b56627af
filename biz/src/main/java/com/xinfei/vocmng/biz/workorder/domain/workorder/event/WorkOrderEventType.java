package com.xinfei.vocmng.biz.workorder.domain.workorder.event;

import lombok.Getter;

/**
 * 工单事件类型枚举
 *
 * <AUTHOR>
 * @version $ WorkOrderEventType, v 0.1 2025/07/30 WorkOrderEventType Exp $
 */
@Getter
public enum WorkOrderEventType {

    /**
     * 工单创建
     */
    CREATED("CREATED", "工单创建"),

    /**
     * 状态变更
     */
    STATUS_CHANGED("STATUS_CHANGED", "状态变更"),

    /**
     * IVR进线事件
     */
    IVR_INCOMING("IVR_INCOMING", "IVR进线事件"),

    /**
     * 接线成功事件
     */
    CALL_CONNECTED("CALL_CONNECTED", "接线成功事件"),

    /**
     * 接线失败事件
     */
    CALL_FAILED("CALL_FAILED", "接线失败事件");

    private final String code;
    private final String description;

    WorkOrderEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static WorkOrderEventType fromCode(String code) {
        for (WorkOrderEventType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的事件类型: " + code);
    }
}
