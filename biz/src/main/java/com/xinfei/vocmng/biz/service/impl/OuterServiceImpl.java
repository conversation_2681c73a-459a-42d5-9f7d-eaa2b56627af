/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.ListEntryReq;
import com.xinfei.vocmng.biz.model.req.SmsArgsReq;
import com.xinfei.vocmng.biz.model.req.SmsReq;
import com.xinfei.vocmng.biz.model.req.WorkReq;
import com.xinfei.vocmng.biz.model.resp.WorkResp;
import com.xinfei.vocmng.biz.service.OuterService;
import com.xinfei.vocmng.biz.util.BizLog;
import com.xinfei.vocmng.biz.util.HttpClientUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
/**
 *
 * <AUTHOR>
 * @version $ OuterServiceImpl, v 0.1 2023/12/21 17:17 wancheng.qu Exp $
 */
@Slf4j
@Service
public class OuterServiceImpl implements OuterService {
    @Autowired
    protected VocConfig vocConfig;

    /**
     * 发送短信验证码
     */
    private static String SEND_SMS = "/sms/send";

    private static String SEND_LIST = "/user-list/create";

    private static String WORK_METHOD = "/outapi/task/check-question-ids-existence";



    @Resource
    private LoginUserConfig loginUserConfig;


    @Override
    public void sendSms(String mobile, String code) {
        String url = loginUserConfig.getSmsUrl() + SEND_SMS;

        SmsReq args = new SmsReq();
        args.setMobile(mobile);
        args.setApp(LoginUserConstants.APP);
        args.setTemplate_id(loginUserConfig.getSmsTemplateId());
        Map<String, String> data = new HashMap<>();
        data.put("#code", code);
        args.setData(data);

        SmsArgsReq req = new SmsArgsReq(args);
        req.setUa(LoginUserConstants.UA);
        req.setSign("mock-sign");
        doRequest(url, req);
    }

    @Override
    public String getOauthUserInfo(String authCode) {
        try {
            GetUserTokenResponseBody tokenBody = getAccessToken(authCode);
            if (ObjectUtil.isNull(tokenBody) || StringUtils.isEmpty(tokenBody.getAccessToken())) {
                throw new IgnoreException(TechplayErrDtlEnum.DINGTALK_TOKEN_ERROR);
            }
            GetUserResponseBody userInfo = contactUsers(tokenBody.getAccessToken(), "me");
            if (ObjectUtil.isNull(userInfo) || StringUtils.isEmpty(userInfo.getMobile())) {
                throw new IgnoreException(TechplayErrDtlEnum.DINGTALK_GETUSER_ERROR);
            }
            return userInfo.getMobile();
        } catch (Exception e) {
            log.error("getOauthUserInfo.Exception:" + e.getMessage());
        }
        return "";
    }

    /**查询工单*/
    @Override
    public Map<Long, Boolean> getWork(List<Long> ids) {
        String url = loginUserConfig.getWorkUrl() + WORK_METHOD;
        WorkReq w = new WorkReq();
        w.setIds(ids);
        return doRequestWork(url,w);
    }

    @Override
    public void sendList(List<ListEntryReq> req) {
        String url = vocConfig.getListCoreUrl() + SEND_LIST;
        req.forEach(r -> {
            sendListReq(url, r);
        });
    }

    private void sendListReq(String url, ListEntryReq r) {
        String req = JsonUtil.toJson(r);
        log.info("sendListReq req:{}", req);
        try {
            HttpClientUtil.postJson(url, null, req);
        } catch (Exception e) {
            log.warn(" send to list error", e);
        }
    }

    public GetUserTokenResponseBody getAccessToken(String authCode) throws Exception {
        Client client = getDingtalkOauthClient();
        GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                .setClientId(loginUserConfig.getClientId())
                .setClientSecret(loginUserConfig.getAppSecret())
                .setCode(authCode)
                .setGrantType("authorization_code");
        try {
            BizLog.addLog("getAccessToken.getUserTokenRequest", JSON.toJSONString(getUserTokenRequest));
            GetUserTokenResponse userToken = client.getUserToken(getUserTokenRequest);
            BizLog.addLog("getAccessToken.userToken", Dict.create().set("getUserTokenRequest", getUserTokenRequest).set("userToken", userToken));

            GetUserTokenResponseBody body = userToken.getBody();
            return body;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("getAccessToken.TeaException:" + err.getCode() + err.getMessage());
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("getAccessToken.Exception:" + err.getCode() + err.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取用户通讯录个人信息
     *
     * @param accessToken
     * @param unionId
     * @throws Exception
     */
    public GetUserResponseBody contactUsers(String accessToken, String unionId) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = getDingtalkContactClient();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;
        try {
            GetUserResponse userWithOptions = client.getUserWithOptions(unionId, getUserHeaders, new RuntimeOptions());
            return userWithOptions.getBody();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("contactUsers.TeaException:" + err.getCode() + err.getMessage());
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                log.error("contactUsers.Exception:" + err.getCode() + err.getMessage());
            }
        }
        return null;
    }

    public static com.aliyun.dingtalkcontact_1_0.Client getDingtalkContactClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }

    public static Client getDingtalkOauthClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    /**
     * 请求短信系统
     *
     * @param uri
     * @param req
     */
    private void doRequest(String uri, SmsArgsReq req) {
        try {
            Map<String, String> query = new HashMap<>();
            query.put("trace_id", UUID.randomUUID().toString());
            HttpClientUtil.postJson(uri, query, JsonUtil.toJson(req));
        } catch (Exception e) {
            log.error("Sms.doRequest.error", e);
        }
    }

    private Map<Long, Boolean>  doRequestWork(String uri, WorkReq req) {
        try {
            String res = HttpClientUtil.postJson(uri, null, JsonUtil.toJson(req));
            if(StringUtils.isNotBlank(res)){
                WorkResp w =JsonUtil.parseJson(res,WorkResp.class);
                if(Objects.nonNull(w) && MapUtil.isNotEmpty(w.getData())){
                    Map<String, Boolean> data = w.getData();
                    return convertKeysToLong(data);
                }
            }
        } catch (Exception e) {
            log.error("doRequestWork.error", e);
        }
        return new HashMap<>(1);
    }

    private  Map<Long, Boolean> convertKeysToLong(Map<String, Boolean> inputMap) {
        return inputMap.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(entry -> Long.parseLong(entry.getKey()), Map.Entry::getValue));
    }
}