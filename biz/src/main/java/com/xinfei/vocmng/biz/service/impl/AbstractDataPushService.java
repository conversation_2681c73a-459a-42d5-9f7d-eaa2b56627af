package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.service.CustomerService;
import com.xinfei.vocmng.biz.service.DataPushService;
import com.xinfei.vocmng.biz.service.OuterService;
import com.xinfei.vocmng.dal.mapper.*;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ AbstractDataPushService, v 0.1 2025/3/5 17:05 shaohui.chen Exp $
 */
public abstract class AbstractDataPushService<T,R> implements DataPushService<T> {

    @Resource
    protected VocConfig vocConfig;

    @Resource
    protected RiskUserMapper riskUserMapper;

    @Resource
    protected EasyComplaintUserMapper easyComplaintUserMapper;

    @Resource
    protected CommunicateSummaryServiceImpl communicateSummaryService;

    @Resource
    protected CommunicateSummaryMapper communicateSummaryMapper;

    @Resource
    protected CommunicateSummaryRemarkMapper communicateSummaryRemarkMapper;

    @Resource
    protected DepartmentMapper departmentMapper;

    @Resource
    protected OuterService outerService;

    @Resource
    protected UdeskClientService udeskClientService;

    @Resource
    protected CisFacadeClientService cisFacadeClientService;

    @Resource
    protected CisFacadeClient cisFacadeClient;

    @Resource
    protected CustomerService customerService;

    @Resource
    protected MqTemplate mqTemplate;

    @Resource
    protected EmployeeMapper employeeMapper;

    @Override
    public final void pushData(T data) {
        if (vocConfig.getPushUDeskQualityCheck()) {
            // 1. 将T类型的 data 组装成所需载荷
            R payload = assemblePayload(data);

            // 2. 发送载荷
            if (Objects.nonNull(payload)) {
                sendData(payload);
            }
        }
    }

    /**
     * 根据传入的 T 类型对象组装载荷，子类可自由决定载荷的结构
     * @param data 待推送的对象
     * @return payload 组装好的载荷
     */
    protected abstract R assemblePayload(T data);

    /**
     * 将组装好的载荷发送出去（HTTP、MQ、文件等）
     * 本系统MQ消息最大长度限制200Kb，注意
     * @param payload 待发送的载荷
     */
    protected abstract void sendData(R payload);

}

