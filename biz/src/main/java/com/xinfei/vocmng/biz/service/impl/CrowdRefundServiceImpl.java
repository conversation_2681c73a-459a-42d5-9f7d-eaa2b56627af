package com.xinfei.vocmng.biz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xinfei.supervip.common.enums.DeductTypeEnum;
import com.xinfei.supervip.common.enums.RefundChannelEnum;
import com.xinfei.supervip.common.enums.RefundTypeEnum;
import com.xinfei.supervip.common.enums.VipOrderStatusEnum;
import com.xinfei.supervip.common.enums.VipTypeEnum;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyExecuteResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyListAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.request.CreateVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.ExecuteVipRefundApplyAdminRequest;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.remote.MemberInterestRemoteService;
import com.xinfei.vocmng.biz.remote.WorkOrderRemoteService;
import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;
import com.xinfei.vocmng.biz.service.CrowdRefundService;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.itl.model.enums.MemberTypeEnum;
import com.xinfei.vocmng.itl.rr.AddFeedbackRequest;
import com.xinfei.vocmng.itl.rr.FinishTaskRequest;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.util.enums.ErrDtlEnum;
import com.xinfei.vocmng.util.exception.VocmngException;
import com.xinfei.vocmng.util.threadpool.ContextInheritableThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 人群退款服务实现
 *
 * <AUTHOR>
 * @version $ CrowdRefundServiceImpl, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Service
@Slf4j
public class CrowdRefundServiceImpl implements CrowdRefundService {

    @Autowired
    private VipFacadeClientImpl vipFacadeClient;

    @Autowired
    private WorkOrderRemoteService workOrderRemoteService;

    @Autowired
    private RedisUtils redisUtils;

    // 创建专用线程池处理退款任务
    private final ThreadPoolExecutor refundExecutor = new ContextInheritableThreadPoolExecutor(
            5,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new CustomizableThreadFactory("crowd-refund-"),
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
    );

    @Override
    public void processCrowdRefund(List<CrowdUserDataDto> userDataList) {
        if (CollectionUtils.isEmpty(userDataList)) {
            log.warn("用户数据列表为空，跳过退款处理");
            return;
        }

        log.info("开始处理人群退款，用户数量: {}", userDataList.size());
        // 使用CompletableFuture并行处理退款
        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(userDataList.stream()
                .map(userData -> CompletableFuture.runAsync(() -> {
                    processUserRefund(userData);
                }, refundExecutor)).toArray(CompletableFuture[]::new));

        try {
            allOf.get();
            log.info("人群退款处理完成，用户数量: {}", userDataList.size());
        } catch (Exception e) {
            throw new RuntimeException("处理失败重试");
        }

    }

    @Override
    public void processUserRefund(CrowdUserDataDto userData) {
        if (Objects.isNull(userData) || StringUtils.isBlank(userData.getUserNo())) {
            log.warn("用户数据无效，跳过退款处理: {}", userData);
            return;
        }
        log.info("开始处理用户退款，userData: {}", JSONObject.toJSONString(userData));
        // 根据退款类型执行相应操作
        switch (userData.getRefundType()) {
            case 1:
                // 场景1：已付费场景，执行退卡退款
                handlePaidUserRefund(userData);
                break;
            case 2:
                // 场景2：未付费场景，执行取消扣款
                handleUnpaidUserCancellation(userData);
                break;
            default:
                log.warn("未知的退款类型: {}, 用户ID: {}", userData.getRefundType(), userData.getUserNo());
        }
    }


    /**
     * 处理已付费用户退款（退卡退款）
     */
    private void handlePaidUserRefund(CrowdUserDataDto userData) {
        log.info("处理已付费用户退卡退款，用户ID: {}", userData.getUserNo());

        // 幂等检查：检查会员订单退款是否已经处理过
        String vipOrderKey = buildVipOrderRefundIdempotentKey(userData.getVipOrderNo());
        if (Objects.nonNull(userData.getVipOrderNo()) && isAlreadyProcessed(vipOrderKey)) {
            log.info("会员订单退款已经处理过，跳过退款处理，用户ID: {}, 订单号: {}",
                    userData.getUserNo(), userData.getVipOrderNo());
            // 即使订单已退款，仍需处理工单（可能工单还未结案）
            handleWorkOrderProcess(userData);
            return;
        }

        // 场景1：对于已付费场景，执行退款操作"退款"
        if (Objects.nonNull(userData.getVipOrderNo())) {
            // 调用飞跃会员退款接口
            executeVipRefund(userData.getVipOrderNo());
            // 标记订单退款已处理
            markAsProcessed(vipOrderKey, "已付费用户退款处理");
        }

        // 处理工单：先添加反馈，后强制结案
        handleWorkOrderProcess(userData);
        log.info("已付费用户退卡退款处理完成，用户ID: {}", userData.getUserNo());
    }

    /**
     * 处理未付费用户取消扣款
     */
    private void handleUnpaidUserCancellation(CrowdUserDataDto userData) {
        log.info("处理未付费用户取消扣款，用户ID: {}", userData.getUserNo());

        // 幂等检查：检查会员订单取消扣款是否已经处理过
        String vipOrderKey = buildVipOrderRefundIdempotentKey(userData.getVipOrderNo());
        if (Objects.nonNull(userData.getVipOrderNo()) && isAlreadyProcessed(vipOrderKey)) {
            log.info("会员订单取消扣款已经处理过，跳过取消扣款处理，用户ID: {}, 订单号: {}",
                    userData.getUserNo(), userData.getVipOrderNo());
            // 即使订单已取消，仍需处理工单（可能工单还未结案）
            handleWorkOrderProcess(userData);
            return;
        }

        // 场景2：对于未付费场景，执行取消扣款操作
        if (Objects.nonNull(userData.getVipOrderNo())) {
            // 调用飞跃会员取消扣款接口
            cancelVipDeduct(userData.getVipOrderNo());
            // 标记订单取消扣款已处理
            markAsProcessed(vipOrderKey, "未付费用户取消扣款处理");
        }

        // 处理工单：先添加反馈，后强制结案
        handleWorkOrderProcess(userData);
        log.info("未付费用户取消扣款处理完成，用户ID: {}", userData.getUserNo());
    }

    /**
     * 处理工单反馈和结案
     */
    private void handleWorkOrderProcess(CrowdUserDataDto userData) {
        if (CollectionUtils.isEmpty(userData.getWorkOrderNoList())) {
            log.warn("工单编号列表为空，跳过工单处理，用户ID: {}", userData.getUserNo());
            return;
        }

        log.info("开始处理工单列表，用户ID: {}, 工单数量: {}", userData.getUserNo(), userData.getWorkOrderNoList().size());

        // 处理每个工单
        for (Long taskId : userData.getWorkOrderNoList()) {
            if (Objects.isNull(taskId)) {
                log.warn("工单ID为空，跳过处理，用户ID: {}", userData.getUserNo());
                continue;
            }

            // 幂等检查：检查工单是否已经处理过
            String workOrderKey = buildWorkOrderIdempotentKey(taskId);
            if (isAlreadyProcessed(workOrderKey)) {
                log.info("工单已经处理过，跳过处理，工单ID: {}, 用户ID: {}", taskId, userData.getUserNo());
                continue;
            }
            // 1. 先添加反馈
            addWorkOrderFeedback(taskId, userData.getRefundType());
            // 2. 再强制结案
            finishWorkOrder(taskId);
            log.info("工单处理完成，工单ID: {}, 用户ID: {}", taskId, userData.getUserNo());
            // 标记工单处理过
            markAsProcessed(workOrderKey, "工单反馈和结案处理");
        }

        log.info("工单列表处理完成，用户ID: {}, 工单数量: {}", userData.getUserNo(), userData.getWorkOrderNoList().size());
    }

    /**
     * 添加工单反馈
     */
    private void addWorkOrderFeedback(Long taskId, Integer refundType) {
        AddFeedbackRequest request = new AddFeedbackRequest();
        request.setTaskId(taskId);
        request.setUserId(ApolloConstant.WORK_ORDER_USER_ID);
        // 根据退款类型设置不同的反馈内容
        String comment;
        if (Objects.nonNull(refundType) && refundType == 2) {
            // 取消扣款场景
            comment = "您所反馈的飞跃会员，已为您取消了后续的扣款，后续不再开通，则不会收费，如您还有其它疑问，可联系在线客服为您处理，谢谢！";
        } else {
            // 退卡退款场景（默认）
            comment = "您所反馈的飞跃会员问题，客服已为您登记退款，款项将原路退回，到账时间一般1-7个工作日，最快24小时内，若您未及时收到，可能是由于银行方面的原因，请您耐心等待，若7个工作日内还未到账，请您提供对应收款账号的流水凭证，再次上传给客服为您核实处理，谢谢";
        }

        request.setComment(comment);
        request.setCollectionStatus(1);
        request.setIsClientSync(1);
        workOrderRemoteService.addWorkOrderFeedback(request);
    }

    /**
     * 工单强制结案
     */
    private void finishWorkOrder(Long taskId) {
        FinishTaskRequest request = new FinishTaskRequest();
        request.setTaskId(taskId);
        request.setUserId(ApolloConstant.WORK_ORDER_USER_ID); // qa1环境固定传600，生产环境传970
        request.setTaskStatus(8); // 8已结案
        workOrderRemoteService.finishWorkOrder(request);
    }

    /**
     * 检查是否已经处理过（基于Redis缓存的幂等检查）
     */
    private boolean isAlreadyProcessed(String key) {
        try {
            String value = redisUtils.get(key);
            if (StringUtils.isNotBlank(value)) {
                log.info("幂等检查：已处理过，key: {}, value: {}", key, value);
                return true;
            }
        } catch (Exception e) {
            log.warn("Redis幂等检查失败，key: {}, 继续执行处理逻辑", key, e);
        }
        return false;
    }

    /**
     * 标记为已处理（设置Redis缓存）
     */
    private void markAsProcessed(String key, String description) {
        try {
            // 设置缓存，过期时间7天（避免重复处理，同时不会永久占用内存）
            long expireTime = 7 * 24 * 60 * 60; // 7天，单位：秒
            String value = description + ":" + System.currentTimeMillis();
            redisUtils.set(key, value, expireTime);
            log.info("标记为已处理，key: {}, value: {}", key, value);
        } catch (Exception e) {
            log.error("设置Redis幂等标记失败，key: {}, description: {}", key, description, e);
        }
    }

    /**
     * 构建工单幂等缓存key
     */
    private String buildWorkOrderIdempotentKey(Long taskId) {
        return RedisKeyConstants.WORK_ORDER_IDEMPOTENT_PREFIX + taskId;
    }

    /**
     * 构建会员订单退款幂等缓存key
     */
    private String buildVipOrderRefundIdempotentKey(Long vipOrderNo) {
        return RedisKeyConstants.VIP_ORDER_REFUND_IDEMPOTENT_PREFIX + vipOrderNo;
    }

    /**
     * 执行飞跃会员退款
     */
    private void executeVipRefund(Long vipOrderNo) {
        log.info("开始执行飞跃会员退款，订单号: {}", vipOrderNo);
        // 查询可退金额
        RefundApplyListAdminDTO refundApplyListAdminDTO = vipFacadeClient.queryVipRefundApplyList(vipOrderNo);
        CreateVipRefundApplyAdminRequest createRequest = new CreateVipRefundApplyAdminRequest();
        createRequest.setVipOrderId(vipOrderNo);
        createRequest.setHoldHour(0);
        createRequest.setVipType(MemberTypeEnum.FEI_YUE.getCode());
        createRequest.setRefundType(RefundTypeEnum.REFUND_CARD.getCode());
        createRequest.setRefundChannel(RefundChannelEnum.ORIGINAL_REFUND.getCode());
        createRequest.setRefundAmount(refundApplyListAdminDTO.getRemainRefundAmount());
        createRequest.setOperator("system"); // 操作人填system
        createRequest.setRefundReason("人群退款批量处理");
        vipFacadeClient.createVipRefundApply(createRequest);
    }

    /**
     * 取消飞跃会员扣款
     */
    private void cancelVipDeduct(Long vipOrderNo) {
        log.info("开始取消飞跃会员扣款，订单号: {}", vipOrderNo);
        Boolean result = vipFacadeClient.cancelVipDeduct(vipOrderNo, "system");
        if (Boolean.TRUE.equals(result)) {
            log.info("飞跃会员取消扣款成功，订单号: {}", vipOrderNo);
        } else {
            log.error("飞跃会员取消扣款失败，订单号: {}, 返回结果: {}", vipOrderNo, result);
            throw new RuntimeException("取消扣款失败，返回结果: " + result);
        }
    }

}
