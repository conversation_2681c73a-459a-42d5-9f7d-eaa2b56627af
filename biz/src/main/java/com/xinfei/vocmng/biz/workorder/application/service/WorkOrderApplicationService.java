package com.xinfei.vocmng.biz.workorder.application.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.util.enums.ErrDtlEnum;
import com.xinfei.vocmng.util.exception.VocmngException;
import com.xinfei.vocmng.util.util.IDGeneratorUtil;
import org.apache.commons.lang3.StringUtils;
import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import com.xinfei.vocmng.biz.remote.HaierComplaintRemoteService;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.biz.workorder.application.command.CallConnectedCommand;
import com.xinfei.vocmng.biz.workorder.application.command.CallFailedCommand;
import com.xinfei.vocmng.biz.workorder.application.command.IvrIncomingCommand;
import com.xinfei.vocmng.biz.workorder.application.command.UpdateWorkOrderStatusCommand;
import com.xinfei.vocmng.biz.workorder.domain.workorder.aggregate.WorkOrderAggregate;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderDataEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;
import com.xinfei.vocmng.biz.workorder.domain.workorder.repository.WorkOrderRepository;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.CustomerInfo;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.BusinessData;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExtendData;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.workorder.infrastructure.mq.WorkOrderEventPublisher;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.itl.rr.haier.QueryTransferCallResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 工单应用服务
 * 编排工单创建、状态同步、事件处理等业务流程
 *
 * <AUTHOR>
 * @version $ WorkOrderApplicationService, v 0.1 2025/08/11 WorkOrderApplicationService Exp $
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderApplicationService {

    private final WorkOrderRepository workOrderRepository;
    private final WorkOrderEventPublisher eventPublisher;
    private final CommunicateSummaryMapper communicateSummaryMapper;
    private final HaierComplaintRemoteService haierComplaintRemoteService;
    private final RedisUtils redisUtils;

    /**
     * 更新工单状态
     */
    public void updateWorkOrderStatus(UpdateWorkOrderStatusCommand command) {
        log.info("开始更新工单状态, callId: {}, orderNo: {}, status: {}",
                command.getCallId(), command.getOrderNo(), command.getNewStatus());
        // 1. 查找工单聚合
        Optional<WorkOrderAggregate> aggregateOpt;

        if (StringUtils.isNotBlank(command.getCallId())) {
            // 通过callId查找工单
            aggregateOpt = workOrderRepository.findByCallId(command.getCallId());
            if (!aggregateOpt.isPresent()) {
                throw new VocmngException(ErrDtlEnum.WORK_ORDER_CANNOT_FIND);
            }
            log.info("通过callId找到工单, callId: {}", command.getCallId());
        } else if (StringUtils.isNotBlank(command.getOrderNo())) {
            // 通过orderNo查找工单（兼容原有逻辑）
            aggregateOpt = workOrderRepository.findByOrderNo(command.getOrderNo());
            if (!aggregateOpt.isPresent()) {
                throw new VocmngException(ErrDtlEnum.WORK_ORDER_CANNOT_FIND);
            }
            log.info("通过orderNo找到工单, orderNo: {}", command.getOrderNo());
        } else {
            throw new IllegalArgumentException("callId和orderNo不能同时为空");
        }

        WorkOrderAggregate aggregate = aggregateOpt.get();

        // 2. 回传workOrderNo，更新记录
        if (StringUtils.isNotBlank(command.getOrderNo()) &&
                StringUtils.isBlank(aggregate.getWorkOrder().getOrderNo())) {
            log.info("更新工单编号, callId: {}, orderNo: {}",
                    command.getCallId(), command.getOrderNo());
            aggregate.getWorkOrder().setOrderNo(command.getOrderNo());
        }

        // 3. 更新状态
        aggregate.updateStatus(
                command.getNewStatus(),
                command.getReason(),
                command.getExternalWorkOrderStatus(),
                command.getProcessTime(),
                command.getCustomerStatus(),
                command.getEventType()
        );

        // 4. 保存更新
        WorkOrderAggregate updatedAggregate = workOrderRepository.update(aggregate);

        // 5. 发布领域事件
        publishDomainEvents(updatedAggregate);

        log.info("工单状态更新成功, callId: {}, orderNo: {}, status: {}",
                command.getCallId(), updatedAggregate.getWorkOrder().getOrderNo(), command.getNewStatus());
    }


    /**
     * 发布领域事件
     */
    private void publishDomainEvents(WorkOrderAggregate aggregate) {
        List<WorkOrderEvent> events = aggregate.getAndClearDomainEvents();
        if (CollectionUtils.isEmpty(events)) {
            return;
        }

        for (WorkOrderEvent event : events) {
            try {
                eventPublisher.publishEvent(event);
                log.debug("发布领域事件成功: {}", event.getEventType());
            } catch (Exception e) {
                log.error("发布领域事件失败: {}", event.getEventType(), e);
                // 这里可以考虑重试或者记录失败事件
            }
        }
    }

    /**
     * 处理IVR进线事件
     */
    public void handleIvrIncoming(IvrIncomingCommand command) {
        log.info("开始处理IVR进线事件, callId: {}, udeskCallId: {}, funderCode: {}, transferTime: {}",
                command.getCallId(), command.getUdeskCallId(), command.getFunderCode(), command.getTransferTime());

        // 检查是否已存在相同的工单
        Optional<WorkOrderAggregate> existingAggregate = workOrderRepository.findByCallId(command.getCallId());
        if (existingAggregate.isPresent()) {
            log.warn("工单已存在, callId: {}", command.getCallId());
            return;
        }

        // 查询海尔消金基础信息并缓存
        QueryTransferCallResponse baseInfo = queryAndCacheHaierBaseInfo(command);

        // 创建新的工单聚合
        WorkOrderEntity workOrder = WorkOrderEntity.builder()
                .funderCode(command.getFunderCode())
                .callId(command.getCallId())
                .udeskCallId(command.getUdeskCallId())
                .customerPhone(baseInfo.getTelPhone())
                .eventType(WorkOrderEventType.IVR_INCOMING.getCode())
                .status(WorkOrderStatus.PENDING)
                .version(0)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 设置转接时间
        if (StringUtils.isNotBlank(command.getTransferTime())) {
            workOrder.setTransferTime(LocalDateTimeUtils.parseLocalDateTimeByDateStr(command.getTransferTime()));
        }

        // 填充来电时间
        fillCallTime(workOrder, baseInfo);

        WorkOrderAggregate aggregate = new WorkOrderAggregate();
        aggregate.setWorkOrder(workOrder);

        // 根据海尔消金基础信息创建工单数据实体
        if (Objects.nonNull(baseInfo)) {
            WorkOrderDataEntity workOrderData = createWorkOrderDataFromBaseInfo(baseInfo, command);
            aggregate.setWorkOrderData(workOrderData);
        }

        // 处理IVR进线事件
        aggregate.handleIvrIncoming(command.getCallId(), command.getUdeskCallId(),
                command.getCustomerPhone(), command.getFunderCode());
        // 保存聚合
        workOrderRepository.save(aggregate);

        log.info("IVR进线事件处理成功, callId: {}, orderNo: {}, baseInfo: {}",
                command.getCallId(), workOrder.getOrderNo(), baseInfo);
    }

    /**
     * 处理接线成功事件
     */
    public void handleCallConnected(CallConnectedCommand command) {
        log.info("开始处理接线成功事件, callId: {}, udeskCallId: {}, agentId: {}, connectTime: {}",
                command.getCallId(), command.getUdeskCallId(), command.getAgentId(), command.getConnectTime());

        // 查找工单聚合
        Optional<WorkOrderAggregate> aggregateOpt = workOrderRepository.findByCallId(command.getCallId());
        if (!aggregateOpt.isPresent()) {
            log.error("未找到对应的工单, callId: {}", command.getCallId());
            throw new VocmngException(ErrDtlEnum.WORK_ORDER_CANNOT_FIND);
        }

        WorkOrderAggregate aggregate = aggregateOpt.get();

        // 查询创建的小结ID（通过callId查询最新的小结记录）
        Long summaryId = findSummaryIdByCallId(command.getUdeskCallId());

        // 处理接线成功事件（传入创建的小结ID和接线时间）
        aggregate.handleCallConnected(command.getAgentId(), summaryId, command.getConnectTime());

        // 更新聚合
        workOrderRepository.update(aggregate);

        log.info("接线成功事件处理成功, callId: {}, udeskCallId: {}, agentId: {}, summaryId: {}",
                command.getCallId(), command.getUdeskCallId(), command.getAgentId(), summaryId);
    }

    /**
     * 处理接线失败事件
     */
    public void handleCallFailed(CallFailedCommand command) {
        log.info("开始处理接线失败事件, callId: {}, failReason: {}",
                command.getCallId(), command.getFailReason());

        // 查找工单聚合
        Optional<WorkOrderAggregate> aggregateOpt = workOrderRepository.findByCallId(command.getCallId());
        if (!aggregateOpt.isPresent()) {
            log.error("未找到对应的工单, callId: {}", command.getCallId());
            throw new VocmngException(ErrDtlEnum.WORK_ORDER_CANNOT_FIND);
        }
        WorkOrderAggregate aggregate = aggregateOpt.get();
        // 处理接线失败事件
        aggregate.handleCallFailed(command.getFailReason());
        // 更新聚合
        workOrderRepository.update(aggregate);
        log.info("接线失败事件处理成功, callId: {}, failReason: {}",
                command.getCallId(), command.getFailReason());
    }


    /**
     * 查询海尔消金基础信息并缓存
     */
    private QueryTransferCallResponse queryAndCacheHaierBaseInfo(IvrIncomingCommand command) {
        log.info("开始查询海尔消金基础信息, callId: {}, customerPhone: {}",
                command.getCallId(), command.getCustomerPhone());

        // 查询海尔消金基础信息
        QueryTransferCallResponse baseInfo = haierComplaintRemoteService.queryTransferCallInfo(
                command.getCallId(), ApolloConstant.HAIER_COMPLAINT_COOPER_ORG_NO);

        if (Objects.nonNull(baseInfo)) {
            // 缓存基础信息
            String cacheKey = RedisKeyConstants.buildHaierBaseInfoKey(command.getCallId());
            redisUtils.set(cacheKey, JSON.toJSONString(baseInfo), RedisKeyConstants.HAIER_BASE_INFO_EXPIRE_SECONDS);

            log.info("海尔消金基础信息查询成功并已缓存, callId: {}, telPhone: {}, complaintStatus: {}",
                    command.getCallId(), baseInfo.getTelPhone(), baseInfo.getComplaintStatus());
        } else {
            log.warn("未查询到海尔消金基础信息, callId: {}", command.getCallId());
        }
        return baseInfo;
    }

    /**
     * 根据callId查询小结ID
     */
    private Long findSummaryIdByCallId(String callId) {
        // 查询最新的小结记录
        LambdaQueryWrapper<CommunicateSummary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommunicateSummary::getCallId, callId)
                .eq(CommunicateSummary::getIsDeleted, 0)
                .orderByDesc(CommunicateSummary::getCreatedTime)
                .last("LIMIT 1");

        CommunicateSummary summary = communicateSummaryMapper.selectOne(wrapper);
        return summary != null ? summary.getId() : null;
    }

    /**
     * 根据海尔消金基础信息创建工单数据实体
     */
    private WorkOrderDataEntity createWorkOrderDataFromBaseInfo(QueryTransferCallResponse baseInfo,
                                                                IvrIncomingCommand command) {
        log.info("根据海尔消金基础信息创建工单数据, callId: {}", command.getCallId());

        // 创建客户信息 - 只填充有数据的字段
        CustomerInfo.CustomerInfoBuilder customerInfoBuilder = CustomerInfo.builder();
        if (StringUtils.isNotBlank(baseInfo.getTelPhone())) {
            customerInfoBuilder.mobile(baseInfo.getTelPhone());
        }
        CustomerInfo customerInfo = customerInfoBuilder.build();

        // 创建业务数据 - 只填充有数据的字段
        BusinessData.BusinessDataBuilder businessDataBuilder = BusinessData.builder();
        if (CollectionUtils.isNotEmpty(baseInfo.getLoanList())) {
            businessDataBuilder.loanOrderNoList(baseInfo.getLoanList());
        }
        BusinessData businessData = businessDataBuilder.build();

        // 创建扩展数据 - 存储baseInfo中所有存在的字段和udeskCallId
        ExtendData extendData = ExtendData.builder().build();

        // 添加baseInfo中所有存在的字段
        if (StringUtils.isNotBlank(baseInfo.getTelPhone())) {
            extendData.addAttribute("telPhone", baseInfo.getTelPhone());
        }
        if (StringUtils.isNotBlank(baseInfo.getCallTime())) {
            extendData.addAttribute("callTime", baseInfo.getCallTime());
        }
        if (StringUtils.isNotBlank(baseInfo.getComplaintStatus())) {
            extendData.addAttribute("complaintStatus", baseInfo.getComplaintStatus());
        }
        if (StringUtils.isNotBlank(baseInfo.getChannelType())) {
            extendData.addAttribute("channelType", baseInfo.getChannelType());
        }
        if (StringUtils.isNotBlank(baseInfo.getIsTransfer())) {
            extendData.addAttribute("isTransfer", baseInfo.getIsTransfer());
        }
        if (StringUtils.isNotBlank(baseInfo.getCallTransferRemark())) {
            extendData.addAttribute("callTransferRemark", baseInfo.getCallTransferRemark());
        }
        if (CollectionUtils.isNotEmpty(baseInfo.getLoanList())) {
            extendData.addAttribute("loanList", baseInfo.getLoanList());
        }

        // 添加udeskCallId
        if (StringUtils.isNotBlank(command.getUdeskCallId())) {
            extendData.addAttribute("udeskCallId", command.getUdeskCallId());
        }

        // 固定值和必填字段
        extendData.addAttribute("cooperativeOrgCode", ApolloConstant.HAIER_COMPLAINT_COOPER_ORG_NO); // 合作机构编号
        extendData.addAttribute("originalCallId", command.getCallId()); // 原始通话ID

        // 创建工单数据实体
        WorkOrderDataEntity workOrderData = WorkOrderDataEntity.builder()
                .customerInfo(customerInfo)
                .businessData(businessData)
                .extendData(extendData)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        log.info("工单数据实体创建成功, callId: {}",
                command.getCallId());

        return workOrderData;
    }


    /**
     * 填充来电时间
     */
    private void fillCallTime(WorkOrderEntity workOrder, QueryTransferCallResponse baseInfo) {
        // 填充来电时间
        if (StringUtils.isNotBlank(baseInfo.getCallTime())) {
            LocalDateTime callTime =
                    LocalDateTimeUtils.parseLocalDateTimeByDateStr(baseInfo.getCallTime());
            workOrder.setCallTime(callTime);
            log.info("来电时间填充成功, orderNo: {}, callTime: {}",
                    workOrder.getOrderNo(), callTime);
        } else {
            // 没有来电时间时使用当前时间
            workOrder.setCallTime(LocalDateTime.now());
            log.info("海尔消金未返回来电时间，使用当前时间, orderNo: {}", workOrder.getOrderNo());
        }

    }

}
