package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 人群退款服务
 *
 * <AUTHOR>
 * @version $ CrowdRefundService, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
public interface CrowdRefundService {

    /**
     * 处理人群用户退款
     *
     * @param userDataList 用户数据列表
     */
    void processCrowdRefund(List<CrowdUserDataDto> userDataList);

    /**
     * 处理单个用户退款
     *
     * @param userData 用户数据
     */
    void processUserRefund(CrowdUserDataDto userData);
}
