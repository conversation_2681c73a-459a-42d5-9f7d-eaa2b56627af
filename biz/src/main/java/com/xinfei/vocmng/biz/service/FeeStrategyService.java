/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;
import com.xinfei.vocmng.biz.model.enums.FeeStrategyEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.DictConfigMapper;
import com.xinfei.vocmng.dal.mapper.DictDetailMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.DictConfig;
import com.xinfei.vocmng.dal.po.DictDetail;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.EngineFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.LendQueryFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.util.LogUtil;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ FeeStrategyService, v 0.1 2024-12-04 11:36 junjie.yan Exp $
 */
@Service
@Slf4j
public abstract class FeeStrategyService<T_IN> {

    @Resource
    private DictConfigMapper dictConfigMapper;

    @Resource
    private DictDetailMapper dictDetailMapper;

    @Resource
    private EngineFeignService engineFeignService;

    @Resource
    protected CisFacadeClient cisFacadeClient;

    @Resource
    protected CisFacadeClientService cisFacadeClientService;

    @Resource
    protected LendQueryFacadeClientImpl lendQueryFacadeClient;

    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    protected UserLabelService userLabelService;

    //子类无法实现
    public final StrategyExecutionResult<FeeStrategyConfig> executeStrategy(FeeStrategyEnum strategyEnum, T_IN strategyInput) {
        // 1. 获取对应场景下的策略配置
        FeeStrategyConfig config = getFeeStrategyConfig(strategyEnum);

        // 2. 获取策略对应的条件参数
        Map<String, String> keys = getFeeStrategyKey(config.getStrategyId());

        // 3. 获取所有入参条件的值（由子类实现具体逻辑）
        Map<String, Object> keyValues = getValue(keys, strategyInput);

        // 4. 允许子类扩展/修改 keyValues（钩子方法）
        String loanNo = customizeKeyValues(keyValues, strategyInput);

        // 5. 记录日志
        log.info(LogUtil.infoLog(strategyEnum.getScene(), keyValues));

        // 6. 调用决策引擎获取结果
        Map<String, String> result = getFeeStrategyValue(config.getStrategyId(), keyValues, strategyEnum.getScene());

        log.info(LogUtil.strategyInfoLog(config.getStrategyId(), loanNo, keyValues, result));

        return new StrategyExecutionResult<>(result, config);
    }

    //子类必须实现
    protected abstract Map<String, Object> getValue(Map<String, String> dictKeys, T_IN strategyInput);


    /**
     * 允许子类自定义 keyValues 的钩子方法
     * 子类可以实现
     */
    protected String customizeKeyValues(Map<String, Object> keyValues, T_IN strategyInput) {
        return "";
    }

    /**
     * 公用的获取订单信息方法
     */
    protected ManageOrderDetailDTO getOrderInfoByLoanNo(String loanNo) {
        ManageOrderListRequest request = new ManageOrderListRequest();
        request.setLoanNos(Collections.singletonList(loanNo));
        Page<ManageOrderDetailDTO> manageOrderDetailDTOPage = lendQueryFacadeClient.getOrderList(request);
        if (CollectionUtils.isEmpty(manageOrderDetailDTOPage.getPageList()) || manageOrderDetailDTOPage.getPageList().get(0) == null) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "获取订单信息失败");
        }
        return manageOrderDetailDTOPage.getPageList().get(0);
    }

    protected LoanPlanResponse getLoanInfo(String loanNo) {
        LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
        loanPlanRequest.setLoanNos(Collections.singletonList(loanNo));
        List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
        if (CollectionUtils.isEmpty(loanPlanResponses)) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "获取借据信息失败");
        }
        return loanPlanResponses.get(0);
    }

    public Map<String, String> getFeeStrategyKey(String id) {

        DictConfig dictConfig = dictConfigMapper.queryByName(id);
        if (dictConfig == null) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "策略：" + id + "未配置字典");
        }

        List<DictDetail> dictDetails = dictDetailMapper.queryByDictConfigId(dictConfig.getId());
        if (CollectionUtils.isEmpty(dictDetails)) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "策略：" + id + "未配置入参");
        }

        return dictDetails.stream().collect(Collectors.toMap(DictDetail::getDictKey, DictDetail::getDictValue));
    }

    public Boolean isReLoan(Long userNo) {
        ManageOrderListRequest request = new ManageOrderListRequest();
        request.setUserNos(Collections.singletonList(userNo));
        Page<ManageOrderDetailDTO> manageOrderDetailDTOPage = lendQueryFacadeClient.getOrderList(request);
        if (CollectionUtils.isEmpty(manageOrderDetailDTOPage.getPageList())) {
            return false;
        }
        return manageOrderDetailDTOPage.getPageList().size() > 1;
    }

    public Double getOrders(Long userNo) {
        ManageOrderListRequest request = new ManageOrderListRequest();
        request.setUserNos(Collections.singletonList(userNo));
        request.setOrderStatuses(Collections.singletonList("03"));
        Page<ManageOrderDetailDTO> manageOrderDetailDTOPage = lendQueryFacadeClient.getOrderList(request);
        if (CollectionUtils.isNotEmpty(manageOrderDetailDTOPage.getPageList())) {
            BigDecimal total = manageOrderDetailDTOPage.getPageList().stream().map(ManageOrderDetailDTO::getLoanAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            return total.doubleValue() / manageOrderDetailDTOPage.getPageList().size();
        }
        return 0.00;
    }

    public Map<String, String> getFeeStrategyValue(String engineCode, Map<String, Object> fields, String scene) {
        return engineFeignService.decision(engineCode, fields, scene);
    }

    public FeeStrategyConfig getFeeStrategyConfig(FeeStrategyEnum feeStrategyEnum) {
        List<FeeStrategyConfig> feeStrategyConfigs = UserContextHolder.getUserContext().getFeeStrategyConfigs();
        if (CollectionUtils.isEmpty(feeStrategyConfigs)) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "该坐席对应的角色未配置新费控减免策略");
        }
        FeeStrategyConfig config = feeStrategyConfigs.stream()
                .filter(r -> feeStrategyEnum.getScene().equals(r.getDictKey()))
                .findFirst()
                .orElse(null);

        if (config == null || StringUtils.isEmpty(config.getStrategyId())) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "新费控场景：" + feeStrategyEnum.getComment() + " 未配置减免策略");
        }
        log.info(LogUtil.infoLog("OrderFeeStrategyConfig", config));
        return config;
    }


}