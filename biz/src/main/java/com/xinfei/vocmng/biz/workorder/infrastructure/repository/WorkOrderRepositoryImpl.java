package com.xinfei.vocmng.biz.workorder.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinfei.vocmng.biz.workorder.domain.workorder.aggregate.WorkOrderAggregate;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderDataEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.repository.WorkOrderRepository;
import com.xinfei.vocmng.biz.workorder.infrastructure.converter.WorkOrderMapStructConverter;
import com.xinfei.vocmng.dal.mapper.WorkOrderDataMapper;
import com.xinfei.vocmng.dal.mapper.WorkOrderMiddlewareMapper;
import com.xinfei.vocmng.dal.po.WorkOrderDataPO;
import com.xinfei.vocmng.dal.po.WorkOrderMiddlewarePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.Optional;

/**
 * 工单仓储实现类
 * 负责工单聚合的持久化操作
 *
 * <AUTHOR>
 * @version $ WorkOrderRepositoryImpl, v 0.1 2025/07/30 WorkOrderRepositoryImpl Exp $
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class WorkOrderRepositoryImpl implements WorkOrderRepository {

    private final WorkOrderMiddlewareMapper workOrderMiddlewareMapper;
    private final WorkOrderDataMapper workOrderDataMapper;
    private final WorkOrderMapStructConverter workOrderConverter;

    @Override
    public WorkOrderAggregate save(WorkOrderAggregate aggregate) {
        if (Objects.isNull(aggregate) || Objects.isNull(aggregate.getWorkOrder())) {
            throw new IllegalArgumentException("工单聚合不能为空");
        }
        WorkOrderEntity workOrder = aggregate.getWorkOrder();
        WorkOrderMiddlewarePO po = workOrderConverter.entityToPO(workOrder);

        // 插入工单主表
        workOrderMiddlewareMapper.insert(po);
        // 更新聚合中的ID
        workOrder.setId(po.getId());

        // 保存工单数据实体（如果存在）
        if (Objects.nonNull(aggregate.getWorkOrderData())) {
            aggregate.getWorkOrderData().setWorkOrderId(po.getId());
            WorkOrderDataPO workOrderDataPO = workOrderConverter.dataEntityToPO(aggregate.getWorkOrderData());
            workOrderDataMapper.insert(workOrderDataPO);
            // 设置生成的ID到聚合中
            aggregate.getWorkOrderData().setId(workOrderDataPO.getId());

            log.info("保存工单数据成功, workOrderId: {}, dataId: {}", po.getId(), workOrderDataPO.getId());
        }

        log.info("保存工单聚合成功, id: {}, orderNo: {}", po.getId(), workOrder.getOrderNo());
        return aggregate;
    }


    @Override
    public Optional<WorkOrderAggregate> findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getOrderNo, orderNo);

        WorkOrderMiddlewarePO po = workOrderMiddlewareMapper.selectOne(wrapper);
        if (Objects.isNull(po)) {
            return Optional.empty();
        }

        WorkOrderEntity entity = workOrderConverter.poToEntity(po);
        WorkOrderAggregate aggregate = new WorkOrderAggregate();
        aggregate.setWorkOrder(entity);

        // 查询并设置工单数据实体
        LambdaQueryWrapper<WorkOrderDataPO> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(WorkOrderDataPO::getWorkOrderId, po.getId());
        WorkOrderDataPO workOrderDataPO = workOrderDataMapper.selectOne(dataWrapper);

        if (Objects.nonNull(workOrderDataPO)) {
            WorkOrderDataEntity dataEntity = workOrderConverter.poToDataEntity(workOrderDataPO);
            aggregate.setWorkOrderData(dataEntity);
        }

        return Optional.of(aggregate);
    }

    @Override
    public WorkOrderAggregate update(WorkOrderAggregate aggregate) {
        if (Objects.isNull(aggregate) || Objects.isNull(aggregate.getWorkOrder())) {
            throw new IllegalArgumentException("工单聚合不能为空");
        }
        WorkOrderEntity workOrder = aggregate.getWorkOrder();
        WorkOrderMiddlewarePO po = workOrderConverter.entityToPO(workOrder);

        // 更新工单主表
        workOrderMiddlewareMapper.updateById(po);
        if (Objects.nonNull(aggregate.getWorkOrderData())) {
            WorkOrderDataPO workOrderDataPO = workOrderConverter.dataEntityToPO(aggregate.getWorkOrderData());
            workOrderDataMapper.updateById(workOrderDataPO);
        }

        log.info("更新工单聚合成功, id: {}, orderNo: {}", po.getId(), workOrder.getOrderNo());
        return aggregate;
    }

    @Override
    public boolean existsByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return false;
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getOrderNo, orderNo);

        Long count = workOrderMiddlewareMapper.selectCount(wrapper);
        return Objects.nonNull(count) && count > 0;
    }

    @Override
    public Optional<WorkOrderAggregate> findByCallId(String callId) {
        if (StringUtils.isBlank(callId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getCallId, callId);

        WorkOrderMiddlewarePO po = workOrderMiddlewareMapper.selectOne(wrapper);
        if (Objects.isNull(po)) {
            return Optional.empty();
        }

        WorkOrderEntity entity = workOrderConverter.poToEntity(po);
        WorkOrderAggregate aggregate = new WorkOrderAggregate();
        aggregate.setWorkOrder(entity);

        // 查询并设置工单数据实体
        LambdaQueryWrapper<WorkOrderDataPO> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(WorkOrderDataPO::getWorkOrderId, po.getId());
        WorkOrderDataPO workOrderDataPO = workOrderDataMapper.selectOne(dataWrapper);

        if (Objects.nonNull(workOrderDataPO)) {
            WorkOrderDataEntity dataEntity = workOrderConverter.poToDataEntity(workOrderDataPO);
            aggregate.setWorkOrderData(dataEntity);
        }

        return Optional.of(aggregate);
    }

    @Override
    public Optional<WorkOrderAggregate> findByUdeskCallId(String udeskCallId) {
        if (StringUtils.isBlank(udeskCallId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getUdeskCallId, udeskCallId);

        WorkOrderMiddlewarePO po = workOrderMiddlewareMapper.selectOne(wrapper);
        if (Objects.isNull(po)) {
            return Optional.empty();
        }

        WorkOrderEntity entity = workOrderConverter.poToEntity(po);
        WorkOrderAggregate aggregate = new WorkOrderAggregate();
        aggregate.setWorkOrder(entity);

        // 查询并设置工单数据实体
        LambdaQueryWrapper<WorkOrderDataPO> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(WorkOrderDataPO::getWorkOrderId, po.getId());
        WorkOrderDataPO workOrderDataPO = workOrderDataMapper.selectOne(dataWrapper);

        if (Objects.nonNull(workOrderDataPO)) {
            WorkOrderDataEntity dataEntity = workOrderConverter.poToDataEntity(workOrderDataPO);
            aggregate.setWorkOrderData(dataEntity);
        }

        return Optional.of(aggregate);
    }

    @Override
    public Optional<WorkOrderAggregate> findBySummaryId(Long summaryId) {
        if (Objects.isNull(summaryId) || summaryId <= 0) {
            return Optional.empty();
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getSummaryId, summaryId);

        WorkOrderMiddlewarePO po = workOrderMiddlewareMapper.selectOne(wrapper);
        if (Objects.isNull(po)) {
            return Optional.empty();
        }

        WorkOrderEntity entity = workOrderConverter.poToEntity(po);
        WorkOrderAggregate aggregate = new WorkOrderAggregate();
        aggregate.setWorkOrder(entity);

        // 查询并设置工单数据实体
        LambdaQueryWrapper<WorkOrderDataPO> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(WorkOrderDataPO::getWorkOrderId, po.getId());
        WorkOrderDataPO workOrderDataPO = workOrderDataMapper.selectOne(dataWrapper);

        if (Objects.nonNull(workOrderDataPO)) {
            WorkOrderDataEntity dataEntity = workOrderConverter.poToDataEntity(workOrderDataPO);
            aggregate.setWorkOrderData(dataEntity);
        }

        return Optional.of(aggregate);
    }

    @Override
    public boolean existsByCallId(String callId) {
        if (StringUtils.isBlank(callId)) {
            return false;
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getCallId, callId);

        Long count = workOrderMiddlewareMapper.selectCount(wrapper);
        return Objects.nonNull(count) && count > 0;
    }

    @Override
    public boolean existsByUdeskCallId(String udeskCallId) {
        if (StringUtils.isBlank(udeskCallId)) {
            return false;
        }
        LambdaQueryWrapper<WorkOrderMiddlewarePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderMiddlewarePO::getUdeskCallId, udeskCallId);

        Long count = workOrderMiddlewareMapper.selectCount(wrapper);
        return Objects.nonNull(count) && count > 0;
    }

}
