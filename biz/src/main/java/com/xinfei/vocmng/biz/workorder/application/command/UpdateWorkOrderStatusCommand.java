package com.xinfei.vocmng.biz.workorder.application.command;

import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 更新工单状态命令
 * 支持通过callId或orderNo查找工单
 *
 * <AUTHOR>
 * @version $ UpdateWorkOrderStatusCommand, v 0.1 2025/08/11 UpdateWorkOrderStatusCommand Exp $
 */
@Data
@Builder
public class UpdateWorkOrderStatusCommand {

    /**
     * 海尔消金通话ID（三方传过来的主要标识）
     */
    private String callId;

    /**
     * 工单编号（创建工单后回传给我们的）
     */
    private String orderNo;

    /**
     * 新状态
     */
    private WorkOrderStatus newStatus;

    /**
     * 变更原因
     */
    private String reason;

    /**
     * 原工单系统ID
     */
    private Long originalWorkOrderId;

    /**
     * 外部工单状态（原始状态码）
     */
    private String externalWorkOrderStatus;

    /**
     * 跟进时间
     */
    private String processTime;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 事件类型：CREATED/FEEDBACK/CLOSED
     */
    private String eventType;

    /**
     * 验证命令参数
     */
    public void validate() {
        // callId和orderNo至少要有一个
        if (StringUtils.isBlank(callId) &&
                StringUtils.isBlank(orderNo)) {
            throw new IllegalArgumentException("callId和orderNo不能同时为空");
        }

        if (Objects.isNull(newStatus)) {
            throw new IllegalArgumentException("新状态不能为空");
        }
    }
}
