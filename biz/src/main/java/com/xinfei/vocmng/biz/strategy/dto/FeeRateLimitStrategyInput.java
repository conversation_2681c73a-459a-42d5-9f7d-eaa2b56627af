/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy.dto;

import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.biz.rr.request.RepaymentProcessReq;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 费率限制策略输入参数
 *
 * <AUTHOR>
 * @version $ FeeRateLimitStrategyInput, v 0.1 2025-05-06 16:44 junjie.yan Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeeRateLimitStrategyInput {

    /**
     * 借据号
     */
    private String loanNo;
    
    /**
     * 订单详情信息
     */
    private ManageOrderDetailDTO orderDetailDTO;

    /**
     * 借据信息
     */
    private LoanPlanResponse loanPlanResponse;

    /**
     * 用户标签
     */
    private String userLabels;
}