package com.xinfei.vocmng.biz.constants;


public class RedisKeyConstants {

    /** 聚合支付短信url */
    public static final String REPAY_AGGREGATE = "repay_aggregate_data_";

    /** 在线抵扣银行卡列表 */
    public static final String REPAY_BANK_LIST = "repay_bank_list_";

    /** 是否包含按日计息订单 */
    public static final String HAS_DAILY = "has_daily_type_";

    /** 订单服务类型 */
    public static final String SERVICE_TYPE_FOR_APP = "service_type_for_app";

    /** 查询支持的银行列表 */
    public static final String BANK_LIST = "bank_list";

    /** 方案失效时间 */
    public static final String END_TIME = "end_time_";

    /** 合并任务队列 (新版本) */
    public static final String MERGE_QUEUE = "voc.merge.queue";

    /** 合并任务重试队列 (新版本) */
    public static final String MERGE_RETRY_QUEUE = "voc.merge.retry.queue";

    /** 合并任务处理中集合 (新版本) */
    public static final String MERGE_PROCESSING_SET = "voc.merge.processing";

    /** 合并任务失败集合 (新版本) */
    public static final String MERGE_FAILED_SET = "voc.merge.failed";

    /** 已合并用户 */
    public static final String MERGED_ZSET = "voc.merge.merged";

    // 存储所有webToken的Set
    public static final String WEB_TOKEN_SET_KEY = "voc.merge.web_tokens";

    // 存储已合并过的
    public static final String FLAG_KEY_PREFIX = "voc.merge.flag:";

    /** 人群退款幂等缓存前缀 */
    public static final String CROWD_REFUND_IDEMPOTENT_PREFIX = "voc.crowd.refund.idempotent:";

    /** 工单处理幂等缓存前缀 */
    public static final String WORK_ORDER_IDEMPOTENT_PREFIX = "voc.work.order.idempotent:";

    /** 会员订单退款幂等缓存前缀 */
    public static final String VIP_ORDER_REFUND_IDEMPOTENT_PREFIX = "voc.vip.order.refund.idempotent:";

    /** 海尔消金基础信息缓存前缀 */
    public static final String HAIER_BASE_INFO_PREFIX = "haier_base_info:";

    /** 海尔消金基础信息缓存时间（秒） - 30分钟 */
    public static final int HAIER_BASE_INFO_EXPIRE_SECONDS = 30 * 60;

    /**
     * 构建海尔消金基础信息缓存Key
     *
     * @param callId 通话ID
     * @return 完整的缓存Key
     */
    public static String buildHaierBaseInfoKey(String callId) {
        return HAIER_BASE_INFO_PREFIX + callId;
    }
}
