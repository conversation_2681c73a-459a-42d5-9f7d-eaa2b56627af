package com.xinfei.vocmng.biz.consumer;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.MergeQueueService;
import com.xinfei.vocmng.biz.service.MergeTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 合并任务队列消费者
 * 使用定时任务从Redis队列中获取并处理任务
 *
 * <AUTHOR>
 * @version $ MergeQueueConsumer, v 0.1 2025/5/1 $
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MergeQueueRetryConsumer extends RepaymentTaskManage {

    private final MergeQueueService mergeQueueService;
    private final MergeTaskHandler mergeTaskHandler;
    private final MergeTaskService mergeTaskService;

    @Override
    protected String getCron() {
        return "* * * * * ?";
    }

    @Override
    protected void processTask() {
        try {
            // 从重试队列获取任务
            String webToken = mergeQueueService.popRetryTask();

            if (StringUtils.isBlank(webToken)) {
                return; // 队列为空，直接返回
            }

            log.info("Consuming task from retry queue: webToken={}", webToken);

            try {
                // 处理任务
                mergeTaskHandler.handleTask(webToken);

                // 标记任务完成
                mergeTaskService.markTaskComplete(webToken);

                log.info("Successfully processed retry task: webToken={}", webToken);
            } catch (Exception e) {
                log.error("Failed to process retry task: webToken={}", webToken, e);
                mergeQueueService.addToRetryQueue(webToken);
            }
        } catch (Exception e) {
            log.error("Error in consumeRetryQueue", e);
        }
    }
}
