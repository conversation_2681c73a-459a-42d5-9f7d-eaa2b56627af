/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.ResourceRoleMappingService;
import com.xinfei.vocmng.dal.mapper.ResourceRoleMappingMapper;
import com.xinfei.vocmng.dal.po.ResourceRoleMapping;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ ResourceRoleMappingServiceImpl, v 0.1 2023/12/26 17:54 wancheng.qu Exp $
 */
@Service
public class ResourceRoleMappingServiceImpl extends BaseService<ResourceRoleMappingMapper, ResourceRoleMapping> implements ResourceRoleMappingService {


    @Override
    @Transactional
    public void batchSave(List<ResourceRoleMapping> list) {
        saveBatch(list);
    }
}