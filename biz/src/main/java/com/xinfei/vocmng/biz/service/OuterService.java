/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.req.ListEntryReq;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ OuterService, v 0.1 2023/12/21 17:17 wancheng.qu Exp $
 */

public interface OuterService {


     void sendSms(String mobile, String code);

     String getOauthUserInfo(String authCode);

     Map<Long,Boolean> getWork(List<Long> ids);

     void sendList(List<ListEntryReq> req);

}