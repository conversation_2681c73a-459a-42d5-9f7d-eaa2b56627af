/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy.dto;

import com.xinfei.vocmng.biz.rr.request.VipCardRefundLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ FeeStrategyContext, v 0.1 2025-05-06 14:44 junjie.yan Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VipRefundStrategyInput {

    private VipCardRefundLog vipCardRefundLog; // 如果是 VIP 策略需要

}