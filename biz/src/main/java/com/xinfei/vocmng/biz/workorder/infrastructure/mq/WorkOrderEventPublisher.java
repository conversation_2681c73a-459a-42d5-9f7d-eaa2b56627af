package com.xinfei.vocmng.biz.workorder.infrastructure.mq;

import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;

/**
 * 工单事件发布器接口
 * 负责将领域事件发布到MQ
 *
 * <AUTHOR>
 * @version $ WorkOrderEventPublisher, v 0.1 2025/07/30 WorkOrderEventPublisher Exp $
 */
public interface WorkOrderEventPublisher {

    /**
     * 发布工单事件
     */
    void publishEvent(WorkOrderEvent event);

    /**
     * 发布工单创建事件
     */
//    void publishWorkOrderCreateEvent(WorkOrderEvent event);

    /**
     * 发布工单状态变更事件
     */
    void publishWorkOrderStatusChangeEvent(WorkOrderEvent event);
}
