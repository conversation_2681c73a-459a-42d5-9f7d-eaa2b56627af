package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import com.xinfei.vocmng.itl.rr.haier.QueryTransferCallResponse;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 业务数据值对象
 *
 * <AUTHOR>
 * @version $ BusinessData, v 0.1 2025/07/30 BusinessData Exp $
 */
@Data
@Builder
public class BusinessData {

    /**
     * 借据号
     */
    private List<QueryTransferCallResponse.LoanInfo> loanOrderNoList;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 问题分类
     */
    private String category;

    /**
     * 发生时间
     */
    private LocalDateTime occurTime;

    /**
     * 联系方式
     */
    private String contactMethod;

    /**
     * 期望解决方案
     */
    private String expectedSolution;

    /**
     * 验证业务数据
     */
    public void validate() {
        if (StringUtils.isBlank(description)) {
            throw new IllegalArgumentException("问题描述不能为空");
        }
        
        if (description.length() > 2000) {
            throw new IllegalArgumentException("问题描述不能超过2000字符");
        }
        
        // 验证金额（如果提供）
        if (Objects.nonNull(amount) && amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }

        // 验证发生时间（如果提供）
        if (Objects.nonNull(occurTime) && occurTime.isAfter(LocalDateTime.now())) {
            throw new IllegalArgumentException("发生时间不能晚于当前时间");
        }
    }

}
