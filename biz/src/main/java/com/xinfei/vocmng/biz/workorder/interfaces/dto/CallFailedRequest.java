package com.xinfei.vocmng.biz.workorder.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 接线失败事件请求
 *
 * <AUTHOR>
 * @version $ CallFailedRequest, v 0.1 2025/08/11 CallFailedRequest Exp $
 */
@Data
@ApiModel("接线失败事件请求")
public class CallFailedRequest {

    @ApiModelProperty(value = "海尔消金通话ID", required = true)
    private String callId;

    @ApiModelProperty(value = "失败原因", required = true)
    private String failReason;
}
