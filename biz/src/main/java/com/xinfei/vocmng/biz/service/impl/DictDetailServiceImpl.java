/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.DictDetailService;
import com.xinfei.vocmng.dal.mapper.DictDetailMapper;
import com.xinfei.vocmng.dal.po.DictDetail;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DictDetailServiceImpl, v 0.1 2024/4/9 20:05 wancheng.qu Exp $
 */
@Service
public class DictDetailServiceImpl extends BaseService<DictDetailMapper, DictDetail> implements DictDetailService {

    @Override
    @Transactional
    public void saveBatchs(List<DictDetail> req) {
        saveBatch(req);
    }
}