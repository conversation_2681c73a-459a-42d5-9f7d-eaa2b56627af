package com.xinfei.vocmng.biz.component.remote.processor;

import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;
import com.xinfei.vocmng.itl.model.header.BaseHeader;
import com.xinfei.vocmng.itl.model.header.MemberInterestHeader;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/20
 */
@Component
@ProcessRule(service = ThirdPartyServiceEnum.MEMBER_INTEREST)
public class MemberInterestHeaderProcessor extends AbstractHeaderProcessRule{
    @Override
    protected BaseHeader buildHeader(RequestTemplate template) {
        MemberInterestHeader header = new MemberInterestHeader();
        header.setAppName("xyf");
        header.setVersionCode(1);
        header.setOs("wap");
        header.setAppId(vocConfig.getMemberInterestAppId());
        header.setAppSecret(vocConfig.getMemberInterestAppSecret());
        return header;
    }

    @Override
    public <T> void process(RequestTemplate template) {
        headerProcess(template);
    }
}
