package com.xinfei.vocmng.biz.component.remote.processor;

import com.xinfei.vocmng.biz.constants.CISClientConstants;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;
import com.xinfei.vocmng.itl.model.header.BaseHeader;
import com.xinfei.vocmng.itl.model.header.CISHeader;
import feign.RequestTemplate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @since 2023/12/17
 */
@Component
@ProcessRule(service = ThirdPartyServiceEnum.CIS)
public class CISHeaderProcessor extends AbstractHeaderProcessRule {

    @Override
    public <T> void process(RequestTemplate template) {
        headerProcess(template);
    }

    @Override
    public BaseHeader buildHeader(RequestTemplate template) {
        CISHeader header = new CISHeader();
        header.setTraceId(UUID.randomUUID().toString());
        header.setAppName(VocConstants.APP_NAME);
        header.setRequestTime(DateFormatUtils.format(new Date(), CISClientConstants.REQUEST_TIME_FORMAT));

        return header;
    }
}
