package com.xinfei.vocmng.biz.workorder.interfaces.web;

import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.workorder.application.command.UpdateWorkOrderStatusCommand;
import com.xinfei.vocmng.biz.workorder.application.service.WorkOrderApplicationService;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExternalWorkOrderStatus;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.biz.workorder.interfaces.dto.WorkOrderStatusUpdateRequest;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工单中间件接口控制器
 *
 * <AUTHOR>
 * @version $ WorkOrderMiddlewareController, v 0.1 2025/07/30 WorkOrderMiddlewareController Exp $
 */
@Api(tags = "工单中间件接口")
@RestController
@RequestMapping("/api/funder/workorder")
@Slf4j
@RequiredArgsConstructor
public class WorkOrderMiddlewareController {

    private final WorkOrderApplicationService workOrderApplicationService;
    
    /**
     * 工单状态更新接口
     */
    @ApiOperation("工单状态更新接口")
    @PostMapping("/status/update")
    @DigestLogAnnotated("updateWorkOrderStatus")
    public ApiResponse<String> updateWorkOrderStatus(
            @RequestBody WorkOrderStatusUpdateRequest request) {
        // 解析外部工单状态并映射到内部状态
        ExternalWorkOrderStatus externalStatus = ExternalWorkOrderStatus.fromCode(request.getWorkOrderStatus());
        WorkOrderStatus internalStatus = externalStatus.toInternalStatus();

        // 构建更新状态命令
        UpdateWorkOrderStatusCommand command = UpdateWorkOrderStatusCommand.builder()
                .callId(request.getHaierCallId())
                .orderNo(request.getWorkOrderNo())
                .newStatus(internalStatus)
                .reason(buildReasonFromRequest(request))
                .externalWorkOrderStatus(String.valueOf(request.getWorkOrderStatus()))
                .processTime(request.getProcessTime())
                .customerStatus(request.getCustomerStatus())
                .eventType(request.getEventType())
                .build();
        // 验证命令参数
        command.validate();
        // 更新工单状态
        workOrderApplicationService.updateWorkOrderStatus(command);
        return ApiResponse.success("工单状态更新成功");
    }

    /**
     * 根据请求构建原因描述
     */
    private String buildReasonFromRequest(WorkOrderStatusUpdateRequest request) {
        StringBuilder reason = new StringBuilder();

        if (request.getCustomerStatus() != null) {
            reason.append("客户状态: ").append(request.getCustomerStatus()).append("; ");
        }

        if (request.getProcessTime() != null) {
            reason.append("跟进时间: ").append(request.getProcessTime()).append("; ");
        }

        if (request.getHaierCallId() != null) {
            reason.append("海尔消金通话ID: ").append(request.getHaierCallId()).append("; ");
        }

        return reason.toString();
    }
}
