package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.constants.VocConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 合并任务队列服务
 * 使用Redis List实现队列功能
 *
 * <AUTHOR>
 * @version $ MergeQueueService, v 0.1 2025/5/1 $
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MergeQueueService {

    private static final int MAX_RETRY_COUNT = 3;  // 最大重试次数
    private static final String RETRY_COUNT_PREFIX = "voc.merge.retry_count:";  // 重试计数前缀
    private static final long PROCESSING_TIMEOUT_SECONDS = 600; // 处理中状态超时时间（10分钟）

    private final StringRedisTemplate redisTemplate;
    private DefaultRedisScript<String> pushTaskScript;

    @PostConstruct
    public void init() {
        // 初始化Lua脚本
        pushTaskScript = new DefaultRedisScript<>();
        pushTaskScript.setLocation(new ClassPathResource("lua/push_merge_task_queue.lua"));
        pushTaskScript.setResultType(String.class);
        log.info("Initialized push_merge_task_queue.lua script");
    }

    /**
     * 将任务推送到主队列
     *
     * @param webToken 要推送的webToken
     * @return 推送结果："OK"-成功，"PROCESSING"-任务已在处理中，"DUPLICATE"-任务已在队列中
     */
    public String pushTask(String webToken) {
        if (ApolloConstant.pushQueueEnabled) {
            return "switch not open";
        }
        if (StringUtils.isBlank(webToken)) {
            log.warn("Cannot push blank webToken to queue");
            return "ERROR";
        }

        try {
            // 使用Lua脚本推送任务，避免重复推送
            String result = redisTemplate.execute(
                    pushTaskScript,
                    Collections.singletonList(RedisKeyConstants.MERGE_QUEUE),
                    webToken, RedisKeyConstants.MERGE_PROCESSING_SET);

            if ("OK".equals(result)) {
                log.info("Successfully pushed webToken {} to merge queue", webToken);
            } else if ("PROCESSING".equals(result)) {
                log.info("WebToken {} is already being processed, skipped pushing to queue", webToken);
            } else if ("DUPLICATE".equals(result)) {
                log.info("WebToken {} is already in the queue, skipped pushing again", webToken);
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to push webToken {} to merge queue", webToken, e);
            throw e;
        }
    }

    /**
     * 批量推送任务到主队列
     *
     * @param webTokens 要推送的webToken列表
     * @return 推送结果统计：成功数量、已处理数量、重复数量、错误数量
     */
    public Map<String, Integer> pushTaskList(List<String> webTokens) {
        if (CollectionUtils.isEmpty(webTokens)) {
            log.warn("Cannot push empty webToken list to queue");
            return Collections.singletonMap("error", 0);
        }

        Map<String, Integer> results = new HashMap<>();
        results.put("total", webTokens.size());
        results.put("success", 0);
        results.put("processing", 0);
        results.put("duplicate", 0);
        results.put("error", 0);

        // 批量处理前记录时间
        long startTime = System.currentTimeMillis();

        try {
            // 逐个处理每个webToken
            for (String webToken : webTokens) {
                if (StringUtils.isBlank(webToken)) {
                    results.put("error", results.get("error") + 1);
                    continue;
                }

                try {
                    String result = pushTask(webToken);

                    if ("OK".equals(result)) {
                        results.put("success", results.get("success") + 1);
                    } else if ("PROCESSING".equals(result)) {
                        results.put("processing", results.get("processing") + 1);
                    } else if ("DUPLICATE".equals(result)) {
                        results.put("duplicate", results.get("duplicate") + 1);
                    } else {
                        results.put("error", results.get("error") + 1);
                    }
                } catch (Exception e) {
                    log.error("Error pushing webToken {} to queue", webToken, e);
                    results.put("error", results.get("error") + 1);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("Batch push completed: {} webTokens processed in {}ms. Results: {}",
                    webTokens.size(), (endTime - startTime), results);

            return results;
        } catch (Exception e) {
            log.error("Failed to push webToken list to merge queue", e);
            results.put("error", webTokens.size());
            return results;
        }
    }

    /**
     * 从主队列获取任务
     *
     * @return 任务webToken，如果队列为空则返回null
     */
    public String popTask() {
        try {
            // 从队列头部获取任务
            String webToken = redisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_QUEUE);

            if (StringUtils.isNotBlank(webToken)) {
                // 将任务标记为处理中
                redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
                redisTemplate.expire(RedisKeyConstants.MERGE_PROCESSING_SET, PROCESSING_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                log.info("Popped webToken {} from merge queue and marked as processing", webToken);
            }

            return webToken;
        } catch (Exception e) {
            log.error("Failed to pop task from merge queue", e);
            return null;
        }
    }

    /**
     * 将任务标记为完成
     *
     * @param webToken 完成的任务webToken
     */
    public void markTaskComplete(String webToken) {
        if (StringUtils.isBlank(webToken)) {
            return;
        }

        try {
            // 从处理中集合移除
            redisTemplate.opsForSet().remove(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);

            // 清除重试计数
            redisTemplate.delete(RETRY_COUNT_PREFIX + webToken);

            log.info("Marked webToken {} as complete", webToken);
        } catch (Exception e) {
            log.error("Failed to mark webToken {} as complete", webToken, e);
        }
    }

    /**
     * 将任务添加到重试队列
     *
     * @param webToken 失败的任务webToken
     */
    public void addToRetryQueue(String webToken) {
        if (StringUtils.isBlank(webToken)) {
            return;
        }

        try {
            // 获取当前重试次数
            String retryCountKey = RETRY_COUNT_PREFIX + webToken;
            String retryCountStr = redisTemplate.opsForValue().get(retryCountKey);
            int retryCount = 0;

            if (StringUtils.isNotBlank(retryCountStr)) {
                try {
                    retryCount = Integer.parseInt(retryCountStr);
                } catch (NumberFormatException e) {
                    log.warn("Invalid retry count for webToken: {}, value: {}", webToken, retryCountStr);
                }
            }

            // 从处理中集合移除
            redisTemplate.opsForSet().remove(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);

            // 如果重试次数小于最大重试次数，则添加到重试队列
            if (retryCount < MAX_RETRY_COUNT) {
                // 更新重试次数
                redisTemplate.opsForValue().set(retryCountKey, String.valueOf(retryCount + 1), 7, TimeUnit.DAYS);

                // 添加到重试队列
                redisTemplate.opsForList().rightPush(RedisKeyConstants.MERGE_RETRY_QUEUE, webToken);
                log.info("Added webToken {} to retry queue, retry count: {}", webToken, retryCount + 1);
            } else {
                // 超过最大重试次数，添加到失败集合
                addToFailedSet(webToken);
                log.warn("WebToken {} has reached max retry count ({}), added to failed set", webToken, MAX_RETRY_COUNT);
            }
        } catch (Exception e) {
            log.error("Failed to add webToken {} to retry queue", webToken, e);
            // 处理异常时也添加到失败集合
            addToFailedSet(webToken);
        }
    }

    /**
     * 从重试队列获取任务
     *
     * @return 任务webToken，如果队列为空则返回null
     */
    public String popRetryTask() {
        try {
            // 从重试队列头部获取任务
            String webToken = redisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_RETRY_QUEUE);

            if (StringUtils.isNotBlank(webToken)) {
                // 将任务标记为处理中
                redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
                redisTemplate.expire(RedisKeyConstants.MERGE_PROCESSING_SET, PROCESSING_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                log.info("Popped webToken {} from retry queue and marked as processing", webToken);
            }

            return webToken;
        } catch (Exception e) {
            log.error("Failed to pop task from retry queue", e);
            return null;
        }
    }

    /**
     * 将任务添加到失败集合
     *
     * @param webToken 失败的任务webToken
     */
    public void addToFailedSet(String webToken) {
        if (StringUtils.isBlank(webToken)) {
            return;
        }

        try {
            // 将webToken添加到失败集合
            redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_FAILED_SET, webToken);
            log.info("Added webToken {} to failed set", webToken);
        } catch (Exception e) {
            log.error("Failed to add webToken {} to failed set", webToken, e);
        }
    }


    /**
     * 获取失败集合中的所有任务
     *
     * @return 失败任务集合
     */
    public Set<String> getFailedTasks() {
        try {
            return redisTemplate.opsForSet().members(RedisKeyConstants.MERGE_FAILED_SET);
        } catch (Exception e) {
            log.error("Failed to get failed tasks", e);
            return Collections.emptySet();
        }
    }


    /**
     * 获取队列长度
     *
     * @return 包含主队列和重试队列长度的Map
     */
    public Map<String, Long> getQueueSizes() {
        Map<String, Long> sizes = new HashMap<>();

        try {
            Long mainQueueSize = redisTemplate.opsForList().size(RedisKeyConstants.MERGE_QUEUE);
            Long retryQueueSize = redisTemplate.opsForList().size(RedisKeyConstants.MERGE_RETRY_QUEUE);
            Long processingSize = redisTemplate.opsForSet().size(RedisKeyConstants.MERGE_PROCESSING_SET);
            Long failedSize = redisTemplate.opsForSet().size(RedisKeyConstants.MERGE_FAILED_SET);

            sizes.put("mainQueue", mainQueueSize != null ? mainQueueSize : 0L);
            sizes.put("retryQueue", retryQueueSize != null ? retryQueueSize : 0L);
            sizes.put("processing", processingSize != null ? processingSize : 0L);
            sizes.put("failed", failedSize != null ? failedSize : 0L);
        } catch (Exception e) {
            log.error("Failed to get queue sizes", e);
            sizes.put("mainQueue", 0L);
            sizes.put("retryQueue", 0L);
            sizes.put("processing", 0L);
            sizes.put("failed", 0L);
        }

        return sizes;
    }

    /**
     * 清理处理超时的任务
     * 将处理中但已超时的任务重新放入重试队列
     */
    public void cleanupStaleTasks() {
        try {
            // 获取处理中的所有任务
            Set<String> processingTasks = redisTemplate.opsForSet().members(RedisKeyConstants.MERGE_PROCESSING_SET);

            if (Objects.isNull(processingTasks) || processingTasks.isEmpty()) {
                return;
            }

            log.info("Checking {} tasks in processing set for timeout", processingTasks.size());

            // 对于每个处理中的任务，检查是否超时
            for (String webToken : processingTasks) {
                // 将任务添加到重试队列
                addToRetryQueue(webToken);
                log.info("Moved stale task {} from processing set to retry queue", webToken);
            }
        } catch (Exception e) {
            log.error("Failed to cleanup stale tasks", e);
        }
    }
}
