package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Getter;

/**
 * 优先级枚举
 *
 * <AUTHOR>
 * @version $ Priority, v 0.1 2025/07/30 Priority Exp $
 */
@Getter
public enum Priority {

    /**
     * 紧急
     */
    URGENT(1, "紧急"),

    /**
     * 高
     */
    HIGH(2, "高"),

    /**
     * 中
     */
    MEDIUM(3, "中"),

    /**
     * 低
     */
    LOW(4, "低"),

    /**
     * 最低
     */
    LOWEST(5, "最低");

    private final int level;
    private final String description;

    Priority(int level, String description) {
        this.level = level;
        this.description = description;
    }

    /**
     * 根据级别获取枚举
     */
    public static Priority fromLevel(int level) {
        for (Priority priority : values()) {
            if (priority.level == level) {
                return priority;
            }
        }
        throw new IllegalArgumentException("未知的优先级: " + level);
    }

    /**
     * 比较优先级高低
     */
    public boolean isHigherThan(Priority other) {
        return this.level < other.level;
    }
}
