package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Getter;

/**
 * 外部工单系统状态枚举
 *
 * <AUTHOR>
 * @version $ ExternalWorkOrderStatus, v 0.1 2025/08/12 ExternalWorkOrderStatus Exp $
 */
@Getter
public enum ExternalWorkOrderStatus {

    /**
     * 0-未提交分配
     */
    NOT_SUBMITTED(0, "未提交分配"),

    /**
     * 1-待分配
     */
    PENDING_ASSIGNMENT(1, "待分配"),

    /**
     * 2-跟进中
     */
    FOLLOWING_UP(2, "跟进中"),

    /**
     * 3-转派
     */
    TRANSFERRED(3, "转派"),

    /**
     * 4-退单
     */
    RETURNED(4, "退单"),

    /**
     * 5-终止
     */
    TERMINATED(5, "终止"),

    /**
     * 6-知悉结案
     */
    ACKNOWLEDGED_CLOSED(6, "知悉结案"),

    /**
     * 7-催收结案
     */
    COLLECTION_CLOSED(7, "催收结案"),

    /**
     * 8-已结案
     */
    CLOSED(8, "已结案"),

    /**
     * 9-失联结案
     */
    LOST_CONTACT_CLOSED(9, "失联结案"),

    /**
     * 10-不接受方案
     */
    REJECTED_PLAN(10, "不接受方案");

    private final Integer code;
    private final String description;

    ExternalWorkOrderStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static ExternalWorkOrderStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ExternalWorkOrderStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的外部工单状态: " + code);
    }

    /**
     * 根据字符串代码获取枚举
     */
    public static ExternalWorkOrderStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        try {
            Integer intCode = Integer.valueOf(code);
            return fromCode(intCode);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的外部工单状态代码: " + code);
        }
    }

    /**
     * 是否为终态
     */
    public boolean isTerminal() {
        return this == RETURNED ||
               this == TERMINATED ||
               this == ACKNOWLEDGED_CLOSED ||
               this == COLLECTION_CLOSED ||
               this == CLOSED ||
               this == LOST_CONTACT_CLOSED ||
               this == REJECTED_PLAN;
    }

    /**
     * 映射到内部工单状态
     */
    public WorkOrderStatus toInternalStatus() {
        switch (this) {
            case NOT_SUBMITTED:
            case PENDING_ASSIGNMENT:
                return WorkOrderStatus.CREATED;
                
            case FOLLOWING_UP:
            case TRANSFERRED:
            case RETURNED:
                return WorkOrderStatus.PROCESSING;

            case TERMINATED:
                return WorkOrderStatus.CANCELLED;
                
            case ACKNOWLEDGED_CLOSED:
            case COLLECTION_CLOSED:
            case CLOSED:
            case LOST_CONTACT_CLOSED:
            case REJECTED_PLAN:
                return WorkOrderStatus.COMPLETED;
                
            default:
                return WorkOrderStatus.PENDING;
        }
    }

    /**
     * 映射到海尔消金客诉状态
     */
    public String toHaierComplaintStatus() {
        switch (this) {
            case NOT_SUBMITTED:
            case PENDING_ASSIGNMENT:
            case FOLLOWING_UP:
            case TRANSFERRED:
                return "process";
                
            case RETURNED:
            case TERMINATED:
            case ACKNOWLEDGED_CLOSED:
            case COLLECTION_CLOSED:
            case CLOSED:
            case LOST_CONTACT_CLOSED:
            case REJECTED_PLAN:
                return "close";
                
            default:
                return "process";
        }
    }
}
