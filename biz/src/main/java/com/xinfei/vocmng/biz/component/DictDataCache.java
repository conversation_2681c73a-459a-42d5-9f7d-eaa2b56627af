/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.xinfei.vocmng.dal.mapper.DictDetailMapper;
import com.xinfei.vocmng.dal.po.DictDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.Optional;

/**
 * 字典数据缓存
 * <AUTHOR>
 * @version $ DictDataCache, v 0.1 2024/4/10 16:52 wancheng.qu Exp $
 */
@Slf4j
@Component
public class DictDataCache {

    private final LoadingCache<Long, List<DictDetail>> cache;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1) ;


    @Autowired
    private DictDetailMapper dictDetailMapper;

    public DictDataCache() {
        this.cache = CacheBuilder.newBuilder()
                .build(new CacheLoader<Long, List<DictDetail>>() {
                    @Override
                    public List<DictDetail> load(Long dictConfigId) throws Exception {
                        return loadDataFromDatabase(dictConfigId);
                    }
                });
    }

    /**获取字典value*/
    public String get(Long dictConfigId,String key) {
        try {
            List<DictDetail> dictDetails = cache.getUnchecked(dictConfigId);
            Optional<DictDetail> optionalDetail = dictDetails.stream()
                    .filter(detail -> detail.getDictKey().equals(key))
                    .findFirst();
            return optionalDetail.map(DictDetail::getDictValue).orElse(null);
        } catch (Exception e) {
            log.warn("DictDataCache get cache error,msg:{}",e.getMessage());
            return null;
        }
    }

    public List<DictDetail> getAllDictById(Long dictConfigId) {
        return cache.getUnchecked(dictConfigId);
    }

    private List<DictDetail> loadDataFromDatabase(Long dictConfigId) {
        log.info("loadDataFromDatabase=========================");
        LambdaQueryWrapper<DictDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictDetail::getIsDeleted, 0).eq(DictDetail::getDictConfigId,dictConfigId);
        return dictDetailMapper.selectList(wrapper);
    }

    public void loadCacheData() {
        log.info("loadCacheData=========================");
        LambdaQueryWrapper<DictDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DictDetail::getIsDeleted, 0);
        List<DictDetail> dictConfigIds = dictDetailMapper.selectList(wrapper);
        for (DictDetail dictConfigId : dictConfigIds) {
            cache.getUnchecked(dictConfigId.getDictConfigId());
        }
    }

    public void refreshCache(Long dictConfigId) {
        log.info("refreshCache=========================");
        cache.refresh(dictConfigId);
    }

    public void scheduleCacheRefresh() {
        scheduler.scheduleAtFixedRate(() -> {
            log.info("Refreshing cache...");
            loadCacheData();
        }, 0, 2, TimeUnit.MINUTES);
    }
}
