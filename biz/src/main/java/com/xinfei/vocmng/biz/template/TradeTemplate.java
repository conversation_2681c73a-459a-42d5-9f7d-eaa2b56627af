/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.template;

import com.xinfei.vocmng.biz.log.DigestLogHolder;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrScenarioEnum;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.util.ExceptionUtil;
import com.xinfei.vocmng.biz.util.LoggerUtil;
import com.xinfei.xfframework.common.BaseResponse;
import org.springframework.dao.DataAccessException;

/**
 * 交易模版
 *
 * <AUTHOR>
 * @version $ TradeCallBack, v 0.1 2023/8/28 20:31 Chengsheng.Li Exp $
 */
public class TradeTemplate extends AbstractTemplate {

    /**
     * 场景码
     */
    private static final TechplayErrScenarioEnum ERR_SCENARIO = TechplayErrScenarioEnum.TRADE;

    /**
     * 查询模板
     *
     * @param response 查询结果
     * @param callBack 回调接口
     */
    public static void trade(BaseResponse response, TradeCallBack callBack) {

        try {

            callBack.checkParameter();
            callBack.doTrade();
            buildSuccessResponse(response);

        } catch (TechplayException e) {

            LoggerUtil.warn(LOGGER, "交易服务异常:", e);
            buildFailureResponse(response, ERR_SCENARIO, e);

        } catch (DataAccessException ex) {

            ExceptionUtil.error(ex, "交易服务出现数据库层异常:");
            buildFailureResponse(response, ERR_SCENARIO, TechplayErrDtlEnum.DB_EXCEPTION,
                    "交易服务出现数据库层异常");

        } catch (RuntimeException ex) {

            ExceptionUtil.error(ex, "交易服务出现未知异常:");
            buildFailureResponse(response, ERR_SCENARIO, TechplayErrDtlEnum.UNKNOWN_EXCEPTION,
                    "交易服务出现未知异常");

        } finally {

            DigestLogHolder.set(callBack.buildDigestLog());
        }
    }
}
