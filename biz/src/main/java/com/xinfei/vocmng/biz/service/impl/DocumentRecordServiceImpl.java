/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.service.DocumentRecordService;
import com.xinfei.vocmng.dal.mapper.DocumentRecordMapper;
import com.xinfei.vocmng.dal.po.DocumentRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DocumentRecordServiceImpl, v 0.1 2024/6/6 14:30 wancheng.qu Exp $
 */
@Service
public class DocumentRecordServiceImpl extends BaseService<DocumentRecordMapper, DocumentRecord> implements DocumentRecordService {

    @Override
    @Transactional
    public void saveBatchs(List<DocumentRecord> req) {
        saveBatch(req);
    }
}