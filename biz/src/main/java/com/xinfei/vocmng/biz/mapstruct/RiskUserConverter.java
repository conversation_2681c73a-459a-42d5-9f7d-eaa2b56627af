/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vocmng.biz.rr.dto.bill.RiskUserDto;
import com.xinfei.vocmng.dal.po.RiskUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR> 2024/7/11 上午10:45
 * RiskUserConverter
 */
@Mapper
public interface RiskUserConverter {

    RiskUserConverter INSTANCE = Mappers.getMapper(RiskUserConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }


    RiskUserDto riskUserToRiskUserDto(RiskUser riskUser);
}