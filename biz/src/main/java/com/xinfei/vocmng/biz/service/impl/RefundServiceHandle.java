/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.RefundApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.RefundApplyResponse;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ReqNoConstants;
import com.xinfei.vocmng.biz.mapstruct.RefundConverter;
import com.xinfei.vocmng.biz.model.base.BatchProcessResult;
import com.xinfei.vocmng.biz.model.enums.ControlEnum;
import com.xinfei.vocmng.biz.model.enums.FeeStrategyEnum;
import com.xinfei.vocmng.biz.model.enums.OrderType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.req.RefundApplyReq;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundProcessDto;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.RefundRequestMapper;
import com.xinfei.vocmng.dal.po.RefundRequest;
import com.xinfei.vocmng.itl.client.feign.impl.RefundFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.xinfei.vocmng.biz.constants.ReqNoConstants.getRequestNo;

/**
 * <AUTHOR>
 * @version $ RefundServiceHandle, v 0.1 2024-10-12 11:15 junjie.yan Exp $
 */
@Slf4j
@Component
public class RefundServiceHandle {

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private RefundFacadeClientImpl refundFacadeClient;

    @Resource
    private RefundRequestMapper refundRequestMapper;

    @Autowired
    private VocConfig vocConfig;

    @Resource
    private CommonService commonService;

    @Resource
    private OrderRefundStrategyServiceImpl orderRefundStrategyService;

    @Async("asyncTaskExecutor")
    public CompletableFuture<BatchProcessResult> refundApplyHandle(RefundApplyReq req, String creator, UserInfo userContext) {
        try {

            UserContextHolder.setUserContext(userContext);

            RefundRequest refundRequest = prepareRefundData(req);

            if (req.getStatus() != 0 && req.getStatus() != 1) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "审核状态只能是0或1");
            }

            if (req.getRefundCount() != null && req.getRefundCount() > 1 && (req.getExecuteType() != 1 || !"OFFLINE_REFUND".equals(req.getRefundType()))) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "存在退款次数时，executeType只能传1，且只能线下退款");
            }

            List<FeeSubjectEnum> reductionFeeList = getFeeSubjectEnums(req.getOrderType(), req.getLoanNo());
            refundRequest.setFeeSubjectEnum(JsonUtil.toJson(reductionFeeList));

            checkOfflineInfo(req, reductionFeeList);

            //需要审核申请单，直接落表
            if (req.getStatus() == 1) {
                return handleReviewedRefund(refundRequest);
            } else {
                RefundApplyRequest refundApplyRequest = RefundConverter.INSTANCE.refundApplyReqToRefundApplyRequest(req);
                refundApplyRequest.setRefundFeeList(reductionFeeList);
                refundApplyRequest.setCreatedBy(creator);
                return handleImmediateRefund(refundApplyRequest, refundRequest);
            }

        } catch (Exception e) {
            log.error("退款申请处理异常，订单号: {}", req.getOrderNo(), e);
            return CompletableFuture.completedFuture(
                    new BatchProcessResult(req.getOrderNo(), false, "系统错误： " + e.getMessage())
            );
        } finally {
            UserContextHolder.clearUserContext();
        }

//        //无需审核申请单，落表后直接调还款引擎退款接口
//        if (req.getStatus() == 0) {
//            RefundApplyRequest refundApplyRequest = RefundConverter.INSTANCE.refundApplyReqToRefundApplyRequest(req);
//            refundApplyRequest.setRefundFeeList(reductionFeeList);
//            refundApplyRequest.setCreatedBy(creator);
//            refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(refundRequest.getRefundTime()));
//            refundApplyRequest.setRefundReason(req.getRefundReason());
//            refundApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCRA, refundRequest.getId()));
//
//            if (req.getRefundCount() != null && req.getRefundCount() > 0) {
//                List<BigDecimal> refundAmtList = divide(refundApplyRequest.getTotalRefundAmt(), req.getRefundCount(), 2);
//                for (BigDecimal amt : refundAmtList) {
//                    refundApplyRequest.setTotalRefundAmt(amt);
//                    RefundApplyResponse response = refundFacadeClient.refundApply(refundApplyRequest);
//                    if (response != null) {
//                        refundRequest.setUpdateTime(LocalDateTime.now());
//                        refundRequest.setRefundOrderId(response.getRefundOrderId());
////                        refundRequestMapper.updateById(refundRequest);
//                    }
//                    refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(refundRequest.getRefundTime().plusDays(30)));
//                    refundRequest.setRefundTime(refundRequest.getRefundTime().plusDays(30));
//                    refundRequest.setRefundAmount(amt.multiply(new BigDecimal("100")).longValue());
//                    refundRequestMapper.insert(refundRequest);
//                }
//            }
//
////            RefundApplyResponse response = refundFacadeClient.refundApply(refundApplyRequest);
////            if (response != null) {
////                refundRequest.setUpdateTime(LocalDateTime.now());
////                refundRequest.setRefundOrderId(response.getRefundOrderId());
////                refundRequestMapper.updateById(refundRequest);
////            }
//        }
    }

    private RefundRequest prepareRefundData(RefundApplyReq req) {
        RefundRequest refundRequest = RefundConverter.INSTANCE.refundApplyReqToRefundRequest(req);
        getEncodeInfo(refundRequest);
        if (CollectionUtils.isNotEmpty(req.getRepaymentNos())) {
            refundRequest.setRepaymentNos(JsonUtil.toJson(req.getRepaymentNos()));
        }
        return refundRequest;
    }


    public CompletableFuture<BatchProcessResult> handleReviewedRefund(RefundRequest refundRequest) {
        refundRequestMapper.insert(refundRequest);
        return CompletableFuture.completedFuture(
                new BatchProcessResult(refundRequest.getOrderNo(), true, String.format("订单： %s 退款申请已提交，等待审核。", refundRequest.getOrderNo()))
        );
    }

    // 私有方法3：处理无需审核的退款
    public CompletableFuture<BatchProcessResult> handleImmediateRefund(RefundApplyRequest refundApplyRequest, RefundRequest baseRefundRequest) {

        try {
            // A. 计算分次列表
            List<BigDecimal> refundAmtList = new ArrayList<>();
            if (baseRefundRequest.getRefundCount() != null && baseRefundRequest.getRefundCount() > 0) {
                refundAmtList.addAll(divide(new BigDecimal(baseRefundRequest.getRefundAmount()).multiply(new BigDecimal("0.01")), baseRefundRequest.getRefundCount(), 2));
            } else {
                refundAmtList.add(new BigDecimal(baseRefundRequest.getRefundAmount()).multiply(new BigDecimal("0.01"))); // 如果不是分期，也统一为列表处理
            }

            // B. 在一个事务内，一次性创建所有待处理的退款记录
            List<RefundRequest> pendingRefunds = commonService.createPendingRefundsInTransaction(baseRefundRequest, refundAmtList);

            // C. 事务成功后，逐一调用外部接口并更新状态
            int successCount = 0;
            List<String> failureDetails = new ArrayList<>();
            for (RefundRequest pendingRefund : pendingRefunds) {
                try {
                    //更新退款请求的计划退款时间、退款金额、请求号
                    refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(pendingRefund.getRefundTime()));
                    refundApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCRA, pendingRefund.getId()));
                    refundApplyRequest.setTotalRefundAmt(new BigDecimal(pendingRefund.getRefundAmount()).multiply(new BigDecimal("0.01")));

                    // 调用外部接口
                    RefundApplyResponse response = refundFacadeClient.refundApply(refundApplyRequest);

                    // 根据结果更新状态（在新的短事务中）
                    if (response != null) {
                        updateRefundStatusAfterApiCall(pendingRefund.getId(), response);
                        successCount++;
                    } else {
                        updateRefundStatusToFailed(pendingRefund.getId());
                        failureDetails.add("还款引擎退款接口调用失败");
                    }
                } catch (Exception e) {
                    // 记录日志，并更新状态为失败（在新的短事务中）
                    log.error("Failed to process refund for ID: {}", pendingRefund.getId(), e);
                    updateRefundStatusToFailed(pendingRefund.getId());
                    failureDetails.add(e.getMessage());
                }
            }

            return CompletableFuture.completedFuture(buildFinalResult(baseRefundRequest.getOrderNo(), successCount, failureDetails));

        } catch (Exception e) {
            log.error("Critical error during immediate refund creation for orderNo: {}", baseRefundRequest.getOrderNo(), e);
            return CompletableFuture.completedFuture(
                    new BatchProcessResult(baseRefundRequest.getOrderNo(), false, "系统错误，申请退款失败: " + e.getMessage())
            );
        }
    }

    private BatchProcessResult buildFinalResult(String orderNo, int successCount, List<String> failureDetails) {
        if (failureDetails.isEmpty()) {
            return new BatchProcessResult(orderNo, true, String.format("订单： %s 全部 %d 次退款已成功提交。", orderNo, successCount));
        } else {
            String summary = String.format("订单： %s 处理完成，成功 %d 笔，失败 %d 笔。", orderNo, successCount, failureDetails.size());
            return new BatchProcessResult(orderNo, false, summary, failureDetails);
        }
    }

    // C部分：构建外部接口的请求
    private RefundApplyRequest buildRefundApplyRequest(RefundApplyReq originalReq, RefundRequest installment, String creator, List<FeeSubjectEnum> reductionFeeList) {
        RefundApplyRequest refundApplyRequest = RefundConverter.INSTANCE.refundApplyReqToRefundApplyRequest(originalReq);
        refundApplyRequest.setRefundFeeList(reductionFeeList);
        refundApplyRequest.setCreatedBy(creator);
        refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(installment.getRefundTime()));
        refundApplyRequest.setRefundReason(originalReq.getRefundReason());
        refundApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCRA, installment.getId()));
        refundApplyRequest.setTotalRefundAmt(new BigDecimal(installment.getRefundAmount()).multiply(new BigDecimal("0.01")));
        return refundApplyRequest; // 返回构建好的对象
    }

    // C部分：根据API调用结果更新状态
    public void updateRefundStatusAfterApiCall(Long refundId, RefundApplyResponse response) {
        RefundRequest updateRequest = new RefundRequest();
        updateRequest.setId(refundId);
        updateRequest.setUpdateTime(LocalDateTime.now());
        updateRequest.setRefundOrderId(response.getRefundOrderId());
        updateRequest.setRefundStatus("SUCCESS");
        refundRequestMapper.updateById(updateRequest);
    }

    // C部分：处理调用失败的情况
    public void updateRefundStatusToFailed(Long refundId) {
        RefundRequest updateRequest = new RefundRequest();
        updateRequest.setId(refundId);
        updateRequest.setRefundStatus("FAILED");
        refundRequestMapper.updateById(updateRequest);
    }

    /**
     * 将一个总数精确地分成指定份数，并将余数均匀分配。
     *
     * @param totalAmount 要分配的总数
     * @param divisor     要分成的份数
     * @param scale       结果保留的小数位数
     * @return 包含分配结果的列表，其总和精确等于 totalAmount
     */
    public static List<BigDecimal> divide(BigDecimal totalAmount, int divisor, int scale) {
        // 1. 输入校验
        if (divisor <= 0) {
            throw new IllegalArgumentException("Divisor must be greater than zero.");
        }

        // 2. 计算每一份的基础值（向下舍入）
        BigDecimal baseValue = totalAmount.divide(new BigDecimal(divisor), scale, RoundingMode.DOWN);

        // 3. 计算总余数
        BigDecimal totalOfBaseValues = baseValue.multiply(new BigDecimal(divisor));
        BigDecimal remainder = totalAmount.subtract(totalOfBaseValues);

//        // 4. 将余数转换为需要分配的最小单位数量
//        // 最小单位，例如 scale=2 时，就是 0.01
//        BigDecimal smallestUnit = new BigDecimal("1").scaleByPowerOfTen(-scale);
//        // 计算余数包含多少个最小单位
//        int remainderUnits = remainder.divide(smallestUnit).intValue();

        // 5. 初始化结果列表，所有值都为基础值
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < divisor; i++) {
            result.add(baseValue);
        }
        result.set(divisor - 1, result.get(divisor - 1).add(remainder));

//        // 6. 将余数（最小单位）逐一分配到前面的元素中
//        for (int i = 0; i < remainderUnits; i++) {
//            // result.get(divisor - 1) 返回的是一个 BigDecimal 对象，是不可变的。
//            // 所以需要用 add() 创建一个新对象，再用 set() 更新列表。
//            result.set(divisor - 1, result.get(divisor - 1).add(smallestUnit));
//        }

        return result;
    }

//    public static void main(String[] args) {
//        List<BigDecimal> result = divide(new BigDecimal("100.00"), 3, 2);
//        result.forEach(System.out::println);
//    }

    /**
     * 若试算明细包含线下退款，校验必填字段
     *
     * @param req
     * @param reductionFeeList
     */
    private void checkOfflineInfo(RefundApplyReq req, List<FeeSubjectEnum> reductionFeeList) {
        //待审核时，需要取所有费控进行试算

        if ("OFFLINE_REFUND".equals(req.getRefundType())) {
            if (StringUtils.isEmpty(req.getOfflineRefundMethod()) || StringUtils.isEmpty(req.getOfflineRefundAccount()) || StringUtils.isEmpty(req.getOfflineRefundUserName())) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "线下退款，请重新试算明细，需要填写退款类型、用户姓名、账号");
            }
            if ("BANK_CARD".equals(req.getOfflineRefundMethod()) && StringUtils.isEmpty(req.getOfflineRefundBank())) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "线下退款类型为银行卡时，需要填写银行名称");
            }
        }
    }

    /**
     * 退款可退费项
     *
     * @param orderType
     * @return
     */
    public List<FeeSubjectEnum> getFeeSubjectEnums(String orderType, String loanNo) {
        List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();

        if (vocConfig.getIsNewRefund() && OrderType.MAIN.getCode().equals(orderType)) {
            OrderRefundStrategyInput input = OrderRefundStrategyInput
                    .builder()
                    .loanNo(loanNo)
                    .build();
            StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = orderRefundStrategyService.executeStrategy(FeeStrategyEnum.ORDER_REFUND, input);
            Map<String, String> result = strategyExecutionResult.getResult();

            for (Map.Entry<String, String> entry : result.entrySet()) {
                String key = entry.getKey();
                BigDecimal value;
                try {
                    value = new BigDecimal(entry.getValue());
                } catch (NumberFormatException e) {
                    log.warn(LogUtil.infoLog("Error parsing BigDecimal for key " + key + ", value: " + entry.getValue()));
                    continue; // 跳过这个条目
                }

                if (value.compareTo(BigDecimal.ZERO) > 0 && OrderRefundProcessDto.NEW_FEE_MAPPING.get(key) != null) {
                    reductionFeeList.add(OrderRefundProcessDto.NEW_FEE_MAPPING.get(key));
                }
            }

        } else {

            List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();

            if (OrderType.PROFIT.getCode().equals(orderType)) {
                controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 3 && r.getControlChildType() == 8).collect(Collectors.toList());
            } else {
                controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 3 && r.getControlChildType() != 8).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(controlDataList)) {
                return reductionFeeList;
            }

            try {
                for (ControlData controlData : controlDataList) {
                    ControlEnum controlEnum = ControlEnum.getByControlChildType(controlData.getControlChildType());
                    switch (controlEnum) {
                        case GUARANTEE_REDUCTION:
                            reductionFeeList.add(FeeSubjectEnum.FEE1);
                            reductionFeeList.add(FeeSubjectEnum.FEE2);
                            break;
                        case PENALTY_REDUCTION:
                            reductionFeeList.add(FeeSubjectEnum.OINT_AMT);
                            reductionFeeList.add(FeeSubjectEnum.FEE3);
                            reductionFeeList.add(FeeSubjectEnum.FEE6);
                            break;
                        case PREPAYMENT_FEE:
                            reductionFeeList.add(FeeSubjectEnum.FEE4);
                            break;
                        case PRINCIPAL_AMOUNT:
                            reductionFeeList.add(FeeSubjectEnum.PRIN_AMT);
                            break;
                        case INTEREST_AMOUNT:
                            reductionFeeList.add(FeeSubjectEnum.INT_AMT);
                            break;
                        default:
                            break;
                    }
                }
            } catch (Exception e) {
                log.error(LogUtil.clientErrorLog("RefundServiceImp", "getFeeSubjectEnums", null, null, "现金订单可退款费项获取错误"), e);
                throw new TechplayException(TechplayErrDtlEnum.REFUND_ERROR);
            }
        }

        return reductionFeeList;
    }


    private void getEncodeInfo(RefundRequest refundRequest) {
        if (StringUtils.isNotEmpty(refundRequest.getOfflineRefundAccount())) {
            refundRequest.setOfflineRefundAccount(cisFacadeClientService.getEncodeByField("bankcard", Collections.singletonList(refundRequest.getOfflineRefundAccount())));
        }

        if (StringUtils.isNotEmpty(refundRequest.getOfflineRefundUserName())) {
            refundRequest.setOfflineRefundUserName(cisFacadeClientService.getEncodeByField("name", Collections.singletonList(refundRequest.getOfflineRefundUserName())));
        }
    }

}