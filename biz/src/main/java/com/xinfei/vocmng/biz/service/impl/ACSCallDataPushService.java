package com.xinfei.vocmng.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinfei.vocmng.biz.model.enums.AccountStatusEnum;
import com.xinfei.vocmng.biz.model.enums.GenderEnum;
import com.xinfei.vocmng.biz.model.enums.UdeskCallResultEnum;
import com.xinfei.vocmng.biz.model.enums.UdeskDropSideEnum;
import com.xinfei.vocmng.biz.rr.dto.CustomerDetailDto;
import com.xinfei.vocmng.biz.rr.dto.UserNoAppDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerRequest;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.util.IDCardUtil;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.dal.po.CommunicateSummaryRemark;
import com.xinfei.vocmng.dal.po.EmployeeDepartment;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.dal.po.RiskUser;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.itl.constants.MQConstants;
import com.xinfei.vocmng.itl.rr.AgentsResponse;
import com.xinfei.vocmng.itl.rr.ImCallLogResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsResponse;
import com.xinfei.vocmng.itl.rr.acsdatacore.ACSFollowData;
import com.xinfei.vocmng.itl.rr.acsdatacore.ACSPushData;
import com.xinfei.vocmng.itl.rr.acsdatacore.CallRecordListReq;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ ACSDataPushService, v 0.1 2025/3/5 17:08 shaohui.chen Exp $
 */
@Service
@Slf4j
public class ACSCallDataPushService extends AbstractDataPushService<ACSPushData, CallRecordListReq> {

    @Resource
    private UserLabelService userLabelService;

    @Override
    protected CallRecordListReq assemblePayload(ACSPushData data) {
        // 初始化 CallRecordListReq 并设置必填的固定字段
        CallRecordListReq req = new CallRecordListReq();
        req.setAppKey(vocConfig.getAcsAppKey());
        req.setSystemCode(vocConfig.getSystemCode());

        // 获取电话数据列表
        List<ImCallLogResponse> callLogList = data.getImCallLogResponseList();
        if (CollectionUtils.isEmpty(callLogList)) {
            req.setCallRecordList(Collections.emptyList());
            return req;
        }

        // 预先批量查询所有需要的数据
        BatchData batchData = prepareBatchData(callLogList);

        List<CallRecordListReq.TCallRecordRequest> recordList = new ArrayList<>();
        for (ImCallLogResponse callLog : callLogList) {
            if (Objects.isNull(callLog)) {
                continue;
            }
            CallRecordListReq.TCallRecordRequest record = new CallRecordListReq.TCallRecordRequest();
            // 呼叫ID：取自 Udesk 字段 call_id；若为空则默认 "-1"
            record.setCallId(callLog.getCallLogId() != null
                    ? String.valueOf(callLog.getCallLogId())
                    : "-1");
            // appKey
            record.setAppKey(vocConfig.getAcsAppKey());
            // 坐席电话号码：取自 trunk_number
            record.setExtPhoneNum(callLog.getTrunkNumber());

            AgentsResponse agent = batchData.getAgentMap().get(callLog.getAgentId());
            String encodeMobileLocal = (Objects.nonNull(agent) && StringUtils.isNotBlank(agent.getEncodeCellphone())) ? agent.getEncodeCellphone() : "";
            String agentId = Objects.nonNull(callLog.getAgentId()) ? String.valueOf(callLog.getAgentId()) : null;
            if (StringUtils.isNotBlank(encodeMobileLocal)) {
                Employee employee = batchData.getEmployeeMap().get(encodeMobileLocal);
                if (Objects.nonNull(employee)) {
                    agentId = String.valueOf(employee.getId());
                }
            }
            record.setAgentId(agentId);

            // 代理名称：取自 agent_nick_name（加密后）
            record.setAgentName(callLog.getAgentNickName());

            // 呼叫类型：根据 call_type 映射为整数
            if ("呼出".equals(callLog.getCallType())) {
                record.setCallType(1);
            } else if ("呼入".equals(callLog.getCallType())) {
                record.setCallType(2);
            } else {
                record.setCallType(0);
            }

            // 手机密文：取自 call_number
            record.setMobileCipher(callLog.getCallNumberCipher());

            // 呼叫开始时间：取自 call_start_at
            record.setCallStartTime(callLog.getCallStartAt());

            // 呼叫结束时间：取自 call_start_at + ring_time + call_time（秒）
            if (Objects.nonNull(callLog.getCallStartAt())
                    && Objects.nonNull(callLog.getRingTime())
                    && Objects.nonNull(callLog.getCallTime())) {
                record.setCallEndTime(callLog.getCallStartAt().plusSeconds(callLog.getRingTime() + callLog.getCallTime()));
            }

            // 用户响铃时长：取自 ring_time
            record.setTUserAlert(callLog.getRingTime());

            // 接通时长：取自 call_time（单位均为秒）
            record.setTConnect(callLog.getCallTime());

            // 接通时间：计算为 call_start_at + ring_time（秒）
            // 接通时间：只有在已接通（即 call_time 大于 0）时才设置接通时间
            if (Objects.nonNull(callLog.getCallTime()) && callLog.getCallTime() > 0
                    && Objects.nonNull(callLog.getCallStartAt()) && Objects.nonNull(callLog.getRingTime())) {
                record.setTConnectTime(callLog.getCallStartAt().plusSeconds(callLog.getRingTime()));
            }

            // 挂断方：使用 UdeskDropSideEnum 根据 UDesk 字段 drop_side 转换为我方 code
            if (StringUtils.isNotBlank(callLog.getDropSide())) {
                record.setDropSide(UdeskDropSideEnum.getMySideCode(callLog.getDropSide()));
            }

            // 呼叫结果：使用映射规则进行转换
            record.setCallResult(UdeskCallResultEnum.getCallResultCodeByUdeskDesc(callLog.getCallResult()));

            // 完成原因描述：取自 defeat_cause
            record.setFinishCauseDesc(callLog.getDefeatCause());

            // 媒体开始时间：取自 call_start_at
            record.setMediaStartTime(callLog.getCallStartAt());
            // 媒体结束时间：与呼叫结束时间一致
            record.setMediaStopTime(record.getCallEndTime());
            // 外呼方式，客服默认9
            record.setOutboundType(9);

            // 持续时长：用接通时长（秒）乘以 1000 得到毫秒
            if (Objects.nonNull(record.getTConnect())) {
                record.setDuration(record.getTConnect().longValue() * 1000L);
            }

            // 媒体文件访问地址：取自 record_url
            record.setMediaChannelUrl(callLog.getRecordUrl());
            // 将 call_id 映射到 bizId
            record.setBizId(callLog.getCallId());
            if (Objects.nonNull(callLog.getCallStartAt())) {
                record.setCreateDate(LocalDate.from(callLog.getCallStartAt()));
            }
            // 核心：设置 flowData（调用批量数据版本的方法）
            record.setFlowData(buildFlowData(callLog, batchData, record));

            record.setPhoneId(callLog.getCallLogId() != null
                    ? String.valueOf(callLog.getCallLogId())
                    : "-1");

            recordList.add(record);
        }
        req.setCallRecordList(recordList);
        return req;
    }

    @Override
    protected void sendData(CallRecordListReq payload) {
        if (Objects.nonNull(payload) && CollectionUtils.isNotEmpty(payload.getCallRecordList())) {
            log.info("send data to call record service, payload: {}", JsonUtil.toJson(payload));
            for (CallRecordListReq.TCallRecordRequest tCallRecordRequest : payload.getCallRecordList()) {
                CallRecordListReq callRecordListReq = new CallRecordListReq();
                callRecordListReq.setCallRecordList(Collections.singletonList(tCallRecordRequest));
                callRecordListReq.setAppKey(payload.getAppKey());
                callRecordListReq.setBizId(tCallRecordRequest.getCallId());
                callRecordListReq.setSystemCode(payload.getSystemCode());
                SendResult sendResult = mqTemplate.syncSend(MQConstants.ACS_DATA_CORE.TP_ACSDATACORE_CALL_RECORD_MSG_TOPIC, callRecordListReq);
                if (Objects.nonNull(sendResult) && Objects.equals(SendStatus.SEND_OK, sendResult.getSendStatus())) {
                    log.info("send data to call record service success, msgId:{}", sendResult.getMsgId());
                } else {
                    log.error("send data to call record service error, sendResult:{}", JsonUtil.toJson(sendResult));
                }
            }
        }
    }

    private String buildFlowData(ImCallLogResponse callLog, BatchData batchData, CallRecordListReq.TCallRecordRequest record) {
        ACSFollowData followData = new ACSFollowData();
        // =========================
        //   1) 用户基本信息（客服系统）
        // =========================
        String userName = StringUtils.EMPTY;  // 占位示例
        String userNo = StringUtils.EMPTY;     // 占位示例
        List<String> registerApp = new ArrayList<>();  // 多选示例
        List<String> accountStatus = new ArrayList<>(); // 多选示例
        String gender = StringUtils.EMPTY;
        List<String> userNoList = new ArrayList<>();
        List<UserNoAppDto> userNoAppDtos = customerService.getUserNoByMobileId(callLog.getCallNumberOrigin(), "", "");
        if (CollectionUtils.isNotEmpty(userNoAppDtos)) {
            UserNoAppDto currentUser = userNoAppDtos.get(0);
            userNo = StringUtils.isNotBlank(currentUser.getUserNo()) ? currentUser.getUserNo() : StringUtils.EMPTY;
            Set<String> apps = userNoAppDtos.stream()
                    .map(UserNoAppDto::getApp)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
                    
            Set<String> userNos = userNoAppDtos.stream()
                    .map(UserNoAppDto::getUserNo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(apps)) {
                registerApp.addAll(apps);
            }
            if (CollectionUtils.isNotEmpty(userNos)) {
                userNoList.addAll(userNos);
            }
            if (StringUtils.isNotBlank(userNo)) {
                CustomerDetailDto customerDetail = customerService
                        .getCustomerDetail(GetCustomerRequest.builder().userNo(userNo).build());
                if (Objects.nonNull(customerDetail)) {
                    gender = StringUtils.isNotBlank(customerDetail.getIdNoTrans()) ?
                            GenderEnum.fromCode(IDCardUtil.getGenderFromIdCard(customerDetail.getIdNoTrans())).getDescription() 
                            : StringUtils.EMPTY;
                    userName = StringUtils.isNotBlank(customerDetail.getName()) ? customerDetail.getName() : StringUtils.EMPTY;
                    if (Objects.nonNull(customerDetail.getStatus())) {
                        AccountStatusEnum accountStatusEnum = AccountStatusEnum.fromCode(customerDetail.getStatus());
                        if (Objects.nonNull(accountStatusEnum)) {
                            accountStatus.add(accountStatusEnum.getDescription());
                        }
                    }
                }
            }
        }

        followData.setUserName(userName);
        followData.setUserNo(userNo);
        followData.setRegisterApp(registerApp);
        followData.setAccountStatus(accountStatus);
        followData.setGender(gender);
        followData.setUserNoList(userNoList);

        // ---- 2) 客户打标（客服系统） ----
        List<String> userTagLabelList = new ArrayList<>();
        List<LabelDto> labelDtos = userLabelService.getUserLabel(userNo.toString());
        if (CollectionUtils.isNotEmpty(labelDtos)) {
            userTagLabelList.addAll(labelDtos.stream()
                    .map(LabelDto::getName)
                    .collect(Collectors.toList()));
        }
        followData.setUserTagLabelList(userTagLabelList);

        // ---- 3) 小结信息（客服系统） ----
        String callId = callLog.getCallId();
        String summaryKey = callLog.getCallNumberCipher() + "_" + callId;
        CommunicateSummary communicateSummary = batchData.getCommunicateSummaryMap().get(summaryKey);
        if (Objects.nonNull(communicateSummary)) {
            List<CommunicateSummaryRemark> remarks = Optional.ofNullable(communicateSummaryRemarkMapper.selectList(
                    new LambdaQueryWrapper<CommunicateSummaryRemark>()
                            .eq(CommunicateSummaryRemark::getCommunicateSummaryId, communicateSummary.getId())
            )).orElse(Collections.emptyList());
            List<Integer> incomingLineSourceList = Objects.nonNull(communicateSummary.getSource()) ? Collections.singletonList(communicateSummary.getSource()) : null;
            // 问题类型，一级、二级、三级
            List<String> questionClassificationOneLevelList =
                    Optional.ofNullable(communicateSummary.getIssueCategoryLv1())
                            .map(communicateSummaryService::getIss)
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList());

            List<String> questionClassificationTwoLevelList =
                    Optional.ofNullable(communicateSummary.getIssueCategoryLv2())
                            .map(communicateSummaryService::getIss)
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList());

            List<String> questionClassificationThreeLevelList =
                    Optional.ofNullable(communicateSummary.getIssueCategoryLv3())
                            .map(communicateSummaryService::getIss)
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList());
            List<String> remarkList = remarks.stream()
                    .map(CommunicateSummaryRemark::getRemark)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            Map<Long, Boolean> work = outerService.getWork(Collections.singletonList(communicateSummary.getId()));
            // 是否有工单
            Boolean createWorkOrderFlag = work.getOrDefault(communicateSummary.getId(), Boolean.FALSE);
            followData.setBriefSummaryId(communicateSummary.getId());
            followData.setIncomingLineSourceList(incomingLineSourceList);
            followData.setQuestionClassificationOneLevelStrList(questionClassificationOneLevelList);
            followData.setQuestionClassificationTwoLevelStrList(questionClassificationTwoLevelList);
            followData.setQuestionClassificationThreeLevelStrList(questionClassificationThreeLevelList);
            followData.setRemark(remarkList);
            followData.setCreateWorkOrderFlag(createWorkOrderFlag);
        }

        // ---- 4) 其他（客服需计算） ----
        if (StringUtils.isNotBlank(callLog.getCallNumberOrigin())) {
            String base64Phone = Base64.getEncoder().encodeToString(callLog.getCallNumberOrigin().getBytes(StandardCharsets.UTF_8));
            String customerDetailPageUrl = vocConfig.getCustomerDetailURL() + base64Phone;
            String workOrderDetailUrl = vocConfig.getWorkOrderDetailURL() + base64Phone;
            followData.setCustomerDetailPageUrl(customerDetailPageUrl);
            followData.setWorkOrderListPageUrl(workOrderDetailUrl);
        }

        // ---- 5) 坐席信息（客服系统） ----
        AgentsResponse agent = batchData.getAgentMap().get(callLog.getAgentId());
        followData.setUDeskAgentId(callLog.getAgentId());
        String encodeMobileLocal = (Objects.nonNull(agent) && StringUtils.isNotBlank(agent.getEncodeCellphone())) ? agent.getEncodeCellphone() : "";

        // 设置坐席名称
        String seatsName = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(encodeMobileLocal)) {
            Employee employee = batchData.getEmployeeMap().get(encodeMobileLocal);
            if (Objects.nonNull(employee) && StringUtils.isNotBlank(employee.getName())) {
                seatsName = employee.getName();
            }
        }
        followData.setSeatsName(seatsName);

        // 设置部门信息
        if (StringUtils.isNotBlank(encodeMobileLocal)) {
            EmployeeDepartment dept = batchData.getDeptMap().get(encodeMobileLocal);
            if (Objects.nonNull(dept)) {
                String seatsOrganId = String.valueOf(dept.getDepartmentId());
                String seatsOrganName = dept.getDepartmentName();
                followData.setSeatsOrganId(seatsOrganId);
                followData.setSeatsOrganName(seatsOrganName);
                record.setOrgName(seatsOrganName);
            }
        }


        // ---- 6) Udesk 公共数据（从 Udesk 拉取） ----
        List<ImUserGroupsResponse> allUserGroups = udeskClientService.getAllUserGroups();
        List<String> seatesGroupList = new ArrayList<>();
        List<String> userTagUdeskLabelList = new ArrayList<>();
        Long currentAgentId = callLog.getAgentId();
        if (Objects.nonNull(currentAgentId) && CollectionUtils.isNotEmpty(allUserGroups)) {
            for (ImUserGroupsResponse group : allUserGroups) {
                if (CollectionUtils.isNotEmpty(group.getAgents())) {
                    for (ImUserGroupsResponse.Agents agent1 : group.getAgents()) {
                        if (currentAgentId.equals(agent1.getId())) {
                            seatesGroupList.add(group.getName());
                            break; // 找到一个组后即可退出该组内循环
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(callLog.getCallNumberOrigin())) {
            ImCustomerDetailsQueryResponse imCustomerDetailsQueryResponse = udeskClientService.getCustomerDetailsWithOutException("cellphone", callLog.getCallNumberOrigin());
            if (Objects.nonNull(imCustomerDetailsQueryResponse)
                    && Objects.nonNull(imCustomerDetailsQueryResponse.getCustomer())
                    && CollectionUtils.isNotEmpty(imCustomerDetailsQueryResponse.getCustomer().getTags())) {
                userTagUdeskLabelList.addAll(imCustomerDetailsQueryResponse.getCustomer()
                        .getTags()
                        .stream()
                        .map(ImCustomerDetailsResponse.Tag::getName)
                        .collect(Collectors.toList()));
            }
        }
        followData.setSeatsGroupList(seatesGroupList);
        followData.setUserTagUdeskLabelList(userTagUdeskLabelList);

        // ---- 7) Udesk-电话（人工） ----
        followData.setUDeskCallId(callLog.getCallId());
        followData.setUDeskCallType(callLog.getCallType());
        followData.setUDeskCallHungUper(callLog.getDropSide());
        followData.setUDeskCallSatisfactionEvaluationList(StringUtils.isNotBlank(callLog.getSurvey()) ? Collections.singletonList(callLog.getSurvey()) : Collections.emptyList());
        followData.setUDeskCallKeepTime(callLog.getHoldDuration());
        followData.setUDeskCallRingTime(callLog.getRingTime());
        followData.setUDeskCallDuration(callLog.getCallTime());

        // ---- 8) 催收相关数据（催收系统） ----
        // 暂不处理催收数据，可后续扩展

        return JsonUtil.toJson(followData);
    }

    /**
     * 批量预加载各类数据，避免在循环中重复查询外部服务
     */
    private BatchData prepareBatchData(List<ImCallLogResponse> callLogList) {
        BatchData batchData = new BatchData();
        // 1. 收集所有加密手机号 & agentId
        List<String> mobileList = callLogList.stream()
                .filter(Objects::nonNull)
                .map(ImCallLogResponse::getCallNumberCipher)
                .distinct()
                .collect(Collectors.toList());
        List<Long> agentIdList = callLogList.stream()
                .filter(Objects::nonNull)
                .map(ImCallLogResponse::getAgentId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 2. 批量查询风险用户（按手机号）
        List<RiskUser> riskUserList = riskUserMapper.selectList(
                new LambdaQueryWrapper<RiskUser>()
                        .in(RiskUser::getMobile, mobileList)
                        .eq(RiskUser::getIsDeleted, 0));
        Map<String, RiskUser> riskUserMap = riskUserList.stream()
                .collect(Collectors.toMap(RiskUser::getMobile, ru -> ru, (a, b) -> a));
        batchData.setRiskUserMap(riskUserMap);

        // 3. 批量查询 CommunicateSummary（按手机号），同一用户进线咨询投诉一年2～3次，暂时不加callId，不影响数据库性能
        List<CommunicateSummary> summaryList = communicateSummaryMapper.selectList(
                new LambdaQueryWrapper<CommunicateSummary>()
                        .in(CommunicateSummary::getTelephoneEncrypted, mobileList)
        );
        // 用 key = telephoneEncrypted + "_" + callId
        Map<String, CommunicateSummary> summaryMap = new LinkedHashMap<>();
        for (CommunicateSummary cs : summaryList) {
            String key = cs.getTelephoneEncrypted() + "_" + cs.getCallId();
            summaryMap.put(key, cs);
        }
        batchData.setCommunicateSummaryMap(summaryMap);

        // 4. 批量查询坐席信息（agent），保存到 Map<agentId, AgentsResponse>
        Map<Long, AgentsResponse> agentMap = new LinkedHashMap<>();
        for (Long agentId : agentIdList) {
            Map<Integer, AgentsResponse> agentsMap = udeskClientService.getAgentsMap();
            AgentsResponse agent = agentsMap.get(agentId.intValue());
            String encodeMobileLocal = "";
            if (Objects.nonNull(agent) && StringUtils.isNotBlank(agent.getCellphone())) {
                encodeMobileLocal = cisFacadeClientService.getEncodeMobileLocal(agent.getCellphone());
                agent.setEncodeCellphone(encodeMobileLocal);
            }
            agentMap.put(agentId, agent);
        }
        batchData.setAgentMap(agentMap);

        // 5. 批量查询部门信息：先收集所有需要的 encodeMobile（从 agent.cellphone）
        List<String> encodeMobileList = agentMap.values().stream()
                .filter(Objects::nonNull)
                .map(agent -> agent.getEncodeCellphone())  // 直接从 agent 获取 encodeCellphone 字段
                .filter(StringUtils::isNotBlank)  // 过滤空字符串
                .distinct()  // 去重
                .collect(Collectors.toList());
        List<EmployeeDepartment> deptList = departmentMapper.getEmployeeDepartmentByUserEncodePhones(encodeMobileList);
        // 假设 EmployeeDepartment 中有一个 getEncodeMobile() 方法返回编码后的手机号
        Map<String, EmployeeDepartment> deptMap = deptList.stream()
                .collect(Collectors.toMap(EmployeeDepartment::getEncodeMobile, ed -> ed, (a, b) -> a));
        batchData.setDeptMap(deptMap);

        // 5. 批量查询客服信息
        List<Employee> employeeList = employeeMapper.getEmployeeByUserEncodePhones(encodeMobileList);
        Map<String, Employee> employeeMap = employeeList.stream()
                .collect(Collectors.toMap(Employee::getMobileEncrypted, ed -> ed, (a, b) -> a));
        batchData.setEmployeeMap(employeeMap);

        return batchData;
    }

    // ================= BatchData 内部类 =================
    @Data
    private static class BatchData {
        /**
         * 高风险用户标签
         */
        private Map<String, RiskUser> riskUserMap;
        /**
         * 小结
         */
        private Map<String, CommunicateSummary> communicateSummaryMap;
        /**
         * 客服坐席
         */
        private Map<Long, AgentsResponse> agentMap;
        /**
         * 用户部门
         */
        private Map<String, EmployeeDepartment> deptMap;
        /**
         * 客服信息
         */
        private Map<String, Employee> employeeMap;

    }
}
