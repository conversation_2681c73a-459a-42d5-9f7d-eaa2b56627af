/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.reflect.TypeToken;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.dal.mapper.CrowdLabelMapper;
import com.xinfei.vocmng.dal.mapper.EasyComplaintUserMapper;
import com.xinfei.vocmng.dal.mapper.LabelConfigMapper;
import com.xinfei.vocmng.dal.mapper.SettleUserMapper;
import com.xinfei.vocmng.dal.mapper.StopUrgingLabelMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.CrowdLabel;
import com.xinfei.vocmng.dal.po.EasyComplaintUser;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.dal.po.SettleUser;
import com.xinfei.vocmng.dal.po.StopUrgingLabel;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.FeaturePlatformClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.FeatureQueryClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.FeatureQueryReq;
import com.xinfei.vocmng.itl.rr.RealCollectReq;
import com.xinfei.vocmng.itl.rr.dto.InputParamsDto;
import com.xinfei.vocmng.itl.rr.dto.ObjType;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ UserLabelServiceImpl, v 0.1 2025/07/02 15:01 pengming.liu Exp $
 */

@Slf4j
@Service
public class UserLabelServiceImpl implements UserLabelService {

    @Resource
    private SettleUserMapper settleUserMapper;

    @Resource
    private LabelConfigMapper labelConfigMapper;

    @Resource
    private EasyComplaintUserMapper easyComplaintUserMapper;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private FeatureQueryClientImpl featureQueryClientImpl;

    @Resource
    private FeaturePlatformClientImpl featurePlatformClient;

    @Resource
    private VocConfig vocConfig;

    @Resource
    private LabelSolutionsService labelSolutionsService;

    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;

    @Resource
    private StopUrgingLabelMapper stopUrgingLabelMapper;

    @Resource
    private CrowdLabelMapper crowdLabelMapper;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final Gson gson = new Gson();
    private final Type typeMapString = new TypeToken<Map<String, String>>(){}.getType();

    /**
     * 获取用户标签
     *
     * @param userNo
     * @return
     */
    @Override
    public List<LabelDto> getUserLabel(String userNo) {
        String custNo ="";
        String enCodeTel="";
        String mobile="";
        if (StringUtils.isNotBlank(userNo)) {
            PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, userNo, 1, 10);
            if (CollectionUtils.isEmpty(pageResult.getList())) {
                return new ArrayList<>();
            }

            UserSearchDTO userSearch = pageResult.getList().get(0);
            enCodeTel = cisFacadeClientService.getEncodeMobileLocal(userSearch.getMobile());
            mobile = userSearch.getMobile();
            custNo = userSearch.getCustNo();
        }

        List<LabelDto> externalLabels = getLabel(mobile);
        try {
            //获取洞察标签映射
            Map<String, String> map = gson.fromJson(vocConfig.AUTO_WORK_LABEL_CROWDID_MAP, typeMapString);
            if (!StringUtils.isEmpty(userNo)) {
                //洞察平台标签
                for (Map.Entry<String, String> e : map.entrySet()) {
                    CrowdLabel crowdLabel = crowdLabelMapper.getCrowdUserNoLabel(userNo, e.getValue());
                    if (crowdLabel != null && crowdLabel.getIsDeleted() == 0 && "groupA".equals(crowdLabel.getUserGroup())) {
                        LabelDto labelConfig = labelConfigMapper.getLabelConfig(crowdLabel.getLabelName());
                        if (labelConfig != null) {
                            externalLabels.add(labelConfig);
                        }
                    }
                }

            }
            //客服是否为结清用户
            if (!StringUtils.isEmpty(enCodeTel)) {
                SettleUser settleUser = settleUserMapper.getSettleUser(enCodeTel);
                if (settleUser != null) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("结清风险");
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }
            }

            //疑似黑产
            if (!StringUtils.isEmpty(mobile)) {
                String reason = getCode(mobile, "phone_is_black_industry_hlevel_type");
                if (!StringUtils.isEmpty(reason) && !reason.equals("n")) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("疑似黑产");
                    String score = getCode(mobile, "phone_black_industry_score");
                    if (!StringUtils.isEmpty(score)) {
                        labelConfig.setSolution(labelSolutionsService.getSolution("黑产类型", "疑似黑产", Double.parseDouble(score)));
                    }
                    externalLabels.add(labelConfig);
                }
            }

            //停催标签
            if (!StringUtils.isEmpty(custNo)) {
                StopUrgingLabel stopUrgingLabel = stopUrgingLabelMapper.getStopUrgingLabel(custNo);
                if (stopUrgingLabel != null) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig(stopUrgingLabel.getLabelName());
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }

                EasyComplaintUser easyComplaintUser = easyComplaintUserMapper.getEasyComplaintUser(custNo);
                if (easyComplaintUser != null && "groupA".equals(easyComplaintUser.getUserGroup())) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("易投诉");
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }

                //洞察平台标签
                for (Map.Entry<String, String> e : map.entrySet()) {
                    CrowdLabel crowdLabel = crowdLabelMapper.getCrowdCustNoLabel(custNo, e.getValue());
                    if (crowdLabel != null && crowdLabel.getIsDeleted() == 0 && "groupA".equals(crowdLabel.getUserGroup())) {
                        LabelDto labelConfig = labelConfigMapper.getLabelConfig(crowdLabel.getLabelName());
                        if (labelConfig != null) {
                            externalLabels.add(labelConfig);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getUserLabel warn,enCodeTel:{} userNo:{} enCodeTel:{} mobile:{}", enCodeTel, userNo, mobile);
        }

        return externalLabels;
    }

    public String getCode(String tel, String varCode) {
        String encodeTel = cisFacadeClientService.getEncodeMobileLocal(tel);
        if (StringUtils.isNotEmpty(encodeTel) && !"T".equals(vocConfig.getIsFeatureQuery())) {
            RealCollectReq realCollectReq = new RealCollectReq();
            realCollectReq.setVarCodes(Collections.singletonList(varCode));
            Map<String, Object> map = new HashMap<>();
            map.put("mobile", encodeTel);
            map.put("timestamp", LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            realCollectReq.setInputParams(map);
            String code = featurePlatformClient.accountInfo(realCollectReq);
            return getValue(code, varCode);
        }

        if (StringUtils.isNotEmpty(encodeTel) && "T".equals(vocConfig.getIsFeatureQuery())) {
            FeatureQueryReq req = new FeatureQueryReq();
            InputParamsDto inputParamsDto = new InputParamsDto();
            inputParamsDto.setFeatureParams(Collections.singletonMap("mobile", new ObjType(encodeTel, "StrValue")));
            inputParamsDto.setFeatureCode(Collections.singletonList(varCode));

            req.setInputParams(inputParamsDto);

            Map<String, ObjType> map = featureQueryClientImpl.featureQuery(req);
            if (map != null && map.get(varCode) != null) {
                return map.get(varCode).getObj();
            }
        }

        return "";
    }

    public static String getValue(String jsonStr, String key) {
        JsonNode rootNode = null;
        try {
            if (ObjectUtil.isNotEmpty(jsonStr)) {
                rootNode = OBJECT_MAPPER.readTree(jsonStr);
            }
        } catch (JsonProcessingException e) {
            log.error("JsonUtil.getValue.error", e);
        }
        if (ObjectUtil.isNotNull(rootNode) && ObjectUtil.isNotNull(rootNode.get(key))) {
            return rootNode.get(key).asText();
        }
        return "";
    }

    private List<LabelDto> getLabel(String mobile) {
        List<LabelDto> labelDtos = new ArrayList<>();
        try {
            if (vocConfig.isWysFlag()) {
                labelDtos = userLabelMappingMapper.getLabelsEncrypted(cisFacadeClientService.getEncodeMobileLocal(mobile));
            } else {
                labelDtos = userLabelMappingMapper.getLabels(mobile);
            }
        } catch (Exception e) {
            log.error("getLabels error, msg:{}, mobile:{},", e, mobile);
        }
        return labelDtos;
    }
}