package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Getter;

/**
 * 外部事件类型枚举
 * 用于标识从外部系统传入的工单事件类型
 *
 * <AUTHOR>
 * @version $ ExternalEventType, v 0.1 2025/08/13 ExternalEventType Exp $
 */
@Getter
public enum ExternalEventType {

    /**
     * 工单创建事件
     */
    CREATED("CREATED", "工单创建"),

    /**
     * 反馈事件（处理过程中）
     */
    FEEDBACK("FEEDBACK", "反馈事件"),

    /**
     * 关闭事件（工单完结）
     */
    CLOSED("CLOSED", "关闭事件");

    private final String code;
    private final String description;

    ExternalEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 事件类型代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static ExternalEventType fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (ExternalEventType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的外部事件类型: " + code);
    }

    /**
     * 判断是否为需要处理的事件类型
     * 只有FEEDBACK和CLOSED事件需要推送到海尔消金
     *
     * @return true表示需要处理，false表示不需要处理
     */
    public boolean shouldProcess() {
        return this == FEEDBACK || this == CLOSED;
    }

    /**
     * 判断是否为处理过程中的事件
     *
     * @return true表示是处理过程中，false表示是完结状态
     */
    public boolean isProcessing() {
        return this == FEEDBACK;
    }

    /**
     * 判断是否为完结事件
     *
     * @return true表示是完结事件，false表示不是
     */
    public boolean isClosed() {
        return this == CLOSED;
    }
}
