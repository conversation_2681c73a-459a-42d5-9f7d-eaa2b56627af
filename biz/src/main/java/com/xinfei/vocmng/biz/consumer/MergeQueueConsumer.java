package com.xinfei.vocmng.biz.consumer;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.service.MergeQueueService;
import com.xinfei.vocmng.biz.service.MergeTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 合并任务队列消费者
 * 使用定时任务从Redis队列中获取并处理任务
 *
 * <AUTHOR>
 * @version $ MergeQueueConsumer, v 0.1 2025/5/1 $
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MergeQueueConsumer extends RepaymentTaskManage {

    private final MergeQueueService mergeQueueService;
    private final MergeTaskHandler mergeTaskHandler;
    private final MergeTaskService mergeTaskService;

    @Override
    protected String getCron() {
        return "* * * * * ?";
    }

    @Override
    protected void processTask() {
        if (!ApolloConstant.mergeQueueEnabled) {
            return;
        }
        try {
            // 从主队列获取任务
            String webToken = mergeQueueService.popTask();

            if (StringUtils.isBlank(webToken)) {
                return; // 队列为空，直接返回
            }

            log.info("Consuming task from main queue: webToken={}", webToken);

            try {
                // 处理任务
                mergeTaskHandler.handleTask(webToken);

                // 标记任务完成
                mergeTaskService.markTaskComplete(webToken);

                log.info("Successfully processed task: webToken={}", webToken);
            } catch (Exception e) {
                // 处理失败，添加到重试队列
                log.error("Failed to process task: webToken={}", webToken, e);
                mergeQueueService.addToRetryQueue(webToken);
            }
        } catch (Exception e) {
            log.error("Error in consumeMainQueue", e);
        }
    }
}
