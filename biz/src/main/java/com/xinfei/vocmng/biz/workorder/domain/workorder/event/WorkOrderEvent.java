package com.xinfei.vocmng.biz.workorder.domain.workorder.event;

import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.util.util.IDGeneratorUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 工单领域事件
 *
 * <AUTHOR>
 * @version $ WorkOrderEvent, v 0.1 2025/07/30 WorkOrderEvent Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderEvent {

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 资方编码
     */
    private String funderCode;

    /**
     * 事件类型
     */
    private WorkOrderEventType eventType;

    /**
     * 事件数据
     */
    @Builder.Default
    private Map<String, Object> eventData = new HashMap<>();

    /**
     * 事件发生时间
     */
    private String occurTime;


    /**
     * 创建状态变更事件
     */
    public static WorkOrderEvent statusChangedEventWithExtendedFields(WorkOrderEntity workOrder,
                                                                     WorkOrderStatus oldStatus,
                                                                     WorkOrderStatus newStatus,
                                                                     String reason,
                                                                     String externalWorkOrderStatus,
                                                                     String processTime,
                                                                     String customerStatus,
                                                                     String eventType) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("oldStatus", oldStatus.getCode());
        eventData.put("newStatus", newStatus.getCode());
        eventData.put("reason", reason);
        eventData.put("originalWorkOrderId", workOrder.getOriginalWorkOrderId());
        eventData.put("callId", workOrder.getCallId());
        eventData.put("udeskCallId", workOrder.getUdeskCallId());
        eventData.put("customerPhone", workOrder.getCustomerPhone());
        // 工单状态更新扩展字段
        eventData.put("externalWorkOrderStatus", externalWorkOrderStatus);
        eventData.put("processTime", processTime);
        eventData.put("customerStatus", customerStatus);
        eventData.put("eventType", eventType);
        // 转接相关字段
        eventData.put("transferTime", LocalDateTimeUtils.format(workOrder.getTransferTime()));
        eventData.put("transferResult", workOrder.getTransferResult());

        return WorkOrderEvent.builder()
                .eventId(generateEventId())
                .workOrderId(workOrder.getId())
                .orderNo(workOrder.getOrderNo())
                .funderCode(workOrder.getFunderCode())
                .eventType(WorkOrderEventType.STATUS_CHANGED)
                .eventData(eventData)
                .occurTime(LocalDateTimeUtils.format(LocalDateTime.now()))
                .build();
    }

    /**
     * 创建工单创建事件
     */
    public static WorkOrderEvent createdEvent(WorkOrderEntity workOrder) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("callId", workOrder.getCallId());
        eventData.put("udeskCallId", workOrder.getUdeskCallId());
        eventData.put("customerPhone", workOrder.getCustomerPhone());
        eventData.put("agentId", workOrder.getAgentId());
        eventData.put("summaryId", workOrder.getSummaryId());
        // 转接相关字段
        eventData.put("transferTime", LocalDateTimeUtils.format(workOrder.getTransferTime()));
        eventData.put("transferResult", workOrder.getTransferResult());

        return WorkOrderEvent.builder()
                .eventId(generateEventId())
                .workOrderId(workOrder.getId())
                .orderNo(workOrder.getOrderNo())
                .funderCode(workOrder.getFunderCode())
                .eventType(WorkOrderEventType.CREATED)
                .eventData(eventData)
                .occurTime(LocalDateTimeUtils.format(LocalDateTime.now()))
                .build();
    }

    /**
     * 生成事件ID
     */
    public static String generateEventId() {
        return IDGeneratorUtil.genBase64Id("WOE_");
    }

    /**
     * 添加事件数据
     */
    public void addEventData(String key, Object value) {
        if (Objects.isNull(eventData)) {
            eventData = new HashMap<>();
        }
        eventData.put(key, value);
    }

    /**
     * 获取事件数据
     */
    public Object getEventData(String key) {
        return Objects.nonNull(eventData) ? eventData.get(key) : null;
    }
}
