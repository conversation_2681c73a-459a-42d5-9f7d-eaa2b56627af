/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.repaytrade.facade.rr.dto.FeeAmountDto;
import com.xinfei.repaytrade.facade.rr.dto.PlanDetailInfo;
import com.xinfei.repaytrade.facade.rr.dto.ReductionInfoDto;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.live.LiveRecordResponse;
import com.xinfei.vocmng.biz.rr.dto.BankFlow;
import com.xinfei.vocmng.biz.rr.dto.HuttaPlanDetailDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentPlanDto;
import com.xinfei.vocmng.biz.rr.request.BankFlowRequest;
import com.xinfei.vocmng.biz.rr.request.FeeAmountDtoResp;
import com.xinfei.vocmng.biz.rr.response.DeductionLoansResponse;
import com.xinfei.vocmng.biz.rr.response.RepaymentPlanDetailDto;
import com.xinfei.vocmng.biz.rr.response.WriteOffRecord;
import com.xinfei.vocmng.dal.po.RepaymentPlan;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.rr.RefundRecordQueryReq;
import com.xinfei.vocmng.itl.rr.RefundRecordQueryRes;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface RepayConverter {

    RepayConverter INSTANCE = Mappers.getMapper(RepayConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    @Mapping(source = "id", target = "planDetailId")
    @Mapping(source = "term", target = "terms")
    @Mapping(source = "prinReduct", target = "prinAmt")
    @Mapping(source = "intReduct", target = "intAmt")
    @Mapping(source = "lateReduct", target = "overdueAmt")
    @Mapping(source = "guarantReduct", target = "guaranteeAmt")
    @Mapping(source = "redReduct", target = "earlySettleAmt")
    @Mapping(source = "deduction", target = "reductionAmt")
    PlanDetailInfo planDetailToPlanDetailInfo(RepaymentPlanDetail repaymentPlanDetail);

    RepaymentPlanDto repaymentPlanToRepaymentPlanDto(RepaymentPlan repaymentPlan);

    @Mapping(source = "term", target = "terms", qualifiedByName = "getTerms")
    RepaymentPlanDetailDto planDetailToPlanDetailDto(RepaymentPlanDetail repaymentPlanDetail);

    @Named("getTerms")
    static List<String> getTerms(String term) {
        if (StringUtils.isNotEmpty(term)) {
            return Arrays.stream(term.split(",")).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    RefundRecordQueryReq bankFlowRequestToRefundRecordQuery(BankFlowRequest bankFlowRequest);

    BankFlow refundRecordToBankFlow(RefundRecordQueryRes refundRecordQueryRes);

    @AfterMapping
    default void setBankFlowAmt(RefundRecordQueryRes refundRecordQueryRes, @MappingTarget BankFlow bankFlow) {
        bankFlow.setFlowAmount(bankFlow.getFlowAmount().multiply(new BigDecimal("0.01")));
        bankFlow.setCancellableAmount(bankFlow.getCancellableAmount().multiply(new BigDecimal("0.01")));
        bankFlow.setCancelledAmount(bankFlow.getCancelledAmount().multiply(new BigDecimal("0.01")));
        bankFlow.setRefundAmount(bankFlow.getRefundAmount().multiply(new BigDecimal("0.01")));
    }

    WriteOffRecord liveRecordToWriteOffRecords(LiveRecordResponse liveRecordResponse);

    DeductionLoansResponse reductionInfoToDeductionResponse(ReductionInfoDto reductionInfo);

    @Mapping(source = "dateExpire", target = "endTime")
    HuttaPlanDetailDto planDeductToHuttaPlanDetailDto(QueryRepaymentPlanResponse.PlanDeduct planDeduct);

    FeeAmountDtoResp feeAmountDtoToFeeAmountDtoResp(FeeAmountDto feeAmountDto);
}