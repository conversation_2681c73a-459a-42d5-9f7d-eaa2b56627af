/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.vocmng.biz.model.resp.FeeStrategyConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryConverter, v 0.1 2024-02-21 11:19 junjie.yan Exp $
 */
@Mapper
public interface FeeStrategyConverter {

    FeeStrategyConverter INSTANCE = Mappers.getMapper(FeeStrategyConverter.class);

    FeeStrategyConfig getFeeStrategyConfig(com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig feeStrategyConfig);

}