package com.xinfei.vocmng.biz.workorder.domain.workorder.aggregate;

import com.xinfei.vocmng.biz.constants.HaierComplaintConstants;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderDataEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.entity.WorkOrderEntity;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExternalEventType;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工单聚合根
 * 管理工单完整生命周期和业务规则
 *
 * <AUTHOR>
 * @version $ WorkOrderAggregate, v 0.1 2025/07/30 WorkOrderAggregate Exp $
 */
@Data
@Slf4j
public class WorkOrderAggregate {

    /**
     * 工单实体
     */
    private WorkOrderEntity workOrder;

    /**
     * 工单数据实体
     */
    private WorkOrderDataEntity workOrderData;

    /**
     * 领域事件列表
     */
    private List<WorkOrderEvent> domainEvents = new ArrayList<>();



    /**
     * 更新工单状态
     */
    public void updateStatus(WorkOrderStatus newStatus, String reason,
                            String externalWorkOrderStatus, String processTime,
                            String customerStatus, String eventType) {
        WorkOrderStatus oldStatus = this.workOrder.getStatus();

        // 验证状态转换是否合法
        validateStatusTransition(oldStatus, newStatus);

        // 更新状态
        this.workOrder.setStatus(newStatus);
        this.workOrder.setUpdateTime(LocalDateTime.now());
        this.workOrder.setVersion(this.workOrder.getVersion() + 1);

        // 根据eventType决定发布什么类型的事件
        if (Objects.equals(ExternalEventType.CREATED.getCode(), eventType)) {
            // 发布工单创建事件
            publishEvent(WorkOrderEvent.createdEvent(this.workOrder));
        } else {
            // 发布状态变更事件
            publishEvent(WorkOrderEvent.statusChangedEventWithExtendedFields(
                    this.workOrder, oldStatus, newStatus, reason,
                    externalWorkOrderStatus, processTime, customerStatus, eventType));
        }

        log.info("工单状态更新成功, orderNo: {}, {} -> {}, eventType: {}",
                this.workOrder.getOrderNo(), oldStatus, newStatus, eventType);
    }

    /**
     * 验证工单数据
     */
    public void validate() {
        if (Objects.isNull(this.workOrder)) {
            throw new IllegalStateException("工单实体不能为空");
        }
        
        this.workOrder.validate();
        
    }

    /**
     * 发布领域事件
     */
    public void publishEvent(WorkOrderEvent event) {
        this.domainEvents.add(event);
    }

    /**
     * 处理IVR进线事件
     */
    public void handleIvrIncoming(String callId, String udeskCallId, String customerPhone, String funderCode) {
        if (StringUtils.isBlank(callId)) {
            throw new IllegalArgumentException("通话ID不能为空");
        }

        if (StringUtils.isBlank(funderCode)) {
            throw new IllegalArgumentException("资方编码不能为空");
        }

        // 更新工单信息
        this.workOrder.setCallId(callId);
        this.workOrder.setUdeskCallId(udeskCallId);
        this.workOrder.setCustomerPhone(customerPhone);
        this.workOrder.setEventType(WorkOrderEventType.IVR_INCOMING.getCode());
        this.workOrder.setStatus(WorkOrderStatus.PENDING);
        this.workOrder.setUpdateTime(LocalDateTime.now());

        log.info("处理IVR进线事件成功, callId: {}, udeskCallId: {}, funderCode: {}",
                callId, udeskCallId, funderCode);
    }

    /**
     * 处理接线成功事件
     */
    public void handleCallConnected(String agentId, Long summaryId, String connectTime) {
        if (StringUtils.isBlank(agentId)) {
            throw new IllegalArgumentException("客服ID不能为空");
        }

        // 更新工单信息
        this.workOrder.setAgentId(agentId);
        this.workOrder.setSummaryId(summaryId);
        this.workOrder.setEventType(WorkOrderEventType.CALL_CONNECTED.getCode());
        this.workOrder.setStatus(WorkOrderStatus.PENDING);
        this.workOrder.setUpdateTime(LocalDateTime.now());

        // 设置接线时间
        if (StringUtils.isNotBlank(connectTime)) {
            this.workOrder.setConnectTime(LocalDateTimeUtils.parseLocalDateTimeByDateStr(connectTime));
        } else {
            this.workOrder.setConnectTime(LocalDateTime.now());
        }

        // 设置转接结果为成功
        this.workOrder.setTransferResult(HaierComplaintConstants.TransferResult.SUCCESS); // setComSummary为转接成功

        log.info("处理接线成功事件成功, agentId: {}, summaryId: {}, callId: {}, connectTime: {}",
                agentId, summaryId, this.workOrder.getCallId(), connectTime);
    }

    /**
     * 处理接线失败事件
     */
    public void handleCallFailed(String failReason) {
        if (StringUtils.isBlank(failReason)) {
            throw new IllegalArgumentException("失败原因不能为空");
        }

        // 更新工单信息
        this.workOrder.setFailReason(failReason);
        this.workOrder.setEventType(WorkOrderEventType.CALL_FAILED.getCode());
        this.workOrder.setStatus(WorkOrderStatus.PENDING);
        this.workOrder.setUpdateTime(LocalDateTime.now());

        // 设置转接结果为失败
        this.workOrder.setTransferResult(HaierComplaintConstants.TransferResult.FAIL); // callFailed为转接失败

        log.info("处理接线失败事件成功, failReason: {}, callId: {}",
                failReason, this.workOrder.getCallId());
    }

    /**
     * 获取并清空领域事件
     */
    public List<WorkOrderEvent> getAndClearDomainEvents() {
        if (CollectionUtils.isEmpty(this.domainEvents)) {
            return new ArrayList<>();
        }
        List<WorkOrderEvent> events = new ArrayList<>(this.domainEvents);
        this.domainEvents.clear();
        return events;
    }


    /**
     * 验证状态转换是否合法
     */
    private void validateStatusTransition(WorkOrderStatus from, WorkOrderStatus to) {
        // 定义合法的状态转换规则
        boolean isValid = false;

        switch (from) {
            case PENDING:
                isValid = to == WorkOrderStatus.CREATED || to == WorkOrderStatus.CANCELLED ||  to == WorkOrderStatus.PROCESSING || to == WorkOrderStatus.COMPLETED;
                break;
            case CREATED:
            case PROCESSING:
                isValid = to == WorkOrderStatus.PROCESSING || to == WorkOrderStatus.CANCELLED || to == WorkOrderStatus.COMPLETED;
                break;
            case COMPLETED:
            case CANCELLED:
                // 终态不允许转换
                break;
        }

        if (!isValid) {
            throw new IllegalStateException(
                    String.format("非法的状态转换: %s -> %s", from, to));
        }
    }
}
