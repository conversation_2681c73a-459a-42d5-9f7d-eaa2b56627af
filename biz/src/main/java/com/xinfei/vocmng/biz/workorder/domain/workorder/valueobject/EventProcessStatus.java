package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

/**
 * 事件处理状态枚举
 *
 * <AUTHOR>
 * @version $ EventProcessStatus, v 0.1 2025/08/11 EventProcessStatus Exp $
 */
public enum EventProcessStatus {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理成功
     */
    SUCCESS("SUCCESS", "处理成功"),

    /**
     * 处理失败
     */
    FAILED("FAILED", "处理失败");

    private final String code;
    private final String description;

    EventProcessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static EventProcessStatus fromCode(String code) {
        for (EventProcessStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的事件处理状态: " + code);
    }
}
