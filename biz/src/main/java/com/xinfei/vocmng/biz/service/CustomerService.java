/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CustomerService, v 0.1 2023-12-20 19:34 junjie.yan Exp $
 */

public interface CustomerService {

    Paging<CustomerDto> getCustomerList(GetCustomerListRequest request);

    List<UserNoAppDto> getUserNoList(GetUserNoListRequest request);

    CustomerDetailDto getCustomerDetail(GetCustomerRequest request);

    List<BankDto> getAllBankCardList(String userNo);

    ImagesDto getImagesDetail(String userNo, String custNo);

    Boolean sendMessage(SendMessageRequest request);

    List<SmsTemplate> getSmsTemplates();

    Boolean userLogOff(LogOffReq logOffReq);

    Boolean userLogOffCancel(LogOffReq request);

    Boolean repayListQuery(String custNo);

    Boolean repayListUpdate(RepayListCreateReq request);

    /**
     * 1. 只传手机号，返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     * 2. 只传身份证号，返回UserNo列表，第一条为订单中创建时间最晚的UserNo
     */
    List<UserNoAppDto> getUserNoByMobileId(String mobile, String idCard, String userNo);

    List<UserRelateNumber> queryUserRelateNumber(QueryUserRelateNumberRequest queryUserRelateNumberRequest);

}