/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.function;

import cn.hutool.core.util.ObjectUtil;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.dal.po.Employee;

/** 有效用户校验
 * <AUTHOR>
 * @version $ UserValidator, v 0.1 2023/12/23 20:29 wancheng.qu Exp $
 */

@FunctionalInterface
public interface UserValidator {

    void validate(Employee user);

    static UserValidator defaultValidator() {
        return user -> {
            validateUserNotFound(user);
            validateUserNotClosed(user);
        };
    }

    static void validateUserNotFound(Employee user) {
        if (ObjectUtil.isNull(user)) {
            throw new IgnoreException(TechplayErrDtlEnum.NOUSER_ERROR);
        }
    }


    static void validateUserNotClosed(Employee user) {
        if (ObjectUtil.equals(1, user.getState())) {
            throw new IgnoreException(TechplayErrDtlEnum.USERCLOSE_ERROR);
        }
    }
}
