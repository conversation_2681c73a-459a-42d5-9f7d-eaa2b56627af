/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderPayLogAdminDTO;
import com.xinfei.vocmng.biz.model.enums.VipPayStatusEnum;
import com.xinfei.vocmng.biz.model.enums.VipPayTypeEnum;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 会员卡支付明细转换器
 *
 * <AUTHOR>
 * @version $ VipOrderPayLogConverter, v 0.1 2025/5/15 15:40 shaohui.chen Exp $
 */
@Mapper
public interface VipOrderPayLogConverter {

    VipOrderPayLogConverter INSTANCE = Mappers.getMapper(VipOrderPayLogConverter.class);

    /**
     * 将内部系统DTO转换为内部系统详情DTO
     *
     * @param vipOrderPayLogDTO 内部系统DTO
     * @return 内部系统详情DTO
     */
    @Mapping(source = "payType", target = "payTypeDesc", qualifiedByName = "payTypeToDesc")
    @Mapping(source = "payStatus", target = "payStatusDesc", qualifiedByName = "payStatusToDesc")
    @Mapping(source = "payAmount", target = "payAmount", qualifiedByName = "convertPayAmount")
    VipOrderPayLogDetailDTO convertToDetailDTO(VipOrderPayLogAdminDTO vipOrderPayLogDTO);


    /**
     * 将内部系统DTO列表转换为内部系统详情DTO列表
     *
     * @param vipOrderPayLogDTOList 内部系统DTO列表
     * @return 内部系统详情DTO列表
     */
    List<VipOrderPayLogDetailDTO> convertToDetailDTOList(List<VipOrderPayLogAdminDTO> vipOrderPayLogDTOList);

    /**
     * 将支付类型转换为支付类型描述
     *
     * @param payType 支付类型
     * @return 支付类型描述
     */
    @Named("payTypeToDesc")
    default String payTypeToDesc(String payType) {
        return VipPayTypeEnum.getDescByCode(payType);
    }

    /**
     * 将支付状态转换为支付状态描述
     *
     * @param status 支付状态
     * @return 支付状态描述
     */
    @Named("payStatusToDesc")
    default String payStatusToDesc(String status) {
        return VipPayStatusEnum.getDescByCode(status);
    }

    /**
     * 将支付金额（分）转换为支付金额（元）
     *
     * @param payAmount 支付金额（分）
     * @return 支付金额（元）
     */
    @Named("convertPayAmount")
    default BigDecimal convertPayAmount(Long payAmount) {
        if (Objects.isNull(payAmount)) {
            return BigDecimal.ZERO;
        }
        // 将分转换为元，除以100
        return new BigDecimal(payAmount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }
}
