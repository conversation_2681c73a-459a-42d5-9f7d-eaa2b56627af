package com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject;

import lombok.Getter;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 * @version $ WorkOrderStatus, v 0.1 2025/07/30 WorkOrderStatus Exp $
 */
@Getter
public enum WorkOrderStatus {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 已创建
     */
    CREATED("CREATED", "已创建"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    WorkOrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static WorkOrderStatus fromCode(String code) {
        for (WorkOrderStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的工单状态: " + code);
    }

    /**
     * 是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == CANCELLED;
    }
}
