package com.xinfei.vocmng.biz.workorder.application.service;

import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.constants.HaierComplaintConstants;
import com.xinfei.vocmng.biz.constants.UDeskConstants;
import com.xinfei.vocmng.biz.remote.HaierComplaintRemoteService;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEvent;
import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExternalEventType;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExternalWorkOrderStatus;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.itl.rr.haier.AddProcessRecordRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 工单事件处理服务
 * 负责处理工单事件并适配到第三方接口
 *
 * <AUTHOR>
 * @version $ WorkOrderEventProcessService, v 0.1 2025/08/12 WorkOrderEventProcessService Exp $
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkOrderEventProcessService {

    private final HaierComplaintRemoteService haierComplaintRemoteService;

    /**
     * 处理工单事件
     *
     * @param event 工单事件
     */
    public void processWorkOrderEvent(WorkOrderEvent event) {
        if (Objects.isNull(event)) {
            log.warn("工单事件为空，跳过处理");
            return;
        }

        // 只处理海尔消金的工单事件
        if (!UDeskConstants.FunderCode.HAIER.equals(event.getFunderCode())) {
            log.info("非海尔消金工单事件，跳过处理, funderCode: {}, orderNo: {}",
                    event.getFunderCode(), event.getOrderNo());
            return;
        }

        // 获取事件类型（从事件数据中获取外部传入的eventType）
        String eventTypeStr = (String) event.getEventData("eventType");
        ExternalEventType eventType = ExternalEventType.fromCode(eventTypeStr);

        // 只有当eventType是FEEDBACK或CLOSED时
        if (Objects.isNull(eventType) || !eventType.shouldProcess()) {
            log.info("事件类型不需要推送MQ通知, eventType: {}, orderNo: {}",
                    eventTypeStr, event.getOrderNo());
            return;
        }

        log.info("开始处理工单事件, eventType: {}, orderNo: {}, funderCode: {}",
                eventType.getCode(), event.getOrderNo(), event.getFunderCode());

        // 适配并调用海尔消金接口
        adaptAndCallHaierComplaintService(event);

        log.info("工单事件处理完成, eventType: {}, orderNo: {}",
                eventType.getCode(), event.getOrderNo());
    }



    /**
     * 适配并调用海尔消金服务
     */
    private void adaptAndCallHaierComplaintService(WorkOrderEvent event) {
        // 构建请求参数
        AddProcessRecordRequest request = adaptToAddProcessRecordRequest(event);
        // 调用海尔消金接口
        haierComplaintRemoteService.addProcessRecord(request);
    }

    /**
     * 将工单事件适配为AddProcessRecordRequest
     */
    private AddProcessRecordRequest adaptToAddProcessRecordRequest(WorkOrderEvent event) {
        AddProcessRecordRequest request = new AddProcessRecordRequest();

        // 基础字段映射 - 根据海尔字段映射关系
        request.setHaierCallId(getHaierCallId(event));                                    // 海消通话id - 资方Call id
        request.setTelPhone(getCustomerPhone(event));                                     // 来电号码 - 资方来电号码
        request.setCooperOrgNo(ApolloConstant.HAIER_COMPLAINT_COOPER_ORG_NO);            // 合作机构编号 - 服务层对外写死
        request.setCooperOrgCallId(getCooperOrgCallId(event));                           // 合作机构通话id - 服务层对外写死
        request.setCallTransferTime(getCallTransferTime(event));                         // 转接时间 - udesk收到转接的时间
        request.setCallTransferResult(getCallTransferResult(event));                     // 转接结果 - Udesk该通话接通状态
        request.setProcessTime(getProcessTime(event));                                   // 跟进时间

        // 获取外部传入的事件类型
        String externalEventTypeStr = (String) event.getEventData("eventType");
        ExternalEventType externalEventType = ExternalEventType.fromCode(externalEventTypeStr);

        // 根据外部事件类型（FEEDBACK/CLOSED）设置不同的字段映射
        if (Objects.equals(externalEventType, ExternalEventType.FEEDBACK)) {
            // 处理过程中：写死"客服正在跟进中"
            request.setProcessDetails("客服正在跟进中");                                  // 跟进情况
            request.setProcessResult("客服正在跟进中");                                   // 处理结果
            adaptForFeedbackEvent(request, event);
        } else if (Objects.equals(externalEventType, ExternalEventType.CLOSED)) {
            // 完结：写死"工单处理完成"
            request.setProcessDetails("工单处理完成");                                    // 跟进情况
            request.setProcessResult("工单处理完成");                                     // 处理结果
            adaptForClosedEvent(request, event);
        }

        return request;
    }


    /**
     * 适配FEEDBACK事件（反馈事件）
     */
    private void adaptForFeedbackEvent(AddProcessRecordRequest request, WorkOrderEvent event) {
        String customerStatus = (String) event.getEventData("customerStatus");
        String externalWorkOrderStatus = (String) event.getEventData("externalWorkOrderStatus");

        // 客诉状态：使用工单状态映射方法
        String complaintStatus = mapWorkOrderStatusToHaierComplaintStatus(externalWorkOrderStatus);
        request.setComplaintStatus(complaintStatus);

        // 是否达成一致：根据客户状态映射
        String isAgreement = mapCustomerStatusToIsAgreement(customerStatus);
        request.setIsAgreement(isAgreement);

        log.debug("适配FEEDBACK事件完成, orderNo: {}, customerStatus: {}, externalWorkOrderStatus: {}, complaintStatus: {}, isAgreement: {}",
                event.getOrderNo(), customerStatus, externalWorkOrderStatus, complaintStatus, isAgreement);
    }

    /**
     * 适配CLOSED事件（关闭事件）
     */
    private void adaptForClosedEvent(AddProcessRecordRequest request, WorkOrderEvent event) {
        String externalWorkOrderStatus = (String) event.getEventData("externalWorkOrderStatus");

        // 客诉状态：使用工单状态映射方法
        String complaintStatus = mapWorkOrderStatusToHaierComplaintStatus(externalWorkOrderStatus);
        request.setComplaintStatus(complaintStatus);

        // 是否达成一致：根据工单状态映射（完结状态下使用工单状态而不是客户状态）
        String isAgreement = mapWorkOrderStatusToIsAgreement(externalWorkOrderStatus);
        request.setIsAgreement(isAgreement);

        log.debug("适配CLOSED事件完成, orderNo: {}, externalWorkOrderStatus: {}, complaintStatus: {}, isAgreement: {}",
                event.getOrderNo(), externalWorkOrderStatus, complaintStatus, isAgreement);
    }

    /**
     * 获取海尔消金通话ID
     */
    private String getHaierCallId(WorkOrderEvent event) {
        return  (String) event.getEventData("callId");
    }

    /**
     * 获取客户电话
     */
    private String getCustomerPhone(WorkOrderEvent event) {
        return (String) event.getEventData("customerPhone");
    }

    /**
     * 获取合作机构通话id - 服务层对外写死
     */
    private String getCooperOrgCallId(WorkOrderEvent event) {
        // 根据映射关系，合作机构通话id由服务层对外写死，这里使用工单号
        return (String) event.getEventData("udeskCallId");
    }

    /**
     * 获取转接时间 - udesk收到转接的时间
     */
    private String getCallTransferTime(WorkOrderEvent event) {
        // 优先使用领域事件中的转接时间
        Object transferTime = event.getEventData("transferTime");
        if (Objects.nonNull(transferTime)) {
            if (transferTime instanceof LocalDateTime) {
                return formatDateTime((LocalDateTime) transferTime);
            } else if (transferTime instanceof String) {
                return (String) transferTime;
            }
        }

        // 如果没有转接时间，使用当前时间
        return formatDateTime(LocalDateTime.now());
    }

    /**
     * 获取转接结果 - Udesk该通话接通状态
     */
    private String getCallTransferResult(WorkOrderEvent event) {
        // 优先使用领域事件中的转接结果
        String transferResult = (String) event.getEventData("transferResult");
        if (StringUtils.isNotBlank(transferResult)) {
            return transferResult;
        }

        // 默认返回成功
        return HaierComplaintConstants.TransferResult.SUCCESS;
    }

    /**
     * 获取跟进时间
     */
    private String getProcessTime(WorkOrderEvent event) {
        // 优先使用事件数据中的跟进时间
        String processTime = (String) event.getEventData("processTime");
        if (StringUtils.isNotBlank(processTime)) {
            return processTime;
        }

        // 使用事件发生时间
        return event.getOccurTime();
    }


    /**
     * 将工单系统状态映射到海尔消金客诉状态
     */
    private String mapWorkOrderStatusToHaierComplaintStatus(String workOrderStatusCode) {
        if (StringUtils.isBlank(workOrderStatusCode)) {
            return HaierComplaintConstants.ComplaintStatus.PROCESS;
        }
        ExternalWorkOrderStatus externalStatus = ExternalWorkOrderStatus.fromCode(workOrderStatusCode);
        return externalStatus.toHaierComplaintStatus();
    }

    /**
     * 格式化时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return LocalDateTimeUtils.format(LocalDateTime.now());
        }
        return LocalDateTimeUtils.format(dateTime);
    }


    /**
     * 将客户状态映射到是否达成一致
     * 根据映射关系：
     * 是 - 同意协商
     * 否 - 不配合
     * 联系不上 - 未联系上、不方便、挂机
     */
    private String mapCustomerStatusToIsAgreement(String customerStatus) {
        if (StringUtils.isBlank(customerStatus)) {
            return HaierComplaintConstants.AgreementStatus.YES; // 默认为是
        }

        String lowerStatus = customerStatus.toLowerCase();

        // 是 - 同意协商
        if (lowerStatus.contains("同意协商")) {
            return HaierComplaintConstants.AgreementStatus.YES;
        }

        // 联系不上 - 未联系上、不方便、挂机
        if (lowerStatus.contains("未联系上") || lowerStatus.contains("不方便") || lowerStatus.contains("挂机")) {
            return HaierComplaintConstants.AgreementStatus.UNREACHABLE;
        }

        // 否 - 不配合
        if (lowerStatus.contains("不配合")) {
            return HaierComplaintConstants.AgreementStatus.NO;
        }

        // 默认为是
        return HaierComplaintConstants.AgreementStatus.YES;
    }

    /**
     * 将工单状态映射到是否达成一致（用于完结状态）
     * 根据映射关系：
     * 是 - 知悉结案(6)、催收结案(7)、已结案(8)、失联结案(9)
     * 否 - 终止（5）、不接受方案(10)
     * 联系不上（无） - 其他情况
     */
    private String mapWorkOrderStatusToIsAgreement(String externalWorkOrderStatus) {
        if (StringUtils.isBlank(externalWorkOrderStatus)) {
            return HaierComplaintConstants.AgreementStatus.UNREACHABLE; // 默认为联系不上
        }

        ExternalWorkOrderStatus status = ExternalWorkOrderStatus.fromCode(externalWorkOrderStatus);

        switch (status) {
            case ACKNOWLEDGED_CLOSED:      // 6-知悉结案
            case COLLECTION_CLOSED:        // 7-催收结案
            case CLOSED:                   // 8-已结案
            case LOST_CONTACT_CLOSED:      // 9-失联结案
                return HaierComplaintConstants.AgreementStatus.YES;

            case REJECTED_PLAN:
            case TERMINATED:// 10-不接受方案:
                return HaierComplaintConstants.AgreementStatus.NO;

            default:
                // 其他情况默认为联系不上
                return HaierComplaintConstants.AgreementStatus.UNREACHABLE;
        }
    }
}
