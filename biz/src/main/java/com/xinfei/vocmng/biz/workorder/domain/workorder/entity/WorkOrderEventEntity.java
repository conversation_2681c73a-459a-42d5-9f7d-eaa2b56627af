package com.xinfei.vocmng.biz.workorder.domain.workorder.entity;

import com.xinfei.vocmng.biz.workorder.domain.workorder.event.WorkOrderEventType;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.EventProcessStatus;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * 工单事件实体
 * 存储工单事件的处理状态、重试信息等
 *
 * <AUTHOR>
 * @version $ WorkOrderEventEntity, v 0.1 2025/08/11 WorkOrderEventEntity Exp $
 */
@Data
@Builder
public class WorkOrderEventEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 事件类型
     */
    private WorkOrderEventType eventType;

    /**
     * 事件数据
     */
    private Map<String, Object> eventData;

    /**
     * 处理状态
     */
    private EventProcessStatus status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 验证事件实体数据
     */
    public void validate() {
        if (Objects.isNull(workOrderId)) {
            throw new IllegalArgumentException("工单ID不能为空");
        }
        
        if (Objects.isNull(eventType)) {
            throw new IllegalArgumentException("事件类型不能为空");
        }
        
        if (Objects.isNull(status)) {
            throw new IllegalArgumentException("处理状态不能为空");
        }
        
        if (Objects.isNull(retryCount) || retryCount < 0) {
            throw new IllegalArgumentException("重试次数不能为空且不能小于0");
        }
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }

    /**
     * 标记处理成功
     */
    public void markSuccess() {
        this.status = EventProcessStatus.SUCCESS;
        this.processTime = LocalDateTime.now();
        this.errorMessage = null;
    }

    /**
     * 标记处理失败
     */
    public void markFailed(String errorMessage) {
        this.status = EventProcessStatus.FAILED;
        this.processTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
    }

    /**
     * 设置下次重试时间
     */
    public void setNextRetryTime(LocalDateTime nextRetryTime) {
        this.nextRetryTime = nextRetryTime;
    }
}
