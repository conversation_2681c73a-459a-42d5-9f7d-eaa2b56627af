/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.repaytrade.facade.rr.dto.RefundTrialDetail;
import com.xinfei.repaytrade.facade.rr.request.RefundApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.CalculateRefundAmountResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRefundApplyResponse;
import com.xinfei.vocmng.biz.model.enums.OfflineRefundMethodEnum;
import com.xinfei.vocmng.biz.model.enums.RefundTypeEnum;
import com.xinfei.vocmng.biz.model.req.OrderRefundRecordsResp;
import com.xinfei.vocmng.biz.model.req.OverPayCancellationReq;
import com.xinfei.vocmng.biz.model.req.OverPayRefundReq;
import com.xinfei.vocmng.biz.model.req.RefundApplyReq;
import com.xinfei.vocmng.biz.model.resp.OrderRefundRecods;
import com.xinfei.vocmng.biz.model.resp.OverPayRecordsResp;
import com.xinfei.vocmng.biz.model.resp.RefundResp;
import com.xinfei.vocmng.biz.model.resp.RefundTrialResp;
import com.xinfei.vocmng.biz.rr.dto.RefundRequestDto;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.RefundRequest;
import com.xinfei.vocmng.itl.rr.RefundApplyCancelReq;
import com.xinfei.vocmng.itl.rr.RefundOrderRecordRes;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface RefundConverter {

    RefundConverter INSTANCE = Mappers.getMapper(RefundConverter.class);

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    RefundResp calculateRefundToRefundResp(CalculateRefundAmountResponse response);

    RefundTrialResp refundTrialDetailToTrialResp(RefundTrialDetail refundTrialDetail);

    /**
     * 退款请求==>退款申请表
     *
     * @param refundApplyReq
     * @return
     */

    @Mapping(target = "refundAmount", ignore = true)
    @Mapping(target = "offlineRefundMethod", ignore = true)
    @Mapping(target = "refundType", ignore = true)
    @Mapping(target = "repaymentNos", ignore = true)
    RefundRequest refundApplyReqToRefundRequest(RefundApplyReq refundApplyReq);

    @Mapping(target = "refundAmount", ignore = true)
    @Mapping(target = "offlineRefundMethod", ignore = true)
    @Mapping(target = "refundType", ignore = true)
    RefundRequest overPayRefundReqToRefundRequest(OverPayRefundReq overPayRefundReq);

    @AfterMapping
    default void refundRequestDto(RefundApplyReq req, @MappingTarget RefundRequest refundRequest) {
        refundRequest.setOfflineRefundMethod(OfflineRefundMethodEnum.getCodeByCodeStr(req.getOfflineRefundMethod()));
        refundRequest.setRefundType(RefundTypeEnum.getCodeByCodeStr(req.getRefundType()));
        refundRequest.setCrateTime(LocalDateTime.now());
        refundRequest.setUpdateTime(LocalDateTime.now());
        refundRequest.setRefundAmount(req.getRefundAmount().multiply(new BigDecimal("100")).longValue());
        refundRequest.setIsDel(0);
        refundRequest.setCreator(UserContextHolder.getUserIdentify());
        if (req.getExecuteType() == 1) {
            LocalDateTime executeTime = LocalDateTime.now().plusHours(2);
            refundRequest.setRefundTime(executeTime);
        } else if (req.getExecuteType() == 2) {
            LocalDateTime executeTime = LocalDateTime.now().plusDays(50);
            refundRequest.setRefundTime(executeTime);
        } else if (req.getExecuteType() == 3) {
            LocalDateTime executeTime = LocalDateTime.now().plusDays(15);
            refundRequest.setRefundTime(executeTime);
        } else {
            LocalDateTime executeTime = LocalDateTime.now().plusDays(req.getExecuteDay());
            refundRequest.setRefundTime(executeTime);
        }
    }

    @AfterMapping
    default void refundRequestDto(OverPayRefundReq req, @MappingTarget RefundRequest refundRequest) {
        refundRequest.setOfflineRefundMethod(OfflineRefundMethodEnum.getCodeByCodeStr(req.getOfflineRefundMethod()));
        refundRequest.setRefundType(RefundTypeEnum.getCodeByCodeStr(req.getRefundType()));
        refundRequest.setCrateTime(LocalDateTime.now());
        refundRequest.setUpdateTime(LocalDateTime.now());
        refundRequest.setRefundAmount(req.getRefundAmount().multiply(new BigDecimal("100")).longValue());
        refundRequest.setIsDel(0);
        refundRequest.setCreator(UserContextHolder.getUserIdentify());
        if (req.getExecuteType() == 1) {
            LocalDateTime executeTime = LocalDateTime.now().plusHours(2);
            refundRequest.setRefundTime(executeTime);
        } else {
            LocalDateTime executeTime = LocalDateTime.now().plusDays(50);
            refundRequest.setRefundTime(executeTime);
        }
    }

    /**
     * 退款请求==>退款接口请求
     *
     * @param refundApplyReq
     * @return
     */
    @Mapping(source = "refundAmount", target = "totalRefundAmt")
    @Mapping(source = "billNo", target = "planNo")
    RefundApplyRequest refundApplyReqToRefundApplyRequest(RefundApplyReq refundApplyReq);

//    RefundApplyRequest refundRequestToRefundApplyRequest(RefundRequest refundRequest);

    OrderRefundRecordsResp instructionInfoToApplyRecords(QueryRefundApplyResponse.RefundInstructionInfo refundInstructionInfo);

    OverPayRecordsResp recordResToOverPayRecords(RefundOrderRecordRes recordRes);

    RefundApplyCancelReq reqToRefundApplyCancelReq(OverPayCancellationReq req);

    OrderRefundRecods refundRequestToOrderRefundRecods(RefundRequest req);

    @Mapping(target = "refundAmount", ignore = true)
    RefundRequestDto refundRequestToRefundRequestDto(RefundRequest req);
}