package com.xinfei.vocmng.biz.component.remote.processor;

import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;
import com.xinfei.vocmng.itl.model.header.BaseHeader;
import com.xinfei.vocmng.itl.model.header.CallCenterHeader;
import feign.Request;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * 合同中心通用请求签名处理
 *
 * <AUTHOR>
 * @version $ CallCenterSignAndHeaderProcessor, v 0.1 2023/12/27 22:21 qu.lu Exp $
 */
@Component
@ProcessRule(service = ThirdPartyServiceEnum.CALL_CENTER)
public class CallCenterSignAndHeaderProcessor extends AbstractHeaderProcessRule{

    @Override
    protected BaseHeader buildHeader(RequestTemplate template) {
        String method = template.method();
        if(!Request.HttpMethod.POST.name().equalsIgnoreCase(method)){
            throw new IllegalArgumentException("GET sign not support.");
        }

        String requestBody = getRequestBody(template);

        CallCenterHeader header = new CallCenterHeader();
        header.setAppKey(vocConfig.getCallCenterAppKey());
        header.setSign(doBodySign(requestBody));
        header.setTimestamp(System.currentTimeMillis());

        return header;
    }

    @Override
    public <T> void process(RequestTemplate template) {
        headerProcess(template);
    }

    /**
     * POST请求签名
     *
     * @param body
     * @return
     */
    private String doBodySign(String body){
        String appSecret = vocConfig.getCallCenterAppSecret();

        body += "&app_secret="+ appSecret;
        return DigestUtils.md5DigestAsHex(body.getBytes(StandardCharsets.UTF_8));
    }
}
