/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy.impl;

import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.rr.CalculationContext;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.request.FeeAmountDtoResp;
import com.xinfei.vocmng.biz.rr.response.CalculateFee;
import com.xinfei.vocmng.biz.rr.response.ExemptionResponse;
import com.xinfei.vocmng.biz.strategy.ExemptionApplicationStrategy;
import com.xinfei.vocmng.biz.util.ReductionStrategyUtil;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version $ GenericExemptionStrategy, v 0.1 2025-04-29 13:59 junjie.yan Exp $
 */
@Slf4j
public class GenericExemptionStrategy implements ExemptionApplicationStrategy {

    // --- 字段提取器  ---
    private final Function<PlanDetailDto, BigDecimal> initPlanAmountExtractor;
    private final Function<PlanDetailDto, BigDecimal> deductPlanAmountExtractor;
    private final Function<CalculateFee, BigDecimal> calculateFeeAmountExtractor;
    private final Function<FeeAmountDtoResp, BigDecimal> canDeductAmountExtractor;
    private final String feeName;

    // --- 字段设置器  ---
    private final BiConsumer<ExemptionResponse, BigDecimal> lowerBoundSetter; // Setter for the lower bound field
    private final BiConsumer<ExemptionResponse, BigDecimal> upperBoundSetter; // Setter for the upper bound field
    private final BiConsumer<ExemptionResponse, BigDecimal> maxBoundSetter; // 新增 Max Setter

    public GenericExemptionStrategy(
            Function<PlanDetailDto, BigDecimal> initPlanAmountExtractor,
            Function<PlanDetailDto, BigDecimal> deductPlanAmountExtractor,
            Function<CalculateFee, BigDecimal> calculateFeeAmountExtractor,
            Function<FeeAmountDtoResp, BigDecimal> canDeductAmountExtractor,
            BiConsumer<ExemptionResponse, BigDecimal> lowerBoundSetter, // Pass the setter method reference
            BiConsumer<ExemptionResponse, BigDecimal> upperBoundSetter,
            BiConsumer<ExemptionResponse, BigDecimal> maxBoundSetter,
            String feeName) { // Pass the setter method reference
        this.initPlanAmountExtractor = initPlanAmountExtractor;
        this.deductPlanAmountExtractor = deductPlanAmountExtractor;
        this.calculateFeeAmountExtractor = calculateFeeAmountExtractor;
        this.canDeductAmountExtractor = canDeductAmountExtractor;
        this.lowerBoundSetter = lowerBoundSetter;
        this.upperBoundSetter = upperBoundSetter;
        this.maxBoundSetter = maxBoundSetter; // 初始化 Max Setter
        this.feeName = feeName;
    }

    @Override
    public void apply(CalculationContext context, ExemptionResponse exemption) {
        // 1. Extract values using Functions
        BigDecimal initAmount = initPlanAmountExtractor.apply(context.getInitPlan());
        BigDecimal deductAmount = deductPlanAmountExtractor.apply(context.getDeductPlan());
        BigDecimal calculatedAmount = calculateFeeAmountExtractor.apply(context.getCalculateFee());
        BigDecimal canDeductAmount = canDeductAmountExtractor.apply(context.getCalculateFee().getCanDeductAmtDetail());
        BigDecimal actualValue = context.getValue(); // 实际传入的百分比/值
        FeeStrategyConfig config = context.getConfig();

        // 2. Perform calculation
        log.info(LogUtil.newInfoLog("减免计算过程", "loanNo", context.getCalculateFee().getLoanNo(),
                "费项：" + feeName +
                        " 减免百分比：" + actualValue +
                        " 初始金额：" + initAmount +
                        " 历史减免：" + deductAmount +
                        " 试算金额：" + calculatedAmount +
                        " 还款引擎最大可减免：" + canDeductAmount));
        ControlRes<BigDecimal, BigDecimal> calculateResult = ReductionStrategyUtil.calculateAmount(
                BigDecimal.ZERO,
                actualValue,
                initAmount,
                deductAmount,
                calculatedAmount
        );

        // 3. Apply results using BiConsumers (specific setters)
        lowerBoundSetter.accept(exemption, calculateResult.getLeft());
        if (canDeductAmount.compareTo(calculateResult.getRight()) > 0) {
            upperBoundSetter.accept(exemption, calculateResult.getRight());
        } else {
            upperBoundSetter.accept(exemption, canDeductAmount);
        }

        // 4. Perform calculation for Max using 100% (BigDecimal.ONE)
        if (config != null && config.getCanBreakOut()  == 1) {
            ControlRes<BigDecimal, BigDecimal> resultMax = ReductionStrategyUtil.calculateAmount(
                    BigDecimal.ZERO,
                    new BigDecimal("100"), // <--- 强制使用 100%
                    initAmount,
                    deductAmount,
                    calculatedAmount // 注意：这里的参数是否也需要调整取决于业务，假设基础金额不变
            );

            // 5. Apply Max result (assuming Max is the Left part of the 100% calculation)
            maxBoundSetter.accept(exemption, resultMax.getRight()); // 设置对应的 Max 字段
        }
    }

}