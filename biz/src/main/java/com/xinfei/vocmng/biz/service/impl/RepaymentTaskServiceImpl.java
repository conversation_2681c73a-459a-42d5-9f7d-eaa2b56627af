/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResultResponse;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.annotation.RedissonLockAnnotation;
import com.xinfei.vocmng.biz.service.RepayService;
import com.xinfei.vocmng.biz.service.RepaymentTaskService;
import com.xinfei.vocmng.biz.util.PaginationUtils;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.util.LogUtil;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ RepaymentTaskServiceImpl, v 0.1 2024/3/28 17:25 wancheng.qu Exp $
 */
@Service
@Slf4j
public class RepaymentTaskServiceImpl implements RepaymentTaskService {


    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Resource
    private LcsFeignService lcsFeignService;
    @Resource
    private LoginUserConfig loginUserConfig;
    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;
    @Autowired
    private RepayService repayService;


    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 1.将过期的更改为实效，并推送信贷端-->repayment-plan-invalid     2.方案查询接口 query-repayment-plan-result
     * @date 2024/3/30 11:16
     */
    @Override
    @RedissonLockAnnotation(lockRedisKey = LoginUserConstants.REDISLOCK_REPAYMENT)
    public void updateRepaymentStatus() {
        log.info("updateRepaymentStatus task begin");
        long startTime1 = System.currentTimeMillis();
        //明细表更新：分页取出生效中方案的所有方案明细，然后去LCS查询明细状态（是否有效、是否使用），并更新客服明细表
        PaginationUtils.PaginationParam paginationParam = new PaginationUtils.PaginationParam(loginUserConfig.getRepayPageSize());
        PaginationUtils.invokePaginationNew(
                PaginationUtils.PaginationTypeEnum.ID_PAGING,
                paginationParam,
                () -> getData(paginationParam.getId(), paginationParam.getPageSize()),
                this::handleData
        );
        //方案表更新成功：对所有方案明细rpd.status = 1 AND rpd.use_status = 2的方案，置为成功
        repayService.updateRepaymentSuccess();
        //1. 待生效方案且待审核过期的，置为失效且审核拒绝
        //2. 待生效方案且过期的，置为失效
        //3. 生效中的方案，方案有效期过期
        //4. 生效中的方案，方案明细都为失效（status=0）
        //5. 生效中的方案 明细均为-- 有效已使用（status=1且use_status=2）>0 与 -- 失效（status=0）>0
        //方案表更新失效：将上述方案置为失效
        updateInvalidAndSend();
        log.info("updateRepaymentStatus task end,cost:{}", System.currentTimeMillis() - startTime1);
    }

    /**
     * @return null
     * <AUTHOR>
     * @description 待生效的超时--->失效(无需调下游失效接口)，方案所有明细到终态属于失效--->失效， 方案生效中超时------->失效
     * @date 2024/4/11 16:53
     */
    public void updateInvalidAndSend() {
        repayService.updatePending();
        List<RepaymentPlanDetail> res = repayService.queryInvalidData();
        if (CollectionUtils.isNotEmpty(res)) {
            try {
                List<String> req = res.stream().map(RepaymentPlanDetail::getId).map(Object::toString).collect(Collectors.toList());
                List<String> loanNos = res.stream().map(RepaymentPlanDetail::getLoanNo).collect(Collectors.toList());
                repayFacadeClient.repaymentPlanInvalid(req, loanNos, "过期失效");
//                res.forEach(p->{
//                    if(Objects.equals(3,p.getRepayMethod()) && StringUtils.isNotBlank(p.getRepaymentNo()))repayFacadeClient.close(p.getRepaymentNo(),"过期失效");
//                });
            } catch (Exception e) {
                log.error("repaymentPlanInvalid send error,msg:{}", e.getMessage());
            }
            Set<Long> cl = res.stream().map(RepaymentPlanDetail::getPlanId).collect(Collectors.toSet());
            repayService.updateRepaymentPlanStatus(new ArrayList<>(cl), 2);
        }

    }

    public void handleData(List<RepaymentPlanDetail> data) {
        List<Long> planDetailIds = data.stream().map(RepaymentPlanDetail::getId).collect(Collectors.toList());

        List<RepaymentPlanDetail> needCheckBill = data.stream().filter(RepaymentPlanDetail::hasAllZeroReductions).collect(Collectors.toList());
        List<Long> needCheckPlanDetailIds = needCheckBill.stream().map(RepaymentPlanDetail::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(needCheckBill)) {
            updateZeroRepaymentStatus(needCheckBill);
        }

        planDetailIds = planDetailIds.stream().filter(id -> !needCheckPlanDetailIds.contains(id)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(planDetailIds)) {
            QueryRepaymentPlanResultResponse rest = repayFacadeClient.queryRepaymentPlanResult(planDetailIds.stream()
                    .map(Object::toString).collect(Collectors.toList()), null, null);
            if (Objects.nonNull(rest) && CollectionUtils.isNotEmpty(rest.getPlanDetailResult())) {
                List<QueryRepaymentPlanResultResponse.PlanDetailResult> pr = rest.getPlanDetailResult();
                pr.forEach(p -> repayService.updateDetail(p));
            }
        }
    }

    private void updateZeroRepaymentStatus(List<RepaymentPlanDetail> needCheckBill) {
        Map<String, RepaymentPlanDetail> needCheckBillMap = needCheckBill.stream()
                .collect(Collectors.toMap(
                        RepaymentPlanDetail::getLoanNo,
                        detail -> detail,
                        (existing, duplicate) -> existing // 处理重复 loanNo（根据业务逻辑选择保留哪个）
                ));
        log.info(LogUtil.infoLog("needCheckBillMap", needCheckBillMap));
        // 1. 提取 loanNos 并拆分批次
        List<String> loanNos = needCheckBill.stream().map(RepaymentPlanDetail::getLoanNo).collect(Collectors.toList());
        List<List<String>> batches = new ArrayList<>();
        int batchSize = 20;
        for (int i = 0; i < loanNos.size(); i += batchSize) {
            batches.add(loanNos.subList(i, Math.min(i + batchSize, loanNos.size())));
        }
        // 2. 逐批调用接口并收集结果
        List<LoanPlanResponse> allResponses = new ArrayList<>();
        for (List<String> batch : batches) {
            LoanPlanRequest request = new LoanPlanRequest();
            request.setLoanNos(batch);

            try {
                List<LoanPlanResponse> responses = lcsFeignService.planDetail(request);
                allResponses.addAll(responses);
            } catch (Exception e) {
                log.error("调用 planDetail 接口失败，loanNos: {}", batch, e);
            }
        }

        // 3. 根据结果更新数据库
        for (LoanPlanResponse loanPlanResponse : allResponses) {
            RepaymentPlanDetail detail = needCheckBillMap.get(loanPlanResponse.getLoanNo());
            if (!"FP".equals(loanPlanResponse.getStatus())) {
                if (StringUtils.isNotBlank(detail.getTerm())) {
                    List<Integer> terms = detail.getTermList();
                    Map<Integer, String> termStatusMap = new HashMap<>();
                    for (PlanResponse plan : loanPlanResponse.getPlanList()) { // 假设 Plan 是 planList 的元素类型
                        termStatusMap.put(plan.getTerm(), plan.getRpyFlag());
                    }
                    boolean allSettled = terms.stream()
                            .allMatch(term -> {
                                String status = termStatusMap.get(term);
                                return "2".equals(status); // 根据实际状态值调整
                            });

                    if  (allSettled) {
                        detail.setStatus(1);
                        detail.setUseStatus(2);
                        repaymentPlanDetailMapper.updateById(detail);
                        log.info(LogUtil.infoLog("updateZeroRepaymentStatus", detail.getId()));
                    }
                }
            } else {
                detail.setStatus(1);
                detail.setUseStatus(2);
                repaymentPlanDetailMapper.updateById(detail);
                log.info(LogUtil.infoLog("updateZeroRepaymentStatus", detail.getId()));
            }
        }
    }


    public ImmutableTriple<List<RepaymentPlanDetail>, Integer, Long> getData(Long id, Long size) {
        List<RepaymentPlanDetail> rest = repaymentPlanDetailMapper.getRepaymentPlanDetailList(id, size);
        return ImmutableTriple.of(rest, rest.size(), rest.isEmpty() ? 0L : rest.get(rest.size() - 1).getId());
    }

}