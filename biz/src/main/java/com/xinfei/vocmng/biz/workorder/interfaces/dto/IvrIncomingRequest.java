package com.xinfei.vocmng.biz.workorder.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * IVR进线事件请求
 *
 * <AUTHOR>
 * @version $ IvrIncomingRequest, v 0.1 2025/08/11 IvrIncomingRequest Exp $
 */
@Data
@ApiModel("IVR进线事件请求")
public class IvrIncomingRequest {

    @ApiModelProperty(value = "海尔消金通话ID", required = true)
    private String callId;

    @ApiModelProperty(value = "Udesk通话ID")
    private String udeskCallId;

    @ApiModelProperty(value = "客户电话")
    private String customerPhone;

    @ApiModelProperty(value = "资方编码", required = true)
    private String funderCode;

    @ApiModelProperty(value = "转接时间")
    private String transferTime;
}
