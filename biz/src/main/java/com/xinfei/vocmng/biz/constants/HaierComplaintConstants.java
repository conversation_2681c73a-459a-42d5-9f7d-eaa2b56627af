package com.xinfei.vocmng.biz.constants;

/**
 * 海尔消金客诉转接相关常量
 * 
 * <AUTHOR>
 * @version $ HaierComplaintConstants, v 0.1 2025/08/08 HaierComplaintConstants Exp $
 */
public class HaierComplaintConstants {
    
    /**
     * 客诉状态枚举
     */
    public static class ComplaintStatus {
        /** 处理中 */
        public static final String PROCESS = "process";
        /** 关闭 */
        public static final String CLOSE = "close";
    }
    
    /**
     * 进线渠道枚举
     */
    public static class ChannelType {
        /** 12378引导线 */
        public static final String CHANNEL_12378_NEW = "12378new";
        /** 12378引导 */
        public static final String CHANNEL_12378_OLD = "12378old";
        /** 客服热线95 */
        public static final String CHANNEL_955 = "955";
        /** 客服热线400 */
        public static final String CHANNEL_400 = "400";
    }
    
    /**
     * 转接结果枚举
     */
    public static class TransferResult {
        /** 成功 */
        public static final String SUCCESS = "SUCCESS";
        /** 失败 */
        public static final String FAIL = "FAIL";
    }
    
    /**
     * 是否达成一致枚举
     */
    public static class AgreementStatus {
        /** 是 */
        public static final String YES = "Y";
        /** 否 */
        public static final String NO = "N";
        /** 联系不上 */
        public static final String UNREACHABLE = "X";
    }
    
    /**
     * 是否已在线转接枚举
     */
    public static class TransferStatus {
        /** 是 */
        public static final String YES = "Y";
        /** 否 */
        public static final String NO = "N";
    }
}
