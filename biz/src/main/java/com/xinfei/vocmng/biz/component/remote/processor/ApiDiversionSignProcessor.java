/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.component.remote.processor;

import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.vocmng.biz.model.annotation.ProcessRule;
import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;
import com.xinfei.vocmng.itl.rr.ApiDiversionPhpReq;
import com.xinfei.vocmng.itl.rr.ContractBaseRequest;
import com.xinfei.xfframework.common.JsonUtil;
import feign.Request;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ ApiDiversionSignProcessor, v 0.1 2024/8/14 20:38 you.zhang Exp $
 */
@Slf4j
@Component
@ProcessRule(service = ThirdPartyServiceEnum.PLATFORM_SERVICE_API)
public class ApiDiversionSignProcessor extends AbstractProcessRule {

    @Override
    public <T> void process(RequestTemplate template) {


        String route = template.request().url();
        String method = template.method();
        String requestBody = getRequestBody(template);
        if(!isNeedProcess(method,requestBody)){
            return;
        }

        ObjectMapper mapper = JsonUtil.getObjMapper();
        try {
            ApiDiversionPhpReq request = mapper.readValue(requestBody, ApiDiversionPhpReq.class);
            String args = mapper.writeValueAsString(request.getArgs());
            String argsSign = doSign(route,args);
            request.setUa(getUa());
            request.setSign(argsSign);
            String signedBody = mapper.writeValueAsString(request);
            template.body(signedBody);

        } catch (JsonProcessingException e) {
            log.error("contract sign process failed, requestBody="+requestBody+", route="+route,e);
        }
    }

    /**
     * 对请求参数进行签名
     * @param route
     * @param args
     * @return
     */
    private String doSign(String route, String args){
        route = getSignRoute(route);
        String signKey = getSignKey();
        String toMd2Content = signKey+route+signKey+args+signKey;
        return md5(toMd2Content);
    }

    private String getSignRoute(String route) {
        if(StringUtils.isNotEmpty(route) && route.startsWith("/v1/")) {
            return route.substring(4);
        }
        return route;
    }

    private boolean isNeedProcess(String method, String requestBody){
        //只有post请求才进行签名操作
        if(!Request.HttpMethod.POST.name().equalsIgnoreCase(method)){
            return false;
        }

        if(StringUtils.isEmpty(requestBody)){
            return false;
        }

        return true;
    }

    /**
     * 获取API导流订单服务signKey
     * @return
     */
    private String getSignKey() {
        return vocConfig.getApiDiversionUA() + vocConfig.getApiDiversionSignKey() + vocConfig.getApiDiversionUA();
    }

    /**
     * 获取API导流订单服务Ua
     * @return
     */
    private String getUa(){
        return vocConfig.getApiDiversionUA();
    }

    /**
     * 对字符串进行MD5加密
     * @param content
     * @return
     */
    private String md5(String content){
        return DigestUtil.md5Hex(content);
    }
}
