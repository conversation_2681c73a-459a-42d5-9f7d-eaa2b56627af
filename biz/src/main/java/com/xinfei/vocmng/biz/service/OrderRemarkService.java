package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.rr.dto.OrderRemarkDto;
import com.xinfei.vocmng.biz.rr.request.AddOrderRemarkRequest;
import com.xinfei.vocmng.biz.rr.request.QueryOrderRemarkRequest;

import java.util.List;

/**
 * 订单备注信息接口
 *
 * <AUTHOR>
 * @version $ OrderRemarkService, v 0.1 2024/1/13 11:23 qu.lu Exp $
 */
public interface OrderRemarkService {
    /**
     * 添加订单备注信息
     *
     * @param request
     * @return
     */
    boolean addOrderRemark(AddOrderRemarkRequest request);

    /**
     * 根据订单号查询订单备注信息
     *
     * @param request
     * @return
     */
    List<OrderRemarkDto> queryOrderRemark(QueryOrderRemarkRequest request);
}
