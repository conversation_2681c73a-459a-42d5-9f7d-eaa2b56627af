/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.model.req.AsrInfoRequest;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.dal.mapper.AsrInfoMapper;
import com.xinfei.vocmng.dal.mapper.QualitySessionMapper;
import com.xinfei.vocmng.dal.po.AsrInfo;
import com.xinfei.vocmng.dal.po.QualitySession;
import com.xinfei.vocmng.itl.client.feign.impl.QualityInspectionClientImpl;
import com.xinfei.vocmng.itl.rr.SessionsMeta;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ QualityInspectionImp, v 0.1 2024-10-22 16:29 junjie.yan Exp $
 */
@Service
public class QualityInspectionImp {

    @Resource
    private QualityInspectionClientImpl qualityInspectionClient;

    @Resource
    private QualitySessionMapper qualitySessionMapper;

    @Resource
    private AsrInfoMapper asrInfoMapper;

    public String getToken(String staffId) {
        return qualityInspectionClient.getToken(staffId);
    }

    @Transactional
    public void create(String callId, String staffId, String name, String customerPhone) {
        QualitySession qualitySession = qualitySessionMapper.queryByCallIdQualitySession(callId);
        if (qualitySession != null && "start".equals(name)) {
            return;
        }
        if (qualitySession == null && "end".equals(name)) {
            return;
        }

        if (qualitySession == null) {
            qualitySession = new QualitySession();
            qualitySession.setCallId(callId);
            qualitySession.setStaffId(staffId);
        }

        if ("start".equals(name)) {
            SessionsMeta sessionsMeta = qualityInspectionClient.sessions(callId, staffId, callId + customerPhone.substring(2, 5));
            SessionsMeta signalMeta = qualityInspectionClient.signals(callId, name);
            qualitySession.setSignalStatus(name);
            qualitySession.setIsSession(1);
            qualitySession.setIsSignal(1);
            qualitySession.setCreatedTime(LocalDateTimeUtils.parseLocalDateTimeByDateStr(sessionsMeta.getCreated()));
            qualitySession.setLastModified(LocalDateTimeUtils.parseLocalDateTimeByDateStr(signalMeta.getLastModified()));
            qualitySessionMapper.insert(qualitySession);
        }

        if ("end".equals(name)) {
            SessionsMeta sessionsMeta = qualityInspectionClient.signals(callId, name);
            qualitySession.setSignalStatus(name);
            qualitySession.setLastModified(LocalDateTimeUtils.parseLocalDateTimeByDateStr(sessionsMeta.getLastModified()));
            qualitySessionMapper.updateById(qualitySession);
        }
    }

    public Boolean saveAsrInfo(AsrInfoRequest req) {
        AsrInfo asrInfo = new AsrInfo();
        asrInfo.setInfoId(req.getId());
        asrInfo.setCallId(req.getSourceId());
        asrInfo.setStartTimestamp(req.getStartTimestamp());
        asrInfo.setEndTimestamp(req.getEndTimestamp());
        asrInfo.setText(req.getText());
        asrInfo.setState(req.getState());
        asrInfo.setSpeaker(req.getSpeaker());
        asrInfoMapper.insert(asrInfo);
        return true;
    }
}