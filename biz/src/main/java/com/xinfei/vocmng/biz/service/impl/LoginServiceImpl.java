/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.function.UserValidator;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.LoginSmsReq;
import com.xinfei.vocmng.biz.model.req.SendSmsReq;
import com.xinfei.vocmng.biz.model.resp.DingTalkRedirectResp;
import com.xinfei.vocmng.biz.model.resp.LoginResp;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.biz.service.OuterService;
import com.xinfei.vocmng.biz.util.JwtUtil;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.DepartmentMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.po.Department;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.Role;
import com.xinfei.vocmng.itl.client.feign.impl.QualityInspectionClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.RealTimeQualityFeignService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.OrgData;
import com.xinfei.vocmng.itl.rr.StaffData;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.vqcprod.facade.api.RealTimeQualityFacade;
import com.xinfei.vqcprod.facade.model.rr.VqcRequest;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LoginServiceImpl, v 0.1 2023/12/19 15:46 wancheng.qu Exp $
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OuterService outerService;
    @Resource
    private LoginUserConfig loginUserConfig;
    @Resource
    private EmployeeService employeeService;
    @Resource
    private EmployeeMapper employeeMapper;
    @Resource
    private VocConfig vocConfig;
    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private CisFacadeClientService cisFacadeClientService;
    @Resource
    private QualityInspectionClientImpl qualityInspectionClientImpl;
    @Resource
    private RealTimeQualityFeignService realTimeQualityFeignService;


    private final UserValidator userValidator = UserValidator.defaultValidator();


    @Override
    public Boolean send(SendSmsReq req) {
        try {
            String smsCode = genSmsCode(req.getMobile());
            outerService.sendSms(req.getMobile(), smsCode);
        } catch (Exception e) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGIN_ERROR);
        }
        return true;
    }

    @Override
    public LoginResp loginBySms(LoginSmsReq loginSmsReq) {
        if (loginUserConfig.isLoginCheck()) {
            verifySmsCode(loginSmsReq.getPassport(), loginSmsReq.getCode());
        }
        Employee e = getUserFromMobile(loginSmsReq.getPassport());
        addUserCache(e);
        String jwt = JwtUtil.generateToken(e.getUserIdentify());
        addJwtCache(jwt,e.getUserIdentify());
        Department department = departmentMapper.selectById(e.getDepartmentId());
        Integer departmentType = department == null ? 1 : department.getDepartmentType();
        return new LoginResp(jwt, e.getName(), e.getUserIdentify(), departmentType,e.getId(),e.getRealTimeAssistance(), e.getSsoUserId(), e.getMobile());
    }

    private void addJwtCache(String jwt, String userIdentify) {
        String key = genUserLoginCacheKey(jwt);
        redisUtils.set(key, userIdentify, 43200);
    }

    private UserInfo addUserCache(Employee e) {
        UserInfo info = getUserDetail(e.getUserIdentify());
        String key = genUserCacheKey(e.getUserIdentify());
        redisUtils.set(key, JsonUtil.toJson(info), 43200);
        return info;
    }

    private UserInfo getUserDetail(String userIdentify) {
        Employee e = new Employee();
        e.setUserIdentify(userIdentify);
        Employee employee = employeeService.getEffectiveUser(e);
        if (employee == null) {
            throw new IgnoreException(TechplayErrDtlEnum.DATA_UPDATE_EXCEPTION, "无此账号，userIdentify：" + userIdentify);
        }
        Role role = employeeMapper.getUserWithRolesByUserIdentify(userIdentify);
        List<String> dataAuth = employeeMapper.getUserWithDataByUserIdentify(userIdentify);
        List<String> resourceAuth = employeeMapper.getUserWithResourceByUserIdentify(userIdentify);
        List<ControlData> controlDataList=employeeMapper.getControleData(userIdentify);
        List<FeeStrategyConfig> feeStrategyConfigs = employeeMapper.getFeeStrategyConfig(userIdentify);
        return new UserInfo(userIdentify, role.getName(),role.getId(), dataAuth, resourceAuth, employee.getState(),controlDataList,feeStrategyConfigs, employee.getName());
    }

    public Employee getUserFromMobile(String mobile) {
        Employee e = new Employee();
        if(vocConfig.isWysFlag()){
            e.setMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(mobile));
        }else {
            e.setMobile(mobile);
        }
        Employee employee = employeeService.getEffectiveUser(e);
        userValidator.validate(employee);
        return employee;
    }


    @Override
    public DingTalkRedirectResp authorizeUrl() {
        return new DingTalkRedirectResp(getOauthScanUrl());
    }

    @Override
    public LoginResp loginByDingTalk(String authCode, String state) {
        String mobile = outerService.getOauthUserInfo(authCode);
        if (StringUtils.isEmpty(mobile)) {
            throw new IgnoreException(TechplayErrDtlEnum.DINGTALK_MOBILE_ERROR);
        }
        Employee e = getUserFromMobile(mobile);
        addUserCache(e);
        String jwt = JwtUtil.generateToken(e.getUserIdentify());
        addJwtCache(jwt,e.getUserIdentify());
        Department department = departmentMapper.selectById(e.getDepartmentId());
        Integer departmentType = department == null ? 1 : department.getDepartmentType();
        return new LoginResp(JwtUtil.generateToken(e.getUserIdentify()),e.getName(),e.getUserIdentify(),departmentType, e.getId(), e.getRealTimeAssistance() ,e.getSsoUserId(), e.getMobile());
    }

    @Override
    public Boolean logout() {
        return true;
    }

    @Override
    public UserInfo getUserInfo(String identify) {
        String key = genUserCacheKey(identify);
        UserInfo info = redisUtils.get(key, UserInfo.class);
        if (Objects.isNull(info) || StringUtils.isBlank(info.getUserIdentify())) {
            Employee e = new Employee();
            e.setUserIdentify(identify);
            info = addUserCache(e);
        }
        return info;
    }

    @Override
    public void addUserCache(List<String> identify) {
        if (identify != null) {
            identify.forEach(t -> {
                Employee e = new Employee();
                e.setUserIdentify(t);
                addUserCache(e);
            });
        }
    }

    @Override
    public void updateUserCache(Employee e) {
        addUserCache(e);
    }

    @Override
    public String getUserIdentifyCache(String jwt) {
        String key = genUserLoginCacheKey(jwt);
        return redisUtils.get(key);
    }

    @Override
    public Boolean delRedisKey(String key) {
        redisUtils.del(key);
        return true;
    }

    @Override
    public String syncUserData() {
        StringBuilder sb = new StringBuilder("同步异常用户信息：");
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Employee::getName, Employee::getMobile, Employee::getState, Employee::getUserIdentify);
        queryWrapper.eq(Employee::getIsDeleted, 0);
        queryWrapper.last("LIMIT 1000");
        List<Employee> res = employeeMapper.selectList(queryWrapper);
        res.forEach(
                t ->
                {
                    try {
                        Triple<Boolean, String, Long> pair = cisFacadeClientService.syncUserData(t.getName(), t.getMobile(), t.getUserIdentify(), "root", t.getState());
                        if (!pair.getLeft()) {
                            sb.append(t.getUserIdentify() + "-->").append(pair.getMiddle());
                        }else {
                            employeeService.updateSsoUserId(t.getUserIdentify(), pair.getRight());
                        }
                    } catch (Exception e) {
                        sb.append(t.getUserIdentify() + "-->").append(e.getMessage());
                    }
                }
        );
        return sb.toString();
    }

    @Override
    public void syncQualityStaff() {
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Employee::getName, Employee::getMobile, Employee::getState, Employee::getUserIdentify, Employee::getSsoUserId, Employee::getId, Employee::getDepartmentId);
        queryWrapper.eq(Employee::getIsDeleted, 0);
        queryWrapper.last("LIMIT 1000");
        List<Employee> res = employeeMapper.selectList(queryWrapper);
        log.info(LogUtil.infoLog("syncQualityStaff", res.size()));

        List<StaffData> staffDataList = new ArrayList<>();
        res.forEach(
                t -> {
                    StaffData staffData = new StaffData();
                    staffData.setId(t.getId().toString());
                    staffData.setName(t.getName());
                    staffData.setPhone(t.getMobile());
                    staffData.setDeptId(t.getDepartmentId().toString());
                    staffData.setIsDimission(t.getState() != 0);
                    staffDataList.add(staffData);
                }
        );

        qualityInspectionClientImpl.staffBath(staffDataList);
        realTimeQualityFeignService.staffBath(staffDataList);
        log.info(LogUtil.infoLog("syncQualityStaff", "success"));
    }

    @Override
    public void syncQualityOrg() {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Department::getName, Department::getId, Department::getParentDepartmentId, Department::getDirector);
        queryWrapper.eq(Department::getIsDeleted, 0);
        queryWrapper.last("LIMIT 1000");
        List<Department> res = departmentMapper.selectList(queryWrapper);
        log.info(LogUtil.infoLog("syncQualityOrg", res));

        List<OrgData> orgDataList = new ArrayList<>();
        res.forEach(
                t -> {
                    OrgData orgData = new OrgData();
                    orgData.setId(t.getId().toString());
                    orgData.setName(t.getName());
                    orgData.setParent(t.getParentDepartmentId() == 0 ? "" : t.getParentDepartmentId().toString());
                    orgData.setLeader(t.getDirector());
                    orgDataList.add(orgData);
                }
        );
        qualityInspectionClientImpl.orgBath(orgDataList);
        realTimeQualityFeignService.orgBatch(orgDataList);
        log.info(LogUtil.infoLog("syncQualityOrg", "success"));
    }

    /**
     * 生成短信验证码
     *
     * @param mobile
     */
    private String genSmsCode(String mobile) {
        String codeKey = genSmsCodeKey(mobile);
        String code = RandomUtil.randomNumbers(4);
        redisUtils.set(codeKey, code, 600L);
        return code;
    }

    private String genSmsCodeKey(String mobile) {
        return LoginUserConstants.SMS_CODE_KEY + mobile;
    }

    private String genUserCacheKey(String identify) {
        return LoginUserConstants.USER__KEY + identify;
    }

    private String genUserLoginCacheKey(String jwt) {
        return LoginUserConstants.LOGIN__KEY + jwt;
    }

    public void verifySmsCode(String mobile, String code) {
        String codeKey = genSmsCodeKey(mobile);
        String cacheCode = redisUtils.getString(codeKey);
        if (cacheCode == null) {
            throw new IgnoreException(TechplayErrDtlEnum.NOLOGIN_ERROR);
        }
        if (!code.equalsIgnoreCase(cacheCode)) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGINCODE_ERROR);
        }
        redisUtils.del(codeKey);
    }

    public String getOauthScanUrl() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("redirect_uri", loginUserConfig.getRedirectUrl());
        map.put("response_type", "code");
        map.put("client_id", loginUserConfig.getClientId());
        map.put("scope", "openid");
        map.put("state", RandomUtil.randomNumbers(4));
        map.put("prompt", "consent");
        return "https://login.dingtalk.com/oauth2/auth?" + HttpUtil.toParams(map);
    }


}