/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import com.xinfei.vocmng.biz.model.req.UDeskIncomingRequest;
import com.xinfei.vocmng.biz.model.req.UDeskCallFailedRequest;

/**
 *
 * <AUTHOR>
 * @version $ UDeskService, v 0.1 2024-02-20 16:25 junjie.yan Exp $
 */

public interface UDeskService {

    String setComSummary(SetComSummaryRequest setComSummaryRequest, String name);

    void sessionsSync(String startTime,String endTime);

    void callSync(String startTime,String endTime);

    void robotSync(String startTime,String endTime);

    void robotDetailsSync(String redisKey, String startTime,String endTime);

    void uDeskSyncAll(String redisKey,String startTime,String endTime);


}