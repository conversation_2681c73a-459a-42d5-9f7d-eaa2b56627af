package com.xinfei.vocmng.biz.component;


import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.xinfei.vocmng.biz.config.OssConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.net.URL;
import java.util.Date;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;


/**
 * <AUTHOR> wanghu
 * @since : 2021/10/29 11:05:39
 */
@Component
@Slf4j
public class OssClient {
    private static final Long EXPIRE_TIME = 90 * 24 * 60 * 60L;

    @Autowired
    private OssConfig ossConfig;

    private OSS createOssClient() {
        return new OSSClientBuilder().build(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
    }

    public void upload(String key, InputStream is) {
        OSS ossClient = null;
        try {
            ossClient = createOssClient();
            log.info("oss begin upload key-> {}", key);
            long startTime = System.currentTimeMillis();

            if (!ossClient.doesBucketExist(ossConfig.getBucketName())) {
                log.info("no fund bucketName,create bucketName");
                ossClient.createBucket(ossConfig.getBucketName());
            }

            ObjectMetadata objectMeta = new ObjectMetadata();
            PutObjectResult putObjectResult = ossClient.putObject(ossConfig.getBucketName(), key, is, objectMeta);

            long endTime = System.currentTimeMillis();
            log.info("oss upload tag = {}, path = {}, cost：{} ms", putObjectResult.getETag(), key, endTime - startTime);
        } catch (Exception e) {
            log.error("oss upload path = {}, error", key, e);
            throw new RuntimeException("oss upload error" + e.getMessage());
        } finally {
            closeStream(is);
            try {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (Exception e) {
                log.error("OssUtil.downloadFile close stream error", e);
            }
        }
    }

    public String getOssUrl(String key) {
        String accessUrl = "";
        OSS ossClient = null;
        try {
            ossClient = createOssClient();
            long expireEndTime = System.currentTimeMillis() + EXPIRE_TIME * 1000;
            Date expiration = new Date(expireEndTime);

            if (StringUtils.isNotBlank(key)) {
                URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), key, expiration);
                accessUrl = url.toString().replace("-internal", "");
                log.info("====>>>>getOssUrl url = {}", accessUrl);
            }
        } catch (Exception e) {
            log.error("==>>>>>>   OSS getOssUrl error e = ", e);
        } finally {
            try {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (Exception e) {
                log.error("OssUtil.downloadFile close stream error", e);
            }
        }
        return accessUrl;
    }

    public static void main(String[] args) {
        String s="http://xyf-cdp.oss-cn-beijing-internal.aliyuncs.com/upload/vocmng/2025-03-19/?Expires=1750125614&OSSAccessKeyId=LTAI5tNvR3ypLTWac6m5wMhh&Signature=pJIjz2antdO4uJtJxx36JfH%2FYuU%3D";
        System.out.println(s.replace("-internal", ""));
    }

    public void downloadFile(HttpServletResponse response, String fileName, String key) {
        OSS ossClient = null;
        BufferedInputStream is = null;
        BufferedOutputStream os = null;
        try {
            ossClient = createOssClient();
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), key);
            is = new BufferedInputStream(ossObject.getObjectContent());
            os = new BufferedOutputStream(response.getOutputStream());

            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.error("OssUtil.downloadFile error", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (IOException e) {
                log.error("OssUtil.downloadFile close stream error", e);
            }
        }
    }

    public InputStream getInputStream(String key) {
        OSS ossClient = createOssClient();
        return ossClient.getObject(ossConfig.getBucketName(), key).getObjectContent();
    }

    private void closeStream(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.error("Close stream error", e);
            }
        }
    }
}
