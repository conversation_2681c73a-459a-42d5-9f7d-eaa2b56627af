///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2024 All Rights Reserved.
// */
//package com.xinfei.vocmng.biz.component;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.SchedulingConfigurer;
//import org.springframework.scheduling.config.ScheduledTaskRegistrar;
//import org.springframework.scheduling.support.CronTrigger;
//
///**
// * 还款任务
// *
// * <AUTHOR>
// * @version $ RepaymentTaskManage, v 0.1 2024/3/28 14:10 wancheng.qu Exp $
// */
//@Configuration
//@EnableScheduling
//@Slf4j
//public abstract class QualityStaffTaskManage implements SchedulingConfigurer {
//
//    protected abstract String getStaffCron();
//
//    protected abstract void processStaff();
//
//    @Override
//    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
//
//        taskRegistrar.addTriggerTask(
//                this::processStaff,
//                triggerContext -> {
//                    //2.1 从apollo获取执行周期
//                    String cron = getStaffCron();
//                    //2.2 合法性校验.
//
//                    //2.3 返回执行周期(Date)
//                    return new CronTrigger(cron).nextExecutionTime(triggerContext);
//                }
//        );
//    }
//}