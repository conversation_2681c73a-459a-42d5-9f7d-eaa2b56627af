/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.rr.dto.MemberCardUseInfoDto;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardUsedListRequest;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLog;
import com.xinfei.vocmng.biz.service.CardUsageService;
import com.xinfei.vocmng.biz.service.FeeStrategyService;
import com.xinfei.vocmng.biz.strategy.dto.VipRefundStrategyInput;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ FeeStrategyServiceImpl, v 0.1 2024-12-04 16:16 junjie.yan Exp $
 */
@Slf4j
@Component
public class VipStrategyServiceImpl extends FeeStrategyService<VipRefundStrategyInput> {

    @Resource
    private CardUsageService cardUsageService;



    @Override
    public Map<String, Object> getValue(Map<String, String> dictKeys, VipRefundStrategyInput strategyInput) {

        Map<String, Object> map = new HashMap<>();
        if (strategyInput == null || strategyInput.getVipCardRefundLog() == null) {
            return map;
        }

        VipCardRefundLog vipCardRefundLog = strategyInput.getVipCardRefundLog();
        for (Map.Entry<String, String> dictKey : dictKeys.entrySet()) {
            Object value = null;
            long userNo;
            switch (dictKey.getKey()) {
                //userNo
                case "Black_Production"://是否黑产
                    if (StringUtils.isBlank(vipCardRefundLog.getUserId())) {
                        break;
                    }
                    userNo = Long.parseLong(vipCardRefundLog.getUserId());
                    List<LabelDto> labelDtos = userLabelService.getUserLabel(String.valueOf(userNo));
                    value = !CollectionUtils.isEmpty(labelDtos) &&
                            labelDtos.stream()
                                    .anyMatch(r -> "疑似黑产".equals(r.getName()));
                    break;
                case "First_Loan"://首复贷
                    if (StringUtils.isBlank(vipCardRefundLog.getUserId())) {
                        break;
                    }
                    userNo = Long.parseLong(vipCardRefundLog.getUserId());
                    value = isReLoan(userNo);
                    break;
                case "Average_Amount"://件均
                    if (StringUtils.isBlank(vipCardRefundLog.getUserId())) {
                        break;
                    }
                    userNo = Long.parseLong(vipCardRefundLog.getUserId());
                    value = getOrders(userNo);
                    break;
                //会员卡
                case "Purchase_Days"://购买天数
                    value = ChronoUnit.DAYS.between(LocalDateTimeUtils.parseLocalDateTimeByDateStr(vipCardRefundLog.getCreateTime()), LocalDateTime.now());
                    break;
                //会员卡
                case "Membership_Amount"://会员金额
                    value = vipCardRefundLog.getPayAmount();
                    break;
                //飞享会员
                case "Membership_Type"://卡类型:会员类型 1：月卡，2:连续包月，3:季卡，4:连续包季
                    value = vipCardRefundLog.getVipTerm();
                    break;
                //飞享会员
                case "Validity_Period"://是否有效期内
                    if (StringUtils.isNotBlank(vipCardRefundLog.getEndTime())) {
                        if (LocalDateTimeUtils.isValidDateTimeFormat(vipCardRefundLog.getEndTime())) {
                            value = LocalDateTimeUtils.parseLocalDateTimeByDateStr(vipCardRefundLog.getEndTime()).isAfter(LocalDateTime.now());
                        } else {
                            value = LocalDateTimeUtils.parseLocalDateByDateStr(vipCardRefundLog.getEndTime()).isAfter(LocalDate.now());
                        }
                    }
                    break;
                //飞享会员
                case "Membership_Stage"://会员阶段:订单操作类型：1:首次购买，2:手动续期，3-自动续期，4-手动关闭，5-自动关闭，6-提前续费
                    value = vipCardRefundLog.getOrderBuyType();
                    break;
                case "Customer_Service_Group"://客服角色
                    value = UserContextHolder.getUserContext().getRole();
                    break;
                case "Card_Name"://卡名称
                    value = vipCardRefundLog.getCardName();
                    break;
                case "Loan_Successful"://todo 借款是否成功
                    value = true;
                    break;
                case "Use_Equity"://是否使用权益
                    QueryMemberCardUsedListRequest memberCardUsedListRequest = new QueryMemberCardUsedListRequest();
                    memberCardUsedListRequest.setCardId(vipCardRefundLog.getVipCardId().intValue());
                    memberCardUsedListRequest.setType(vipCardRefundLog.getCardType());
                    if (vipCardRefundLog.getCardType() == 3) {
                        memberCardUsedListRequest.setCardId(vipCardRefundLog.getVipCardId().intValue());
                    }
                    List<MemberCardUseInfoDto> memberCardUseInfoDtos = cardUsageService.queryCardUsedInfo(memberCardUsedListRequest);
                    value = !(memberCardUseInfoDtos.stream()
                            .filter(r -> r.getNum() == 1)
                            .findFirst()
                            .orElse(null) == null);
                    break;
                default:
                    break;
            }
            if (value != null) {
                map.put(dictKey.getKey(), value);
            }
        }

        return map;
    }

    @Override
    public String customizeKeyValues(Map<String, Object> dictKeys, VipRefundStrategyInput strategyInput) {
        return strategyInput.getVipCardRefundLog().getVipCardId().toString();
    }
}