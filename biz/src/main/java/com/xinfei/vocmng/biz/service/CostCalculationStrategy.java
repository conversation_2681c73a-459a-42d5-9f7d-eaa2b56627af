package com.xinfei.vocmng.biz.service;


import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;

import java.math.BigDecimal;

/**     费控计算策略类
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public interface CostCalculationStrategy<T, U, V,M,N> {

    ControlRes<M,N> calculateAmount(ControlItemValue<T, U, V> values, Object... params);

}
