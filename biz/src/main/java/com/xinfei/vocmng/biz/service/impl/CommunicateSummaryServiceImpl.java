/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.Gson;
import apollo.com.google.gson.JsonObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.biz.config.DisplayNumber;
import com.xinfei.vocmng.biz.config.DisplayNumberConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.mapstruct.CommunicateSummaryConverter;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.IncomingSourceEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.CommunicateRemarkCreateReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryCreateReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryDetailReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryDetailRsp;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryRemarkReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.req.EagleEyeDataReq;
import com.xinfei.vocmng.biz.model.req.GetUserNoReq;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryRemarkResp;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.model.resp.EagleEyeDataResp;
import com.xinfei.vocmng.biz.model.resp.RemarkInfo;
import com.xinfei.vocmng.biz.service.AccountService;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.CommunicateSummaryService;
import com.xinfei.vocmng.biz.service.EagleEyeCommunicateSummary;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.OuterService;
import com.xinfei.vocmng.biz.service.UserLabelService;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.biz.workorder.domain.workorder.aggregate.WorkOrderAggregate;
import com.xinfei.vocmng.biz.workorder.domain.workorder.repository.WorkOrderRepository;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.ExtendData;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryRemarkMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.dal.po.CommunicateSummaryRemark;
import com.xinfei.vocmng.dal.po.IssueCategoryConfig;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.FeaturePlatformClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.FeatureQueryClientImpl;
import com.xinfei.vocmng.biz.model.resp.TransferCallInfoResp;
import com.xinfei.vocmng.itl.client.feign.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.rr.haier.QueryTransferCallResponse;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.IdNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryServiceImpl, v 0.1 2023/12/18 15:01 wancheng.qu Exp $
 */

@Slf4j
@Service
public class CommunicateSummaryServiceImpl implements CommunicateSummaryService {


    @Autowired
    private CommunicateSummaryMapper communicateSummaryMapper;
    @Autowired
    private CommunicateSummaryRemarkMapper communicateSummaryRemarkMapper;
    @Autowired
    private EmployeeService employeeService;
    @Resource
    private AccountService accountService;
    @Autowired
    private OuterService outerService;
    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private FeaturePlatformClientImpl featurePlatformClient;

    @Resource
    private FeatureQueryClientImpl featureQueryClientImpl;

    @Resource
    private UserLabelMappingMapper userLabelMappingMapper;

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClientImpl;

    @Resource
    private RedisUtils redisUtils;

    @Autowired
    private VocConfig vocConfig;

    @Resource
    private DisplayNumberConfig displayNumberConfig;

    @Resource
    private CommonService commonService;

    @Resource
    private CustomerServiceImpl customerServiceImpl;

    @Resource
    private EagleEyeCommunicateSummary eagleEyeCommunicateSummary;

    @Resource
    private UserLabelService userLabelService;

    @Resource
    private UserLabelServiceImpl userLabelServiceImpl;

    @Resource
    private WorkOrderRepository workOrderRepository;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    @Override
    public PageResultResponse<CommunicateSummaryResp> list(CommunicateSummaryReq req) {
        LambdaQueryWrapper<CommunicateSummary> wrapper = new LambdaQueryWrapper<>();
        if(CollectionUtils.isNotEmpty(req.getMobiles()) && CollectionUtils.isNotEmpty(req.getUserNos())){
            getCustomerSummaryReqWrapper(req, wrapper);
        }else{
            getSummaryReqWrapper(req, wrapper);
        }

        IPage<CommunicateSummary> page = new Page<>(req.getCurrentPage(), req.getPageSize());

        if (wrapper.isEmptyOfWhere()) {
            return new PageResultResponse<>();
        }

        IPage<CommunicateSummary> pageList = communicateSummaryMapper.selectPage(page, wrapper);

        if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<Long> idList = pageList.getRecords().stream()
                    .map(CommunicateSummary::getId)
                    .collect(Collectors.toList());
            Map<Long, Boolean> m = getWork(idList);
            Set<String> teleList = pageList.getRecords().stream()
                    .flatMap(dto -> Stream.of(dto.getTelephoneEncrypted(), dto.getCallBackMobileEncrypted()))
                    .collect(Collectors.toSet());
            Map<String, String> desMap = cisFacadeClientService.batchDecrypt(new ArrayList<>(teleList));
            List<CommunicateSummaryResp> res = pageList.getRecords().stream().map(
                    t -> {
                        CommunicateSummaryResp cr = CommunicateSummaryConverter.INSTANCE.communicateSummaryToResp(t);
                        cr.setRemarkInfoList(getRemarkAllForList(t.getId()));
                        cr.setIssueCategoryLv1(getIss(t.getIssueCategoryLv1()));
                        cr.setIssueCategoryLv2(getIss(t.getIssueCategoryLv2()));
                        cr.setIssueCategoryLv3(getIss(t.getIssueCategoryLv3()));
                        cr.setHasWork(m.get(t.getId()));
                        cr.setCreateUser(employeeService.getUserNameForIdentify(t.getCreateUserIdentify()));
                        setMobileAndApp(cr, t);
                        if (vocConfig.isWysFlag()) {
                            cr.setTelephone(desMap.get(t.getTelephoneEncrypted()));
                            cr.setCallBackMobile(desMap.get(t.getCallBackMobileEncrypted()));
                        }
                        if (StringUtils.isNotBlank(t.getCustNo())) {
                            IdNoDTO idNoDTO = cisFacadeClient.queryIdNoByCustNo(t.getCustNo());
                            if (idNoDTO != null) {
                                cr.setName(cisFacadeClient.queryIdNoByCustNo(t.getCustNo()).getName());
                            }
                        } else if (t.getUserId() != null) {
                            ThreeElementsDTO threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(t.getUserId());
                            if (threeElementsDTO != null) {
                                cr.setName(threeElementsDTO.getName());
                            }
                        }
                        return cr;
                    }
            ).collect(Collectors.toList());
            return new PageResultResponse<>(res, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());

        }
        return new PageResultResponse<>();
    }

    @Override
    public String getUserNo(GetUserNoReq req) {
        UserNoDTO userNoDTO = cisFacadeClient.getUserNoByMobileAndApp(req.getMobile(), req.getApp());
        if (userNoDTO != null && userNoDTO.getUserNo() != null) {
            return userNoDTO.getUserNo().toString();
        }
        return "";
    }

    private void setMobileAndApp(CommunicateSummaryResp cr, CommunicateSummary t) {
        if (t.getUserId() != null) {
            MobileDTO mobileDTO = cisFacadeClient.queryMobileByUserNo(t.getUserId());
            cr.setMobile(mobileDTO.getMobile());
            cr.setApp(mobileDTO.getApp());
        }
    }

    public List<String> getUserIdentify(List<String> userNames) {
        return employeeMapper.getUserIdentifyByUserName(userNames);
    }

    private void getSummaryReqWrapper(CommunicateSummaryReq req, LambdaQueryWrapper<CommunicateSummary> wrapper) {
        List<Long> userNos = null;
        if (StringUtils.isNotEmpty(req.getMobile())) {
            PageResult<UserSearchDTO> result = cisFacadeClient.queryUserList(req.getMobile(), null, null, 1, 30);
            if (CollectionUtils.isNotEmpty(result.getList())) {
                userNos = result.getList().stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList());
            }

            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(req.getMobileCust());
            List<Long> userNosByCustNo = custNoUsers.stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(userNos)) {
                userNos.addAll(userNosByCustNo);
            } else {
                userNos = userNosByCustNo;
            }
            // 去重
            userNos = userNos.stream().distinct().collect(Collectors.toList());

        }

        wrapper.in(CollectionUtils.isNotEmpty(userNos), CommunicateSummary::getUserId, userNos);

        if (CollectionUtils.isNotEmpty(req.getCreateUserNames())) {
            req.setCreateUserIdentify(getUserIdentify(req.getCreateUserNames()));
        }

        wrapper.eq(Objects.nonNull(req.getId()), CommunicateSummary::getId, req.getId());
        wrapper.eq(Objects.nonNull(req.getUserId()), CommunicateSummary::getUserId, req.getUserId());
        wrapper.eq(StringUtils.isNotBlank(req.getCustNo()), CommunicateSummary::getCustNo, req.getCustNo());
        if (!StringUtils.isEmpty(req.getTelephone())) {
            Set<String> tels = new HashSet<>();
            tels.add(req.getTelephone());
            tels.addAll(getAllTel(req.getTelephone()));
            if (vocConfig.isWysFlag()) {
                wrapper.in(CommunicateSummary::getTelephoneEncrypted, cisFacadeClientService.getEncodeMobileLocal(new ArrayList<>(tels)));
            } else {
                wrapper.in(CommunicateSummary::getTelephone, tels);
            }
        }
        wrapper.eq(Objects.nonNull(req.getStatus()), CommunicateSummary::getStatus, req.getStatus());
        wrapper.eq(Objects.nonNull(req.getSource()), CommunicateSummary::getSource, req.getSource());
        wrapper.eq(Objects.nonNull(req.getIssueCategoryLv1()), CommunicateSummary::getIssueCategoryLv1, req.getIssueCategoryLv1());
        wrapper.eq(Objects.nonNull(req.getIssueCategoryLv2()), CommunicateSummary::getIssueCategoryLv2, req.getIssueCategoryLv2());
        wrapper.eq(Objects.nonNull(req.getIssueCategoryLv3()), CommunicateSummary::getIssueCategoryLv3, req.getIssueCategoryLv3());
        wrapper.in(CollectionUtils.isNotEmpty(req.getCreateUserIdentify()), CommunicateSummary::getCreateUserIdentify, req.getCreateUserIdentify());
        wrapper.ge(Objects.nonNull(req.getStartTime()), CommunicateSummary::getCreatedTime, req.getStartTime());
        wrapper.le(Objects.nonNull(req.getEndTime()), CommunicateSummary::getCreatedTime, req.getEndTime());
        wrapper.eq(Objects.nonNull(req.getOrderNo()), CommunicateSummary::getOrderNo, req.getOrderNo());
        wrapper.eq(Objects.nonNull(req.getQuestionType()), CommunicateSummary::getQuestionType, req.getQuestionType());
        if (wrapper.isEmptyOfWhere()) {
            return;
        }
        wrapper.eq(CommunicateSummary::getIsDeleted, 0);
        wrapper.orderByDesc(CommunicateSummary::getCreatedTime);
    }

    private void getCustomerSummaryReqWrapper(CommunicateSummaryReq req, LambdaQueryWrapper<CommunicateSummary> wrapper) {
        if (vocConfig.isWysFlag()) {
            List<String> mobilesEncrypted = req.getMobiles().stream()
                    .map(cisFacadeClientService::getEncodeMobileLocal)
                    .collect(Collectors.toList());
            wrapper.and(w -> w.in(CommunicateSummary::getTelephoneEncrypted, mobilesEncrypted)
                    .or().in(CommunicateSummary::getUserId, req.getUserNos()));
        } else {
            wrapper.and(w -> w.in(CommunicateSummary::getTelephone, req.getMobiles())
                    .or().in(CommunicateSummary::getUserId, req.getUserNos()));
        }
        wrapper.eq(CommunicateSummary::getIsDeleted, 0);
        wrapper.eq(CommunicateSummary::getStatus, 3);
    }

    @Override
    public CommunicateSummaryDetailRsp detail(CommunicateSummaryDetailReq detailReq) {
        //删除 redis key
        try {
            if (StringUtils.isNotEmpty(detailReq.getMesgId())) {
                log.info("sse key del,key={}", detailReq.getMesgId());
                redisUtils.del(detailReq.getMesgId());
            }
        } catch (Exception e) {
            log.error("redis deleted failed,key:{}", detailReq.getMesgId(), e);
        }

        CommunicateSummaryDetailRsp communicateSummaryDetailRsp = new CommunicateSummaryDetailRsp();

        //获取会话小结
        CommunicateSummary communicateSummary = communicateSummaryMapper.selectById(detailReq.getSummaryId());
        if (vocConfig.isWysFlag()) {
            communicateSummary.setTelephone(cisFacadeClientService.batchDecrypt(communicateSummary.getTelephoneEncrypted()));
            communicateSummary.setCallBackMobile(cisFacadeClientService.batchDecrypt(communicateSummary.getCallBackMobileEncrypted()));
        }
        communicateSummaryDetailRsp.setCommunicateSummary(communicateSummary);

        //获取工单
        communicateSummaryDetailRsp.setTasks(workOrderFeignClientImpl.getTaskByMobile(detailReq.getTelephone()));

        //查询WorkOrderAggregate，获取extendData中的转接来电信息
        try {
            Optional<WorkOrderAggregate> workOrderAggregateOpt = workOrderRepository.findBySummaryId(detailReq.getSummaryId());
            if (workOrderAggregateOpt.isPresent()) {
                WorkOrderAggregate workOrderAggregate = workOrderAggregateOpt.get();
                if (Objects.nonNull(workOrderAggregate.getWorkOrderData()) && Objects.nonNull(workOrderAggregate.getWorkOrderData().getExtendData())) {
                    TransferCallInfoResp transferCallInfo = extractTransferCallInfo(workOrderAggregate, workOrderAggregate.getWorkOrderData().getExtendData());
                    communicateSummaryDetailRsp.setTransferCallInfo(transferCallInfo);
                }
            }
        } catch (Exception e) {
            log.error("查询WorkOrderAggregate失败, summaryId: {}", detailReq.getSummaryId(), e);
        }

        //获取5条会话小结
        List<CommunicateSummary> communicateSummaries = new ArrayList<>();
        if (vocConfig.isWysFlag()) {
            communicateSummaries = getEncryptFiveSummaries(detailReq.getTelephone(), detailReq.getSummaryId());
        } else {
            communicateSummaries = communicateSummaryMapper.getFiveSummaries(detailReq.getTelephone(), detailReq.getSummaryId());
        }

        List<CommunicateSummaryResp> res = communicateSummaries.stream().map(
                t -> {
                    CommunicateSummaryResp cr = new CommunicateSummaryResp();
                    cr.setId(t.getId());
                    cr.setTelephone(t.getTelephone());
                    cr.setSource(IncomingSourceEnum.getDescByCode(t.getSource()));
                    cr.setRemark(getRemarkOne(t.getId()));
                    cr.setIssueCategoryLv1(getIss(t.getIssueCategoryLv1()));
                    cr.setIssueCategoryLv2(getIss(t.getIssueCategoryLv2()));
                    cr.setIssueCategoryLv3(getIss(t.getIssueCategoryLv3()));
                    cr.setCreatedTime(t.getCreatedTime());
                    if (t.getUserId() != null) {
                        cr.setUserNo(t.getUserId().toString());
                        MobileDTO mobileDTO = cisFacadeClient.queryMobileByUserNo(t.getUserId());
                        cr.setMobile(mobileDTO.getMobile());
                        cr.setApp(mobileDTO.getApp());
                    }
                    if (vocConfig.isWysFlag()) {
                        cr.setTelephone(cisFacadeClientService.batchDecrypt(t.getTelephoneEncrypted()));
                    }
                    return cr;
                }
        ).collect(Collectors.toList());
        communicateSummaryDetailRsp.setCommunicateSummaries(res);

        //中继号名称
        if (StringUtils.isNotEmpty(communicateSummary.getRelayNumber())) {
            List<JsonObject> udeskDisplayNumbers = displayNumberConfig.getUdeskDisplayNumber();
            List<DisplayNumber> udeskDisplayNumberList = new ArrayList<>();
            for (JsonObject jsonObject : udeskDisplayNumbers) {
                DisplayNumber displayNumber = new Gson().fromJson(jsonObject, DisplayNumber.class);
                udeskDisplayNumberList.add(displayNumber);
            }
            udeskDisplayNumberList = udeskDisplayNumberList.stream().filter(r -> communicateSummary.getRelayNumber().equals(r.getNumber())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(udeskDisplayNumberList)) {
                communicateSummaryDetailRsp.setDisplayNumberName(udeskDisplayNumberList.get(0).getName() + communicateSummary.getRelayNumber());
            }
        }

        return communicateSummaryDetailRsp;
    }

    private List<CommunicateSummary> getEncryptFiveSummaries(String telephone, Long summaryId) {
        String enctelephone = cisFacadeClientService.getEncodeMobileLocal(telephone);
        LambdaQueryWrapper<CommunicateSummary> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommunicateSummary::getTelephoneEncrypted, enctelephone)
                .eq(CommunicateSummary::getIsDeleted, 0)
                .ne(CommunicateSummary::getId, summaryId)
                .orderByDesc(CommunicateSummary::getCreatedTime)
                .last("LIMIT 5");
        return communicateSummaryMapper.selectList(queryWrapper);
    }

    private List<String> getAllTel(String mobile) {
        PageResult<UserSearchDTO> result = cisFacadeClient.queryUserList(mobile, null, null, 1, 30);
        if (!CollectionUtils.isEmpty(result.getList())) {
            return result.getList().stream().map(UserSearchDTO::getMobile).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    private Map<Long, Boolean> getWork(List<Long> ids) {
        return outerService.getWork(ids);
    }

    public String getIss(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        IssueCategoryConfig is = new IssueCategoryConfig();
        is.setId(id);
        List<IssueCategoryConfig> res = accountService.queryIssList(is);
        if (CollectionUtils.isNotEmpty(res)) {
            return res.get(0).getName();
        }
        return null;
    }

    @Override
    public EagleEyeDataResp getEagleEyeData(EagleEyeDataReq req) {
        EagleEyeDataResp eagleEyeDataResp = new EagleEyeDataResp();

        LocalDateTime nowStartDate = req.getNowDate().atStartOfDay(); // 当天 00:00:00
        LocalDateTime nowEndDate = nowStartDate.plusDays(1);

        LocalDateTime periodStartDate = req.getPeriodDate().atStartOfDay();
        LocalDateTime periodEndDate = periodStartDate.plusDays(1);

        List<CommunicateSummary> nowCommunicateSummaries = communicateSummaryMapper.getEagleEyeData(req.getSourceList(), req.getCreateUserIdentifyList(), req.getStatusList(), nowStartDate, nowEndDate);
        List<CommunicateSummary> periodCommunicateSummaries = communicateSummaryMapper.getEagleEyeData(req.getSourceList(), req.getCreateUserIdentifyList(), req.getStatusList(), periodStartDate, periodEndDate);

        eagleEyeDataResp.setHourlyData(eagleEyeCommunicateSummary.countHourlyData(nowCommunicateSummaries, periodCommunicateSummaries));
        eagleEyeDataResp.setSourceAndStatus(eagleEyeCommunicateSummary.countSourceAndStatus(nowCommunicateSummaries));
        eagleEyeDataResp.setUserPerHour(eagleEyeCommunicateSummary.countUserPerHour(nowCommunicateSummaries));
        eagleEyeDataResp.setCategoryNodes(eagleEyeCommunicateSummary.hierarchicalCategoryStats(nowCommunicateSummaries, periodCommunicateSummaries));

        return eagleEyeDataResp;
    }

    private String getRemarkOne(Long id) {
        CommunicateSummaryRemarkReq cq = new CommunicateSummaryRemarkReq();
        cq.setCommunicateSummaryId(id);
        PageResultResponse<CommunicateSummaryRemarkResp> res = remarkList(cq);
        if (CollectionUtils.isNotEmpty(res.getList())) {
            return res.getList().get(0).getRemark();
        }
        return null;
    }

    /**
     * 获取最新一条备注信息，返回RemarkInfo对象，用于detail接口
     */
    private List<RemarkInfo> getRemarkOneForDetail(Long id) {
        CommunicateSummaryRemarkReq cq = new CommunicateSummaryRemarkReq();
        cq.setCommunicateSummaryId(id);
        cq.setCurrentPage(1);
        cq.setPageSize(1); // 只获取最新一条
        PageResultResponse<CommunicateSummaryRemarkResp> res = remarkList(cq);
        if (CollectionUtils.isNotEmpty(res.getList())) {
            CommunicateSummaryRemarkResp remarkResp = res.getList().get(0);
            return Collections.singletonList(new RemarkInfo(
                    remarkResp.getRemark(),
                    remarkResp.getCreatedTime(),
                    remarkResp.getCreateUser()
            ));
        }
        return null;
    }

    /**
     * 获取所有备注信息（限制10条），包含备注内容和创建时间，专门用于list接口
     */
    private List<RemarkInfo> getRemarkAllForList(Long id) {
        CommunicateSummaryRemarkReq cq = new CommunicateSummaryRemarkReq();
        cq.setCommunicateSummaryId(id);
        cq.setCurrentPage(1);
        cq.setPageSize(10); // 限制10条展示
        PageResultResponse<CommunicateSummaryRemarkResp> res = remarkList(cq);
        if (CollectionUtils.isNotEmpty(res.getList())) {
            // 将备注转换为RemarkInfo对象列表
            return res.getList().stream()
                    .map(remarkResp -> new RemarkInfo(
                            remarkResp.getRemark(),
                            remarkResp.getCreatedTime(),
                            remarkResp.getCreateUser()
                    ))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional
    public Long create(CommunicateSummaryCreateReq req) {
        CommunicateSummary cr = CommunicateSummaryConverter.INSTANCE.reqToCommunicateSummary(req);
        if (!StringUtils.isEmpty(req.getTelephone())) {
            try {
                setBlackProduct(cr, req.getTelephone());
            } catch (Exception e) {
                log.warn("setBlackProduct failed,mobile={}", req.getTelephone());
            }
            if (vocConfig.isWysFlag()) {
                cr.setTelephoneEncrypted(cisFacadeClientService.getEncodeMobileLocal(req.getTelephone()));
            }
        }
        if (StringUtils.isNotBlank(req.getCallBackMobile()) && vocConfig.isWysFlag()) {
            cr.setCallBackMobileEncrypted(cisFacadeClientService.getEncodeMobileLocal(req.getCallBackMobile()));
        }

        if (StringUtils.isNotEmpty(req.getOrderNo())) {
            if ("T".equals(vocConfig.getIsValidOrderNo())) {
                if (isValidOrderNo(req.getOrderNo())) {
                    cr.setOrderNo(req.getOrderNo());
                } else {
                    throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "此订单号查不到订单");
                }
            } else {
                cr.setOrderNo(req.getOrderNo());
            }
        }

        if (Objects.nonNull(req.getUserId()) && StringUtils.isBlank(req.getCustNo())) {
            //查询cis获取custno
            cr.setCustNo(getCustNo(req.getUserId()));
        } else {
            cr.setCustNo(req.getCustNo());
        }

        //获取当前用户所有标签
        if (cr.getUserId() != null) {
            List<LabelDto> userLabels = userLabelService.getUserLabel(String.valueOf(cr.getUserId()));
            List<String> labelNames = userLabels.stream()
                    .map(LabelDto::getName)
                    .collect(Collectors.toList());
            cr.setLabelList(JsonUtil.toJson(labelNames));
        }

        if (StringUtils.isEmpty(req.getCreateUserIdentify())) {
            cr.setCreateUserIdentify(UserContextHolder.getUserContext().getUserIdentify());
        }

        if (StringUtils.isNotBlank(req.getVipOrderNo())) {
            cr.setVipOrderNo(req.getVipOrderNo());
        }

        if (StringUtils.isNotBlank(req.getSVipOrderNo())) {
            cr.setSVipOrderNo(req.getSVipOrderNo());
        }

        if (req.getId() == null) {
            cr.setCreatedTime(LocalDateTime.now());
            cr.setUpdatedTime(LocalDateTime.now());
            communicateSummaryMapper.insert(cr);
        } else {
            CommunicateSummary communicateSummary = communicateSummaryMapper.selectById(req.getId());
            if (communicateSummary != null && communicateSummary.getStatus() != 3) {
                cr.setUpdatedTime(LocalDateTime.now());
                communicateSummaryMapper.updateById(cr);
            }
        }

        if (StringUtils.isNotBlank(req.getRemark()) && Objects.nonNull(cr.getId())) {
            CommunicateSummaryRemark crm = new CommunicateSummaryRemark();
            crm.setCommunicateSummaryId(cr.getId());
            crm.setRemark(req.getRemark());
            crm.setCreateUserIdentify(cr.getCreateUserIdentify());
            crm.setCreatedTime(LocalDateTime.now());
            crm.setUpdatedTime(LocalDateTime.now());
            communicateSummaryRemarkMapper.insert(crm);
        }

        return cr.getId();
    }

    private Boolean isValidOrderNo(String orderNo) {
        ManageOrderDetailRequest request = new ManageOrderDetailRequest();
        request.setOrderNo(orderNo);
        ManageOrderDetailDTO manageOrderDetailDTO = lendQueryFacadeClient.getOrderDetail(request);
        return !Objects.isNull(manageOrderDetailDTO);
    }

    private void setBlackProduct(CommunicateSummary cr, String tel) {
        String reason = userLabelServiceImpl.getCode(tel, "phone_is_black_industry_hlevel_type");
        if (StringUtils.isEmpty(reason) || reason.equals("n")) {
            cr.setSusBlackMarket(0);
        } else {
            cr.setSusBlackMarket(1);
            cr.setBlackMarketReason(reason);
        }
    }

    private String getCustNo(Long userNo) {
        try {
            return cisFacadeClient.queryCustNoByUserNo(userNo).getCustNo();
        } catch (Exception e) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_CUSTNO_ERROR);
        }
    }

    @Override
    public PageResultResponse<CommunicateSummaryRemarkResp> remarkList(CommunicateSummaryRemarkReq req) {
        if (Objects.isNull(req.getCommunicateSummaryId())) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_SUMMARY_ERROR);
        }
        IPage<CommunicateSummaryRemark> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        LambdaQueryWrapper<CommunicateSummaryRemark> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(req.getCommunicateSummaryId()), CommunicateSummaryRemark::getCommunicateSummaryId, req.getCommunicateSummaryId());
        wrapper.eq(CommunicateSummaryRemark::getIsDeleted, 0);
        wrapper.orderByDesc(CommunicateSummaryRemark::getCreatedTime);
        IPage<CommunicateSummaryRemark> pageList = communicateSummaryRemarkMapper.selectPage(page, wrapper);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<CommunicateSummaryRemarkResp> res = pageList.getRecords().stream().map(t -> {
                CommunicateSummaryRemarkResp cr = new CommunicateSummaryRemarkResp();
                cr.setCommunicateSummaryId(t.getCommunicateSummaryId());
                cr.setRemark(t.getRemark());
                cr.setCreatedTime(t.getCreatedTime());
                cr.setCreateUser(employeeService.getUserNameForIdentify(t.getCreateUserIdentify()));
                return cr;
            }).collect(Collectors.toList());
            return new PageResultResponse<>(res, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        }
        return new PageResultResponse<>();
    }

    @Override
    @Transactional
    public Boolean createRemark(CommunicateRemarkCreateReq req) {
        String userIdentify = UserContextHolder.getUserContext().getUserIdentify();
        CommunicateSummaryRemark cr = new CommunicateSummaryRemark();
        cr.setCommunicateSummaryId(req.getCommunicateSummaryId());
        cr.setRemark(req.getRemark());
        cr.setCreateUserIdentify(userIdentify);
        cr.setCreatedTime(LocalDateTime.now());
        cr.setUpdatedTime(LocalDateTime.now());
        communicateSummaryRemarkMapper.insert(cr);
        return true;
    }

    /**
     * 从WorkOrderAggregate和ExtendData中提取TransferCallInfoResp
     */
    private TransferCallInfoResp extractTransferCallInfo(WorkOrderAggregate workOrderAggregate, ExtendData extendData) {
        if (Objects.isNull(workOrderAggregate) || Objects.isNull(extendData) || extendData.isEmpty()) {
            return null;
        }

        try {
            TransferCallInfoResp.TransferCallInfoRespBuilder builder = TransferCallInfoResp.builder();
            if (Objects.nonNull(workOrderAggregate.getWorkOrder())) {
                builder.funderCode(workOrderAggregate.getWorkOrder().getFunderCode());
            }
            // 从ExtendData中提取其他字段
            String telPhone = extendData.getStringAttribute("telPhone");
            if (StringUtils.isNotBlank(telPhone)) {
                builder.telPhone(telPhone);
            }
            String callTime = extendData.getStringAttribute("callTime");
            if (StringUtils.isNotBlank(callTime)) {
                builder.callTime(callTime);
            }
            String channelType = extendData.getStringAttribute("channelType");
            if (StringUtils.isNotBlank(channelType)) {
                builder.channelType(channelType);
            }
            String callTransferRemark = extendData.getStringAttribute("callTransferRemark");
            if (StringUtils.isNotBlank(callTransferRemark)) {
                builder.callTransferRemark(callTransferRemark);
            }
            return builder.build();
        } catch (Exception e) {
            log.error("从WorkOrderAggregate提取TransferCallInfoResp失败", e);
            return null;
        }
    }

    /**
     * 从ExtendData中提取QueryTransferCallResponse
     */
    private QueryTransferCallResponse extractQueryTransferCallResponse(ExtendData extendData) {
        if (Objects.isNull(extendData) || extendData.isEmpty()) {
            return null;
        }

        try {
            // 直接将ExtendData的attributes转换为JSON，然后反序列化为QueryTransferCallResponse
            String json = JsonUtil.toJson(extendData.getAttributes());
            return JsonUtil.parseJson(json, QueryTransferCallResponse.class);
        } catch (Exception e) {
            log.error("从ExtendData提取QueryTransferCallResponse失败", e);
            return null;
        }
    }
}