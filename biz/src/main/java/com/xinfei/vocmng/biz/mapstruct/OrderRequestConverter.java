/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.repaytrade.facade.rr.dto.FeeAmountDto;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentsByLoanNoResponse;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDto;
import com.xinfei.vocmng.biz.rr.request.GetOrderListRequest;
import io.kyoto.pillar.lcs.loan.domain.PayPlanDomain;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface OrderRequestConverter {

    OrderRequestConverter INSTANCE = Mappers.getMapper(OrderRequestConverter.class);

    @Mapping(source = "currentPage", target = "pageNo")
    ManageOrderListRequest requestToManageOrderList(GetOrderListRequest getOrderListRequest);

    @Mapping(source = "loanReqNo", target = "orderNo")
    @Mapping(source = "loanAmt", target = "loanAmount")
    @Mapping(source = ".", target = "capitalPool", qualifiedByName = "getCapitalPool")
    OrderDto manageOrderRespToOrderDto(ManageOrderDetailDTO manageOrderDetailDTO);

    @Named("getCapitalPool")
    static String getCapitalPool(ManageOrderDetailDTO manageOrderDetailDTO) {
        if (manageOrderDetailDTO.getOrgName() == null) {
            manageOrderDetailDTO.setOrgName("");
        }

        if (manageOrderDetailDTO.getFundSource() == null) {
            manageOrderDetailDTO.setFundSource("");
        }

        return manageOrderDetailDTO.getOrgName() + "@" +manageOrderDetailDTO.getFundSource();
    }

    @AfterMapping
    default void filterSameOrderNo(ManageOrderDetailDTO manageOrderDetailDTO, @MappingTarget OrderDto orderDto) {
        orderDto.setSubOrders(filterOrderDto(orderDto.getSubOrders(), manageOrderDetailDTO.getLoanReqNo()));
    }

    default List<OrderDto> filterOrderDto(List<OrderDto> orderDtos, String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(orderDtos)) {
            return new ArrayList<>();
        }

        return orderDtos.stream()
                .filter(r -> !orderNo.equals(r.getOrderNo()))
                .collect(Collectors.toList());
    }

    RepaymentsDto repaymentInfoToRepaymentDto(QueryRepaymentsByLoanNoResponse.RepaymentInfo repaymentInfo);

    @AfterMapping
    default void mapCreatedBy(QueryRepaymentsByLoanNoResponse.RepaymentInfo repaymentInfo, @MappingTarget RepaymentsDto repaymentsDto) {
        // 操作人映射：cashiercore -> 收银台还款
        if ("cashiercore".equals(repaymentInfo.getCreatedBy())) {
            repaymentsDto.setCreatedBy("收银台还款");
        }
    }

    RepayFeeDetailDto feeAmountDtoToRepayFeeDetail(FeeAmountDto feeAmountDto);

    @AfterMapping
    default void getLateFee(FeeAmountDto feeAmountDto, @MappingTarget RepayFeeDetailDto repayFeeDetailDto) {
        repayFeeDetailDto.setLateFee(feeAmountDto.getTransOint().add(feeAmountDto.getTransFee3()).add(feeAmountDto.getTransFee6()));
        repayFeeDetailDto.setGuaranteeFee(feeAmountDto.getTransFee1().add(feeAmountDto.getTransFee2()));
    }

    DeductionInfoDto deductionInfoToDeductionInfoDto(QueryRepaymentsByLoanNoResponse.RepaymentInfo.DeductionInfo deductionInfo);

    @AfterMapping
    default void mapCardInfo(QueryRepaymentsByLoanNoResponse.RepaymentInfo.DeductionInfo deductionInfo, @MappingTarget DeductionInfoDto deductionInfoDto) {
        // 是否卡单字段：根据push_system判断
        if (StringUtils.equals("02", deductionInfo.getStatus()) && deductionInfo.getPushSystem() != null) {
            switch (deductionInfo.getPushSystem()) {
                case "fund":
                    deductionInfoDto.setIsCardOrder("资金卡单");
                    break;
                case "pay":
                    deductionInfoDto.setIsCardOrder("支付卡单");
                    break;
                default:
                    deductionInfoDto.setIsCardOrder("--");
                    break;
            }
        } else {
            deductionInfoDto.setIsCardOrder("--");
        }
    }

    BillDto payPlanDomainToBillDto(PayPlanDomain payPlanDomain);

    PlanDto planResponseToPlanDto(PlanResponse planResponse);

    @Mapping(source = "term", target = "totalTerms")
    @Mapping(target = "planList", ignore = true)
    LoanPlanDto loanPlanResponseToLoanPlanDto(LoanPlanResponse loanPlanResponse);

    default PlanDetailDto planDetailToPlanDetailDto(PlanResponse.PlanDetail planDetail) {
        if (planDetail == null) {
            return null;
        }

        PlanDetailDto planDetailDto = new PlanDetailDto();

        planDetailDto.setPrinAmt(planDetail.getPrinAmt());
        planDetailDto.setIntAmt(planDetail.getIntAmt());
        planDetailDto.setFee1Amt(planDetail.getFee1Amt());
        planDetailDto.setFee2Amt(planDetail.getFee2Amt());
        planDetailDto.setOintAmt(planDetail.getOintAmt());
        planDetailDto.setFee3Amt(planDetail.getFee3Amt());
        planDetailDto.setFee4Amt(planDetail.getFee4Amt());
        planDetailDto.setFee6Amt(planDetail.getFee6Amt());
        planDetailDto.setSumAmt(planDetail.getSumAmt());

        planDetailDto.setGuaranteeFee(planDetail.getFee1Amt().add(planDetail.getFee2Amt()));
        planDetailDto.setLateFee(planDetail.getOintAmt().add(planDetail.getFee3Amt().add(planDetail.getFee6Amt())));

        return planDetailDto;
    }

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }
}