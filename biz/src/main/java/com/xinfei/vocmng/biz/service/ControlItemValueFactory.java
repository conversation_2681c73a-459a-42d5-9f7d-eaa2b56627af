/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.enums.ControlEnum;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ ControlItemValueFactory, v 0.1 2024/3/27 18:41 wancheng.qu Exp $
 */

// 控制项值解析工厂
@Component
public class ControlItemValueFactory {
    private final ObjectMapper mapper;

    public ControlItemValueFactory() {
        this.mapper = new ObjectMapper();
    }

    public ControlItemValue<?, ?, ?> create(String json, ControlEnum controlEnum) throws IOException {
        switch (controlEnum) {
            case GUARANTEE_REDUCTION:
            case PENALTY_REDUCTION:
            case PRINCIPAL_AMOUNT:
            case INTEREST_AMOUNT:
            case PREPAYMENT_FEE:
            case DEDUCTION_AMOUNT:
            case PROFIT_PRINCIPAL_REDUCTION:
            case VIP_CARD_REFUND_AMOUNT:
            case RENEW_CARD_REFUND_AMOUNT:
                return mapper.readValue(json, new TypeReference<ControlItemValue<BigDecimal, BigDecimal, BigDecimal>>() {
                });
            case DEDUCTION_POOL_RANGE:
                return mapper.readValue(json, new TypeReference<ControlItemValue<Integer, Integer, Integer>>() {
                });
            default:
                throw new IllegalArgumentException("Invalid ControlEnum: " + controlEnum);
        }
    }
}
