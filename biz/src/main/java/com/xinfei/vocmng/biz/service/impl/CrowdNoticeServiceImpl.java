package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.model.enums.CrowdNoticeMessageTypeEnum;
import com.xinfei.vocmng.biz.rr.dto.CrowdLabelDto;
import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;
import com.xinfei.vocmng.biz.rr.request.CrowdNoticeMsg;
import com.xinfei.vocmng.biz.service.CrowdFileParseService;
import com.xinfei.vocmng.biz.service.CrowdNoticeService;
import com.xinfei.vocmng.biz.service.CrowdRefundService;
import com.xinfei.vocmng.dal.mapper.CrowdLabelMapper;
import com.xinfei.vocmng.dal.po.CrowdLabel;
import com.xinfei.vocmng.itl.client.feign.impl.RandomGeneratorClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;

/**
 * 人群通知服务实现
 *
 * <AUTHOR>
 * @version $ CrowdNoticeServiceImpl, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Service
@Slf4j
public class CrowdNoticeServiceImpl implements CrowdNoticeService {

    @Autowired
    private CrowdFileParseService crowdFileParseService;

    @Autowired
    private CrowdRefundService crowdRefundService;

    @Resource
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Resource
    private CrowdLabelMapper crowdLabelMapper;

    @Resource
    private VocConfig vocConfig;

    private final Gson gson = new Gson();
    private final Type typeListString = new TypeToken<List<Long>>() {
    }.getType();
    private final Type typeMapString = new TypeToken<Map<String, String>>() {
    }.getType();

    // 缓存配置
    private volatile List<Long> crowdIdList;
    private volatile Map<String, String> crowdIdMap;
    private volatile Map<String, String> groupMap;

    private boolean initConfig() {
        try {
            crowdIdList = gson.fromJson(vocConfig.AUTO_WORK_LABEL_CROWDID, typeListString);
            crowdIdMap = gson.fromJson(vocConfig.AUTO_WORK_LABEL_CROWDID_MAP, typeMapString);
            groupMap = gson.fromJson(vocConfig.AUTO_WORK_LABEL_CROWDID_GROUP, typeMapString);
            return true;
        } catch (Exception e) {
            log.error("解析洞察平台标签人群配置失败", e);
            return false;
        }
    }

    @Override
    public void processCrowdNotice(CrowdNoticeMsg crowdNoticeMsg) {
        if (!ApolloConstant.AUTO_WORK_ORDER) {
            return;
        }
        if (Objects.isNull(crowdNoticeMsg)) {
            log.warn("人群通知消息为空，跳过处理");
            return;
        }
        List<Long> sourcList = gson.fromJson(vocConfig.AUTO_WORK_LABEL_CROWDID, typeListString);
        if (Objects.isNull(crowdNoticeMsg.getCrowdId()) || (!Objects.equals(crowdNoticeMsg.getCrowdId(), ApolloConstant.AUTO_WORK_CROWDID) && !sourcList.contains(crowdNoticeMsg.getCrowdId()))) {
            log.info("非需要处理的人群id");
            return;
        }

        CrowdNoticeMessageTypeEnum messageType = CrowdNoticeMessageTypeEnum.getByCode(crowdNoticeMsg.getMessageType());
        log.info("处理人群通知消息，类型: {}, 人群ID: {}, 人群大小: {}",
                messageType.getName(), crowdNoticeMsg.getCrowdId(), crowdNoticeMsg.getCrowdSize());

        try {
            switch (messageType) {
                case ONLINE:
                case OFFLINE:
                case DELETE:
                    break;
                case REFRESH:
                    handleRefreshMessage(crowdNoticeMsg);
                    break;
                default:
                    log.warn("未知的消息类型: {}", crowdNoticeMsg.getMessageType());
            }
        } catch (Exception e) {
            log.error("处理人群通知消息失败，人群ID: {}, 消息类型: {}",
                    crowdNoticeMsg.getCrowdId(), messageType.getName(), e);
            throw e;
        }
    }


    /**
     * 处理刷新消息
     */
    private void handleRefreshMessage(CrowdNoticeMsg msg) {
        log.info("处理人群刷新消息，人群ID: {}, 新版本: {}, OSS路径: {}",
                msg.getCrowdId(), msg.getRunVersion(), msg.getOssPath());
        try {
            // 处理OSS文件
            if (StringUtils.isNotBlank(msg.getOssPath()) && CollectionUtils.isNotEmpty(msg.getOssFiles())) {
                for (String fileName : msg.getOssFiles()) {
                    log.info("开始处理OSS文件: {}", fileName);
                    if(Objects.equals(msg.getCrowdId(), ApolloConstant.AUTO_WORK_CROWDID)){
                        List<CrowdUserDataDto> userDataList = crowdFileParseService.parseFile(msg.getOssPath(), fileName);
                        if (CollectionUtils.isNotEmpty(userDataList)) {
                            log.info("文件解析完成，用户数量: {}", userDataList.size());
                            // 处理用户退款
                            crowdRefundService.processCrowdRefund(userDataList);
                            log.info("文件处理完成: {}", fileName);
                        } else {
                            log.warn("文件解析结果为空: {}", fileName);
                        }
                    }else{
                        if (!initConfig()) {
                            log.warn("人群标签配置初始化失败，跳过文件处理: {}", fileName);
                            return;
                        }
                        if (CollectionUtils.isNotEmpty(crowdIdList) && crowdIdList.contains(msg.getCrowdId())) {
                            crowdLabelHandel(msg, fileName);
                        }
                    }
                }
            } else {
                log.warn("OSS路径或文件列表为空，跳过文件处理");
            }

            log.info("人群刷新消息处理完成，人群ID: {}", msg.getCrowdId());

        } catch (Exception e) {
            log.error("处理人群刷新消息失败，人群ID: {}", msg.getCrowdId(), e);
            throw e;
        }
    }

    //洞察平台人群标签处理
    private boolean crowdLabelHandel(CrowdNoticeMsg msg, String fileName) {
        if (crowdIdMap == null || crowdIdMap.isEmpty()) {
            log.info("洞察平台标签人群映射解析为空");
            return true;
        }
        String labelName = crowdIdMap.get(msg.getCrowdId().toString());
        if (StringUtils.isBlank(labelName)) {
            log.info("未找到对应的标签名称 {} ", msg.getCrowdId());
            return true;
        }
        List<CrowdLabelDto> crowdLabelDtoList = crowdFileParseService.parseCrowdLabelFile(msg.getOssPath(), fileName);
        if (CollectionUtils.isEmpty(crowdLabelDtoList)) {
            log.info("洞察标签文件解析完成，用户数量: {}", crowdLabelDtoList.size());
            return true;
        }
        List<CrowdLabel> crowdLabelAddList = new ArrayList<>();
        List<String> crowdLabelDelList = new ArrayList<>();
        String Dimension = "";
        for (CrowdLabelDto crowdLabelDto : crowdLabelDtoList) {
            //处理创建灰度数据
            Dimension = getCrowdLabel(msg, crowdLabelDto, groupMap, Dimension, labelName, crowdLabelAddList, crowdLabelDelList);
        }
        //批量处理标签数据
        if (CollectionUtils.isNotEmpty(crowdLabelAddList)) {
            crowdLabelMapper.batchInsert(crowdLabelAddList);
            List<CrowdLabel> dedupedList = deduplicate(crowdLabelAddList);
            batchInsertData(dedupedList, 500, crowdLabelMapper::batchInsert);
        }
        if (CollectionUtils.isNotEmpty(crowdLabelDelList)) {
            if ("userNo".equals(Dimension)) {
                crowdLabelMapper.batchUserNoDelete(crowdLabelDelList, labelName);
            } else if ("custNo".equals(Dimension)) {
                crowdLabelMapper.batchCustNoDelete(crowdLabelDelList, labelName);
            }
        }
        return false;
    }

    private String getCrowdLabel(CrowdNoticeMsg msg, CrowdLabelDto crowdLabelDto, Map<String, String> groupMap, String Dimension, String labelName, List<CrowdLabel> crowdLabelAddList, List<String> crowdLabelDelList) {
        String bizId = "";
        if (StringUtils.isNotEmpty(crowdLabelDto.getCustNo())) {
            Dimension = "custNo";
            bizId = crowdLabelDto.getCustNo();
        }
        if (StringUtils.isNotEmpty(crowdLabelDto.getUserNo())) {
            Dimension = "userNo";
            bizId = crowdLabelDto.getUserNo();
        }
        if ("I".equals(crowdLabelDto.getOpType())) {
            //获取灰度数据
            String userGroup = randomGeneratorClient.ab(bizId, groupMap.get(msg.getCrowdId().toString()));
            if (StringUtils.isNotBlank(userGroup)) {
                CrowdLabel crowdLabel = new CrowdLabel();
                crowdLabel.setUserGroup(userGroup);
                crowdLabel.setCustNo(crowdLabelDto.getCustNo());
                crowdLabel.setUserNo(crowdLabelDto.getUserNo());
                crowdLabel.setLabelName(labelName);
                crowdLabel.setCreatedTime(LocalDateTime.now());
                crowdLabel.setUpdatedTime(LocalDateTime.now());
                crowdLabelAddList.add(crowdLabel);
            }
        } else if ("D".equals(crowdLabelDto.getOpType())) {
            if ("userNo".equals(Dimension)) {
                crowdLabelDelList.add(crowdLabelDto.getUserNo());
            } else if ("custNo".equals(Dimension)) {
                crowdLabelDelList.add(crowdLabelDto.getCustNo());
            }
        }
        return Dimension;
    }

    public <T> void batchInsertData(List<T> dataList, int batchSize, Consumer<List<T>> insertFunction) {
        //数据库批量插入方法
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
            int totalSize = dataList.size();
            int batchCount = (totalSize + batchSize - 1) / batchSize;
            for (int i = 0; i < batchCount; i++) {
                int start = i * batchSize;
                int end = Math.min(start + batchSize, totalSize);
                List<T> subList = dataList.subList(start, end);
                insertFunction.accept(subList);
            }
        }
    }

    public static List<CrowdLabel> deduplicate(List<CrowdLabel> crowdLabelAddList) {
        // 用于存储已出现的联合唯一键
        Set<String> userNoLabelSet = new HashSet<>();
        Set<String> custNoLabelSet = new HashSet<>();
        List<CrowdLabel> result = new ArrayList<>();

        for (CrowdLabel label : crowdLabelAddList) {
            String userNoKey = label.getUserNo() + "#" + label.getLabelName();
            String custNoKey = label.getCustNo() + "#" + label.getLabelName();

            // 只要有一个唯一键重复就跳过
            if ((label.getUserNo() != null && !userNoLabelSet.add(userNoKey))
                    || (label.getCustNo() != null && !custNoLabelSet.add(custNoKey))) {
                continue;
            }
            result.add(label);
        }
        return result;
    }
}
