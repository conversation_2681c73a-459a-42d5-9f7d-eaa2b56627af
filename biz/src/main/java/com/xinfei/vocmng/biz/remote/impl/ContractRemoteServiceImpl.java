package com.xinfei.vocmng.biz.remote.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xinfei.contractcore.common.service.facade.request.contract.ContractProofApplyRequest;
import com.xinfei.contractcore.common.service.facade.request.query.ContractQueryRequest;
import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.contractcore.common.service.facade.vo.GuaranteeVO;
import com.xinfei.fundcore.facade.api.request.SettleCertApplyRequest;
import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.vocmng.biz.component.OssClient;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.config.OssConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.model.base.*;
import com.xinfei.vocmng.biz.model.enums.ResultCodeEnum;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.ContractRemoteService;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.service.DocumentRecordService;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.util.*;
import com.xinfei.vocmng.dal.mapper.CapitalMailMapper;
import com.xinfei.vocmng.dal.mapper.DocumentRecordMapper;
import com.xinfei.vocmng.dal.mapper.SendMailMapper;
import com.xinfei.vocmng.dal.po.CapitalMail;
import com.xinfei.vocmng.dal.po.DocumentRecord;
import com.xinfei.vocmng.dal.po.SendMail;
import com.xinfei.vocmng.itl.ContractFeignClient;
import com.xinfei.vocmng.itl.ItlConfig;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.ContractCoreClient;
import com.xinfei.vocmng.itl.client.feign.FundCoreClient;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.http.FundOrderFacade;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.rr.dto.ContractBaseDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import com.xinfei.vocmng.biz.rr.dto.DocumentRecordDto;
import com.xinfei.vocmng.itl.rr.dto.LoanInfoDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @version $ ContractRemoteServiceImpl, v 0.1 2023/12/23 16:33 qu.lu Exp $
 */
@Slf4j
@Service
public class ContractRemoteServiceImpl implements ContractRemoteService {

    @Autowired
    private ItlConfig itlConfig;
    @Autowired
    private ContractFeignClient contractFeignClient;
    @Autowired
    private LendQueryFacadeClient lendQueryFacadeClient;
    @Autowired
    private LcsFeignService lcsFeignClient;
    @Autowired
    private LoginUserConfig loginUserConfig;
    @Autowired
    private CapitalMailMapper capitalMailMapper;
    @Autowired
    private DocumentRecordMapper documentRecordMapper;
    @Autowired
    private DocumentRecordService documentRecordService;
    @Autowired
    private FundOrderFacade fundOrderFacade;
    @Autowired
    private CisFacadeClient cisFacadeClient;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private OssClient ossClient;
    @Autowired
    private SendMailMapper sendMailMapper;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private VocConfig vocConfig;
    @Autowired
    private FundCoreClient fundCoreClient;
    @Resource
    private ContractCoreClient contractCoreClient;
    @Resource
    private RedisUtils redisUtils;


    @Override
    public ApiResponse<List<ContractDetailDto>> queryCashSubContractList(QueryContractListRequest request) {
        String validateResult = validateParam(request);

        if (StringUtils.isNotEmpty(validateResult)) {
            return ApiResponse.responseWithCode(ResultCodeEnum.ERROR_PATTERN, validateResult);
        }

        return ApiResponse.success(queryContractDetailInfo(request));
    }

    @Override
    public ApiResponse<LoanInfoDto> queryJqzmInfo(LoanInfoRequest request) {
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(request.getOrderNumber());
        ManageOrderDetailDTO od = lendQueryFacadeClient.getOrderDetail(o);
        if (Objects.isNull(od)) {
            throw new IllegalArgumentException(request.getOrderNumber() + "订单号不存在");
        }
        LoanPlanRequest l = new LoanPlanRequest();
        l.setLoanNos(Collections.singletonList(request.getOrderNumber()));
        List<LoanPlanResponse> lps = lcsFeignClient.planDetail(l);
        if (CollectionUtils.isEmpty(lps) || CollectionUtils.isEmpty(lps.get(0).getPlanList())) {
            throw new IllegalArgumentException("账单不存在或订单未结清");
        }
        PlanResponse prs = lps.get(0).getPlanList().stream().max(Comparator.comparingInt(PlanResponse::getTerm)).get();
        if (!Objects.equals("2", prs.getRpyFlag())) {
            throw new IllegalArgumentException("订单还未结清，请确认后再操作！");
        }
        ThreeElementsDTO ted = cisFacadeClient.queryThreeElementsByUserNo(od.getUserNo());
        LoanInfoDto lid = new LoanInfoDto();
        lid.setName(ted.getName());
        lid.setUserNo(od.getUserNo().toString());
        lid.setCustNo(ted.getCustNo());
        lid.setAmount(od.getLoanAmt());
        lid.setOrderNumber(request.getOrderNumber());
        Date dateCash = od.getDateCash();
        lid.setLoanYear(String.valueOf(DateUtil.year(dateCash)));
        lid.setLoanMonth(String.format("%02d", DateUtil.month(dateCash) + 1));
        lid.setLoanDay(String.format("%02d", DateUtil.dayOfMonth(dateCash)));
        Date dateSettle = prs.getDateSettle();
        lid.setPayYear(String.valueOf(DateUtil.year(dateSettle)));
        lid.setPayMonth(String.format("%02d", DateUtil.month(dateSettle) + 1));
        lid.setPayDay(String.format("%02d", DateUtil.dayOfMonth(dateSettle)));
        Date now = new Date();
        lid.setYear(String.valueOf(DateUtil.year(now)));
        lid.setMonth(String.format("%02d", DateUtil.month(now) + 1));
        lid.setDay(String.format("%02d", DateUtil.dayOfMonth(now)));
        Date dateDue = prs.getDateDue();
        lid.setLoanEndYear(String.valueOf(DateUtil.year(dateDue)));
        lid.setLoanEndMonth(String.format("%02d", DateUtil.month(dateDue) + 1));
        lid.setLoanEndDay(String.format("%02d", DateUtil.dayOfMonth(dateDue)));
        lid.setAppName(od.getApp());
        lid.setApp(getAppName(od.getApp()));
        lid.setCapital(od.getFundSource());
        return ApiResponse.success(lid);
    }

    @Override
    public ApiResponse<List<String>> querySpecialList() {
        LambdaQueryWrapper<CapitalMail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalMail::getIsDeleted, 0);
        List<CapitalMail> capitalMails = capitalMailMapper.selectList(wrapper);
        return ApiResponse.success(Optional.ofNullable(capitalMails)
                .map(list -> list.stream()
                        .map(CapitalMail::getCapital)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList()));
    }

    public List<CapitalMail> getOne(String capital) {
        LambdaQueryWrapper<CapitalMail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalMail::getCapital, capital);
        wrapper.eq(CapitalMail::getIsDeleted, 0);
        return capitalMailMapper.selectList(wrapper);
    }

    @Override
    public ApiResponse<List<ContractStatusDetail>> queryObtainStatus(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return ApiResponse.fail("orderIds is null");
        }
        QueryLoanReq queryLoanReq = new QueryLoanReq();
        queryLoanReq.setLoanNoList(orderIds);
        queryLoanReq.setNeedPlan(false);
        List<QueryLoanDetailResp> res = lcsFeignClient.queryLoanInfo(queryLoanReq);
        if (CollectionUtil.isEmpty(res)) {
            return ApiResponse.fail("queryLoanInfo error");
        }
        return ApiResponse.success(res.stream().map(t -> {
            ContractStatusDetail d = new ContractStatusDetail();
            d.setOrderNumber(t.getLoanInfo().getLoanNo());
            d.setSettle(Objects.equals("FP", t.getLoanInfo().getStatus()) ? true : false);
            return d;
        }).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> sendMail(SettlementCertificateReq req) {
        if (CollectionUtils.isEmpty(req.getOrders())) {
            return ApiResponse.fail("order list is null");
        }
        List<String> urls;
        try {
            urls = getSettleUrl(req);
        } catch (Exception e) {
            log.error("getSettleUrl error,req:{}", JsonUtil.toJson(req), e);
            return ApiResponse.fail(e.getMessage());
        }
        try {
            MailConfig.Config mc = getMailConfig(req.getOrders().get(0).getApp());
            if (Objects.isNull(mc)) {
                return ApiResponse.fail("this app has not mail config,app=>" + req.getOrders().get(0).getApp());
            }
            MailUtil.sendTXMail(urls, mc.getMail(), req.getMail(), "结清证明", mc.getBox(), "尊敬的客户：\n" +
                    "\n" +
                    "      您好！附件为您的贷款结清证明,祝您生活愉快！", mc.getPassword(), mc.getPort(), mc.getName());
            documentRecordService.saveBatchs(req.getOrders().stream().map(order -> {
                return DocumentRecord.builder()
                        .orderNo(order.getOrderNumber())
                        .userNo(req.getUserNo())
                        .createUser(UserContextHolder.getUserIdentify())
                        .type(order.getType())
                        .mail(req.getMail())
                        .status(2)
                        .capitalPool(order.getCapitalPoolStr())
                        .prinAmt(new BigDecimal(order.getLoanAmount()))
                        .createdTime(LocalDateTime.now())
                        .updatedTime(LocalDateTime.now())
                        .build();
            }).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("send mail error,req:{}", JsonUtil.toJson(req), e);
            return ApiResponse.fail(e.getMessage());
        }
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    public ApiResponse<List<String>> downFile(SettlementCertificateReq req) {
        if (CollectionUtils.isEmpty(req.getOrders())) {
            return ApiResponse.fail("order list is null");
        }
        List<String> urls;
        try {
            urls = getSettleUrl(req);
        } catch (Exception e) {
            log.error("getSettleUrl error,req:{}", JsonUtil.toJson(req), e);
            return ApiResponse.fail(e.getMessage());
        }
        return ApiResponse.success(urls);
    }

    /**
     * @param req:
     * @return ApiResponse<Boolean>
     * <AUTHOR>
     * @description 记录申请日志
     * @date 2024/6/6 15:13
     */
    @Override
    @Transactional
    public ApiResponse<String> applyNow(SettlementCertificateReq req) {
        if (CollectionUtils.isEmpty(req.getOrders())) {
            return ApiResponse.fail("order list is null");
        }
        String msg = "";
        List<String> errorOrders = new ArrayList<>();
        //资方结清证明发邮件，凭证不用。非特殊资方直接调用资金侧申请证明文件接口，特殊资方需调用配置中配置的邮箱及资方邮箱信息
        try {
            if (Objects.equals(2, req.getType())) {
                ImmutablePair<String,List<String>> inv= getInvestorSettlementProof(req);
                msg=inv.getLeft();
                errorOrders=inv.getRight();
            } else if (Objects.equals(3, req.getType())) {
                ImmutablePair<String,List<String>> cert = getInvestorLoanDisbursementCertificate(req);
                msg=cert.getLeft();
                errorOrders=cert.getRight();
            }
            List<String> finalErrorOrders = errorOrders;
            documentRecordService.saveBatchs(req.getOrders().stream()
                    .filter(order -> !finalErrorOrders.contains(order.getOrderNumber()))
                    .map(order -> {
                return DocumentRecord.builder()
                        .orderNo(order.getOrderNumber())
                        .userNo(req.getUserNo())
                        .createUser(UserContextHolder.getUserIdentify())
                        .type(req.getType())
                        .mail(req.getMail())
                        .status(1)
                        .access(order.getAccess())
                        .capitalPool(order.getCapitalPoolStr())
                        .prinAmt(new BigDecimal(order.getLoanAmount()))
                        .createdTime(LocalDateTime.now())
                        .updatedTime(LocalDateTime.now())
                        .build();
            }).collect(Collectors.toList()));
            if (StringUtils.isNotBlank(msg.trim())) {
                return ApiResponse.fail(msg);
            }
        } catch (Exception e) {
            log.error("applyNow run error", e);
            return ApiResponse.fail(e.getMessage());
        }

        return ApiResponse.success("");
    }

    @Override
    public ApiResponse<PageResultResponse<DocumentRecordDto>> queryApplyList(DocumentRecordReq req) {
        LambdaQueryWrapper<DocumentRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(req.getOrderNumber()), DocumentRecord::getOrderNo, req.getOrderNumber());
        wrapper.eq(StringUtils.isNotBlank(req.getUserNo()), DocumentRecord::getUserNo, req.getUserNo());
        wrapper.eq(Objects.nonNull(req.getType()), DocumentRecord::getType, req.getType());
        wrapper.ge(Objects.nonNull(req.getStarTime()), DocumentRecord::getCreatedTime, req.getStarTime());
        wrapper.le(Objects.nonNull(req.getEndTime()), DocumentRecord::getCreatedTime, req.getEndTime());
        wrapper.orderByDesc(DocumentRecord::getCreatedTime);
        IPage<DocumentRecord> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<DocumentRecord> pageList = documentRecordMapper.selectPage(page, wrapper);

        if (Objects.nonNull(pageList) && org.apache.commons.collections.CollectionUtils.isNotEmpty(pageList.getRecords())) {
            List<DocumentRecordDto> dtoList = pageList.getRecords().stream()
                    .map(documentRecord -> {
                        DocumentRecordDto dto = new DocumentRecordDto();
                        BeanUtils.copyProperties(documentRecord, dto);
                        dto.setCreateUser(employeeService.getUserNameForIdentify(documentRecord.getCreateUser()));
                        return dto;
                    })
                    .collect(Collectors.toList());
            // 返回转换后的分页数据
            return ApiResponse.success(new PageResultResponse<>(dtoList, pageList.getCurrent(), pageList.getSize(), pageList.getTotal()));
        }
        return ApiResponse.success(new PageResultResponse<>());
    }

    @Override
    public ApiResponse<LookFileResp> lookFile(DocumentRecordReq record) {
        LookFileResp response = new LookFileResp();
        List<String> urls = new ArrayList<>();

        switch (record.getType()) {
            case 1:
            case 4:
                String url = lookXfFile(record.getOrderNumber(), record.getType());
                urls.add(url);
                break;
            case 2:
                urls = lookZfFile(record.getOrderNumber(), record.getId(), response);
                break;
            case 3:
                urls = lookPzFile(record.getOrderNumber(), record.getId(), response);
                break;
            case 5:
                urls = getIntermediaryAgreementUrls(record.getOrderNumber());
                if (CollectionUtil.isEmpty(urls)) {
                    response.setMsg("居间协议不存在或无法获取");
                }
                break;
            default:
                return ApiResponse.fail("Invalid type: " + record.getType());
        }

        response.setUrl(urls);

        if(StringUtils.isNotBlank(response.getMsg())){
            return ApiResponse.fail(response.getMsg());
        }
        return ApiResponse.success(response);
    }

    private static String getExtension(String fileName) {
        return fileName.substring(fileName.lastIndexOf('.') + 1);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> uploadDocumentRecord(MultipartFile[] files, String mail, Integer type, Long id) {
        if (files == null || files.length == 0) {
            throw new RuntimeException("至少选择一个文件进行上传");
        }

        String extension = getExtension(files[0].getOriginalFilename());
        for (MultipartFile file : files) {
            if (!extension.equals(getExtension(file.getOriginalFilename()))) {
                throw new RuntimeException("所有上传文件类型必须一致");
            }
        }

        String mergedFileName = "merged_file_" + System.currentTimeMillis();
        String key = ossConfig.getUploadFilePath() + "/" + DateUtil.format(new Date(), "yyyy-MM-dd") + "/";

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            if ("pdf".equalsIgnoreCase(extension)) {
                mergePdfFiles(files, outputStream);
                mergedFileName += ".pdf";
            } else if (isImage(extension)) {
                mergedFileName += ".pdf"; // 统一转换为 PDF
                mergeImagesToPdf(files, outputStream);
            } else {
                mergedFileName += "." + extension;
                mergeOtherFiles(files, outputStream);
            }

            // 上传到 OSS
            try (InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray())) {
                ossClient.upload(key + mergedFileName, inputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("上传文件时发生错误", e);
        }

        documentRecordMapper.updateById(DocumentRecord.builder().id(id).fileUrl(key + mergedFileName).mail(mail).build());

        //type =1 立即发送,发送给用户,
        if (Objects.equals(1, type)) {
            DocumentRecord dr = documentRecordMapper.selectById(id);
            ManageOrderDetailRequest o = new ManageOrderDetailRequest();
            o.setOrderNo(dr.getOrderNo());
            ManageOrderDetailDTO odo = lendQueryFacadeClient.getOrderDetail(o);
            if (Objects.isNull(odo)) {
                throw new IllegalArgumentException(dr.getOrderNo() + "订单号不存在");
            }
            MailConfig.Config mc = getMailConfig(odo.getApp());
            if (Objects.isNull(mc)) {
                return ApiResponse.fail("this app has not mail config,app=>" + odo.getApp());
            }
            try {
                if (Objects.equals(2, dr.getType())) {
                    MailUtil.sendTXMail(Arrays.asList(ossClient.getOssUrl(key + mergedFileName)), mc.getMail(), mail, "结清证明", mc.getBox(), getSub(odo.getFundSource()), mc.getPassword(), mc.getPort(), mc.getName());
                } else if (Objects.equals(3, dr.getType())) {
                    MailUtil.sendTXMail(Arrays.asList(ossClient.getOssUrl(key + mergedFileName)), mc.getMail(), mail, "放款证明", mc.getBox(), getFkSub(odo.getFundSource()), mc.getPassword(), mc.getPort(), mc.getName());
                }
                documentRecordMapper.updateById(DocumentRecord.builder().id(id).status(2).build());
            } catch (Exception e) {
                throw new RuntimeException("send mail fail");
            }
        }
        return ApiResponse.success(Boolean.TRUE);
    }

    private void mergeImagesToPdf(MultipartFile[] files, OutputStream outputStream) throws IOException {
        PDDocument document = new PDDocument();
        for (MultipartFile file : files) {
            BufferedImage image = ImageIO.read(file.getInputStream());
            PDPage page = new PDPage(new PDRectangle(image.getWidth(), image.getHeight()));
            document.addPage(page);

            PDPageContentStream contentStream = new PDPageContentStream(document, page);
            PDImageXObject pdImage = LosslessFactory.createFromImage(document, image);
            contentStream.drawImage(pdImage, 0, 0, image.getWidth(), image.getHeight());
            contentStream.close();
        }
        document.save(outputStream);
        document.close();
    }

    private void mergePdfFiles(MultipartFile[] files, OutputStream outputStream) throws IOException {
        PDFMergerUtility pdfMerger = new PDFMergerUtility();
        try {
            pdfMerger.setDestinationStream(outputStream);
            for (MultipartFile file : files) {
                pdfMerger.addSource(file.getInputStream());
            }
            pdfMerger.mergeDocuments(null);
        } catch (Exception e) {
                InputStream firstFileStream = files[0].getInputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = firstFileStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                firstFileStream.close();
        }
    }


    private void mergeOtherFiles(MultipartFile[] files, OutputStream outputStream) throws IOException {
        for (MultipartFile file : files) {
            outputStream.write(file.getBytes());
            outputStream.write("\n".getBytes());  // 添加换行符，避免内容粘连
        }
    }

    private boolean isImage(String extension) {
        return Arrays.asList("jpg", "jpeg", "png").contains(extension.toLowerCase());
    }

    private String getFkSub(String fundSource) {
        if (TechplayStringUtil.isContain(vocConfig.getJqzmEncipher(), fundSource)) {
            return "尊敬的客户：\n" +
                    "\n" +
                    "      您好！附件为您的贷款放款证明，为保证数据安全，资料进行加密处理，密码为您身份证后6位。祝您生活愉快！";
        }
        return "尊敬的客户：\n" +
                "\n" +
                "      您好！附件为您的贷款放款证明，祝您生活愉快！";
    }

    @Override
    public ApiResponse<List<CapitalMail>> queryCapitalList() {
        LambdaQueryWrapper<CapitalMail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalMail::getIsDeleted, 0);
        List<CapitalMail> capitalMails = capitalMailMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(capitalMails)) {
            capitalMails.forEach(p -> p.setUpdateUser(employeeService.getUserNameForIdentify(p.getUpdateUser())));
        }
        return ApiResponse.success(capitalMails);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> updateCapital(CapitalMail req) {
        req.setUpdateUser(UserContextHolder.getUserIdentify());
        req.setUpdatedTime(LocalDateTime.now());
        capitalMailMapper.updateById(req);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> insertCapital(CapitalMail req) {
        req.setCreateUser(UserContextHolder.getUserIdentify());
        req.setUpdateUser(UserContextHolder.getUserIdentify());
        req.setCreatedTime(LocalDateTime.now());
        req.setUpdatedTime(LocalDateTime.now());
        capitalMailMapper.insert(req);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    public ApiResponse<List<SendMail>> querySendList(SendMail req) {
        LambdaQueryWrapper<SendMail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SendMail::getIsDeleted, 0);
        wrapper.eq(Objects.nonNull(req.getSendObject()), SendMail::getSendObject, req.getSendObject());
        List<SendMail> capitalMails = sendMailMapper.selectList(wrapper);
        return ApiResponse.success(capitalMails);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> insertSend(SendMail req) {
        req.setCreateUser(UserContextHolder.getUserIdentify());
        req.setUpdateUser(UserContextHolder.getUserIdentify());
        req.setCreatedTime(LocalDateTime.now());
        req.setUpdatedTime(LocalDateTime.now());
        sendMailMapper.insert(req);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> updateSend(SendMail req) {
        req.setUpdateUser(UserContextHolder.getUserIdentify());
        req.setUpdatedTime(LocalDateTime.now());
        sendMailMapper.updateById(req);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    @Transactional
    public ApiResponse<String> sendFile(DocumentRecordReq record) {
        String url = null;
        List<String> urls=new ArrayList<>();
        LookFileResp response = new LookFileResp();
        DocumentRecord dr = documentRecordMapper.selectById(record.getId());
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(dr.getOrderNo());
        ManageOrderDetailDTO odo = lendQueryFacadeClient.getOrderDetail(o);
        if (Objects.isNull(odo)) {
            throw new IllegalArgumentException(dr.getOrderNo() + "订单号不存在");
        }
        MailConfig.Config mc = getMailConfig(odo.getApp());
        String mail = StringUtils.isBlank(record.getMail()) ? dr.getMail() : record.getMail();
        if (Objects.isNull(mc)) {
            return ApiResponse.fail("this app has not mail config,app=>" + odo.getApp());
        }
        try {
            switch (record.getType()) {
                case 1:
                case 4:
                    url = lookXfFile(record.getOrderNumber(),record.getType());
                    if (StringUtils.isNotBlank(url)) {
                        MailUtil.sendTXMail(Arrays.asList(url), mc.getMail(), mail, "结清证明", mc.getBox(), getSub(null), mc.getPassword(), mc.getPort(), mc.getName());
                    }
                    break;
                case 2:
                    urls = lookZfFile(record.getOrderNumber(), record.getId(), response);
                    if (CollectionUtil.isNotEmpty(urls)) {
                        MailUtil.sendTXMail(urls, mc.getMail(), mail, "结清证明", mc.getBox(), getSub(odo.getFundSource()), mc.getPassword(), mc.getPort(), mc.getName());
                    }
                    break;
                case 3:
                    urls = lookPzFile(record.getOrderNumber(), record.getId(), response);
                    if (CollectionUtil.isNotEmpty(urls)) {
                        MailUtil.sendTXMail(urls, mc.getMail(), mail, "放款证明", mc.getBox(), getFkSub(odo.getFundSource()), mc.getPassword(), mc.getPort(), mc.getName());
                    }
                    break;
                case 5:
                    urls = getIntermediaryAgreementUrls(record.getOrderNumber());
                    if (CollectionUtil.isNotEmpty(urls)) {
                        String subject = "平台居间协议" + DateUtil.format(new Date(), "yyyyMMdd");
                        String content = "尊敬的客户：\n\n      您好！附件为您的平台居间协议，祝您生活愉快！";
                        MailUtil.sendTXMail(urls, mc.getMail(), mail, subject, mc.getBox(), content, mc.getPassword(), mc.getPort(), mc.getName());
                    }
                    break;
                default:
                    return ApiResponse.fail("Invalid type: " + record.getType());
            }
            documentRecordMapper.updateById(DocumentRecord.builder().id(record.getId()).mail(mail).status(2).build());
        } catch (Exception e) {
            log.error("sendFile error", e);
            ApiResponse.fail(e.getMessage());
        }
        return ApiResponse.success(response.getMsg());
    }

    @Override
    public ApiResponse<List<ContractDataDto>> queryContractList(ContractReq req) {
        List<ContractDataDto> contractDataDtos;
        switch (req.getType()) {
            /*case "profit":
                contractDataDtos=queryContractByProfit(req.getOrderNO());*/
            case "bind_card":
                contractDataDtos = queryBindCardList(req.getMobile());
                break;
            case "activation":
                contractDataDtos = queryActivationList(req.getMobile());
                break;
            case "cash":
                contractDataDtos = vocConfig.isNewContractFlag()?queryNewCashList(req.getOrderNO()):queryCashList(req.getOrderNO());
                break;
            case "credit_investigation":
                contractDataDtos = queryCreditList(req.getMobile());
                break;
            default:
                return ApiResponse.fail("Invalid type: " + req.getType());
        }
        return ApiResponse.success(contractDataDtos);
    }

    @Override
    public ApiResponse<List<ProtocolDto>> getProtocolsByType(ContractReq req) {
        String type = req.getType();
        if(StringUtils.isBlank(type)){
            return ApiResponse.paramIllegal("type不能为空");
        }
        List<ProtocolDto> protocolList = new ArrayList<>();
        switch (type) {
            case "consume":
                protocolList = queryContractUnionRuleUnionTemplateCfgListByStatus();
                break;
            case "activation":
                protocolList = queryContractCashRuleTemplateConfigUnionCashRuleListBySearch("activation",1,null);
                break;
            case "bind_card":
                protocolList = queryContractCashRuleTemplateConfigUnionCashRuleListBySearch("bind_card",1,null);
                break;
            case "cash":
                protocolList = queryContractCashRuleTemplateConfigUnionCashRuleListBySearch("", 1, Arrays.asList("bind_card", "activation"));
                break;
        }

       return ApiResponse.success(protocolList);
    }

    private  List<ProtocolDto> queryContractCashRuleTemplateConfigUnionCashRuleListBySearch(String type, Integer status, List<String> list) {
        GroupKeyReq g = new GroupKeyReq();
        if(StringUtils.isNotBlank(type)){
            g.setSpecial_type(type);
        }
        if(Objects.nonNull(status)){
            g.setStatus(status);
        }
        if(CollectionUtil.isNotEmpty(list)){
            g.setSpecialTypeNotInList(list);
        }
        List<GroupKeyResp> groupByKeyAll = getCashGroupByKeyAll(g);
        if (CollectionUtil.isEmpty(groupByKeyAll)){
            return Collections.emptyList();
        }
        return groupByKeyAll.stream().map(group -> {
            ProtocolDto protocolDto = new ProtocolDto();
            protocolDto.setKey(group.getKey());
            protocolDto.setName(group.getName());
            return protocolDto;
        }).collect(Collectors.toList());

    }

    public List<GroupKeyResp> getCashGroupByKeyAll(GroupKeyReq g){
        ContractBaseRequest<GroupKeyReq> request = new ContractBaseRequest<>();
        request.setArgs(g);
        request.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<List<GroupKeyResp>> res = contractFeignClient.getCashGroupByKeyAll(request);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("getCashGroupByKeyAll resp:{}", JsonUtil.toJson(res.getResponse()));
            return res.getResponse();
        } catch (Exception e) {
            log.warn("getCashGroupByKeyAll client error while fetching contract ", e);
            throw new ClientException("合同协议接口cash返回异常");
        }
    }


    private List<ProtocolDto> queryContractUnionRuleUnionTemplateCfgListByStatus() {
        List<GroupKeyResp> groupByKeyAll = getGroupByKeyAll();
        if (CollectionUtil.isEmpty(groupByKeyAll)){
            return Collections.emptyList();
        }
        return groupByKeyAll.stream().map(group -> {
            ProtocolDto protocolDto = new ProtocolDto();
            protocolDto.setKey(group.getKey());
            protocolDto.setName(group.getName());
            return protocolDto;
        }).collect(Collectors.toList());
    }

    public List<GroupKeyResp> getGroupByKeyAll(){
        ContractBaseRequest<GroupKeyReq> request = new ContractBaseRequest<>();
        GroupKeyReq r = new GroupKeyReq();
        request.setArgs(r);
        request.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<List<GroupKeyResp>> res = contractFeignClient.getGroupByKeyAll(request);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("getGroupByKeyAll resp:{}", JsonUtil.toJson(res.getResponse()));
            return res.getResponse();
        } catch (Exception e) {
            log.warn("getGroupByKeyAll client error while fetching contract ", e);
            throw new ClientException("合同协议接口返回异常");
        }
    }

    public void cashReSign(ReSignReq req){
        ContractBaseRequest<ReSignReq> request = new ContractBaseRequest<>();
        request.setArgs(req);
        request.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<Object> res = contractFeignClient.cashReSign(request);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("cashReSign resp:{}", JsonUtil.toJson(res.getResponse()));
        } catch (Exception e) {
            log.warn("cashReSign client error while fetching contract req:{}",JsonUtil.toJson(request), e);
            throw new ClientException("合同重签接口返回异常");
        }
    }

    public void consumeReSign(ReSignReq r){
        ContractBaseRequest<ReSignReq> request = new ContractBaseRequest<>();
        request.setArgs(r);
        request.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<Object> res = contractFeignClient.consumeReSign(request);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("consumeReSign resp:{}", JsonUtil.toJson(res.getResponse()));
        } catch (Exception e) {
            log.warn("consumeReSign client error while fetching contract req:{}",JsonUtil.toJson(request), e);
            throw new ClientException("合同重签接口返回异常");
        }
    }

    @Override
    public ApiResponse<String> resignContract(ResignContractReq req) {
        Map<String, Object> fixData = new HashMap<>();
        Map<String, Object> data = new HashMap<>();

        String fixDataStr = req.getFixData().trim();
        if (!fixDataStr.isEmpty()) {
            fixData = JsonUtil.parseJson(fixDataStr, new TypeReference<Map<String, Object>>() {});
        }

        String dataStr = req.getData().trim();
        if (!dataStr.isEmpty()) {
            data = JsonUtil.parseJson(dataStr, new TypeReference<Map<String, Object>>() {});
        }
        switch (req.getType()){
            case "activation":
            case "bind_card":
                ReSignReq rr = new ReSignReq();
                rr.setId(req.getContractId());
                rr.setKey(req.getShortName());
                if(!data.isEmpty()){rr.setData(JsonUtil.toJson(data));}
                if(!fixData.isEmpty()){rr.setFix_data(JsonUtil.toJson(fixData));}
                cashReSign(rr);
                break;
            case "cash":
                ManageOrderDetailRequest o = new ManageOrderDetailRequest();
                o.setOrderNo(req.getOrderNumber());
                ManageOrderDetailDTO od = lendQueryFacadeClient.getOrderDetail(o);
                if (Objects.isNull(od)) {
                    throw new IllegalArgumentException(req.getOrderNumber() + "该订单号无合同信息");
                }
                ReSignReq rrq = new ReSignReq();
                rrq.setId(od.getContractNumber());
                rrq.setKey(req.getShortName());
                if(!data.isEmpty()){rrq.setData(JsonUtil.toJson(data));}
                if(!fixData.isEmpty()){rrq.setFix_data(JsonUtil.toJson(fixData));}
                cashReSign(rrq);
                break;
            case "consume":
                ReSignReq rs = new ReSignReq();
                rs.setId(req.getContractId());
                rs.setKey(req.getShortName());
                if(!fixData.isEmpty()){rs.setFix_data(JsonUtil.toJson(fixData));}
                consumeReSign(rs);
                break;
            default:
                throw new IllegalArgumentException("不合法的类型");
        }

        return null;
    }

    @Override
    public ApiResponse<List<ContractVO>> queryContractCoreList(ContractCoreReq req) {
        ContractQueryRequest contractQueryRequest = new ContractQueryRequest();
        contractQueryRequest.setBizType(req.getBizType());
        contractQueryRequest.setContractOrderNo(req.getContractBizNo());
        List<ContractVO> res = contractCoreClient.listQuery(contractQueryRequest);
        return ApiResponse.success(res);
    }

    private void resignProfit(ResignContractReq req, Map<String, Object> fixData, Map<String, Object> data) {
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(req.getOrderNumber());
        ManageOrderDetailDTO od = lendQueryFacadeClient.getOrderDetail(o);
        if (Objects.isNull(od) || StringUtils.isBlank(od.getContractNumber())) {
            throw new IllegalArgumentException(req.getOrderNumber() + "该订单号无合同信息");
        }
        Map<String, Object> fixDataResult = new HashMap<>();
        List<String> delKeys = new ArrayList<>();
        if (fixData != null) {
            for (String key : fixData.keySet()) {
                Object value = null;
                value= TechplayStringUtil.getProperty(od,key);
                if (Objects.nonNull(value)) {
                    fixDataResult.put(key, value);
                }
            }
        }
    }

    private List<ContractDataDto> queryCreditList(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            throw new IllegalArgumentException("手机号为空");
        }
        List<InvestigationResp> res = queryInvestigationList(mobile);
        if (CollectionUtil.isEmpty(res)) {
            return Collections.emptyList();
        }
        return res.stream().map(t -> {
            return ContractDataDto.builder()
                    .id(t.getId())
                    .name(t.getName())
                    .mobile(t.getMobile())
                    .idCardNumber(t.getIdCardNumber())
                    .title(t.getTitle())
                    .shortName(t.getShortName())
                    .viewUrl(t.getView_url())
                    .contractId(t.getContractId())
                    .createdTime(t.getCreatedTime())
                    .build();
        }).collect(Collectors.toList());
    }

    public List<InvestigationResp> queryInvestigationList(String mobile) {
        ContractBaseRequest<InvestigationReq> request = new ContractBaseRequest<>();
        InvestigationReq r = new InvestigationReq(mobile);
        request.setArgs(r);
        request.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<List<InvestigationResp>> res = contractFeignClient.creditInvestigationList(request);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("creditInvestigationList req:{},resp:{}", mobile, JsonUtil.toJson(res.getResponse()));
            return res.getResponse();
        } catch (Exception e) {
            log.warn("creditInvestigationList client error while fetching contract for mobile: {}", mobile, e);
            throw new ClientException("征信合同接口返回异常");
        }
    }


    private List<ContractDataDto> queryNewCashList(String orderNO) {
        if (StringUtils.isBlank(orderNO)) {
            throw new IllegalArgumentException("订单号为空");
        }

        // 获取订单详情
        ManageOrderDetailRequest orderRequest = new ManageOrderDetailRequest();
        orderRequest.setOrderNo(orderNO);
        ManageOrderDetailDTO orderDetail = lendQueryFacadeClient.getOrderDetail(orderRequest);
        if (Objects.isNull(orderDetail)) {
            throw new IllegalArgumentException(orderNO + "该订单号无合同信息");
        }

        // 查询贷款合同
        List<ContractVO> loanContracts = queryLoanContracts(orderNO, orderDetail);

        // 查询补偿证明合同
        List<ContractVO> compensationContracts = queryCompensationContracts(orderNO, orderDetail);

        // 合并所有合同并过滤掉downloadUrl为空的对象
        List<ContractVO> allContracts = mergeAndFilterContracts(loanContracts, compensationContracts);

        // 转换为DTO
        return convertToContractDataDtos(allContracts);
    }

    /**
     * 查询贷款合同
     */
    private List<ContractVO> queryLoanContracts(String orderNO, ManageOrderDetailDTO orderDetail) {
        ContractQueryRequest loanRequest = new ContractQueryRequest();
        loanRequest.setBizType("loan");
        loanRequest.setContractOrderNo(orderDetail.getContractNumber());
        loanRequest.setBizNo(orderNO);
        return contractCoreClient.listQuery(loanRequest);
    }

    /**
     * 查询补偿证明合同
     */
    private List<ContractVO> queryCompensationContracts(String orderNO, ManageOrderDetailDTO orderDetail) {
        ContractQueryRequest compensationRequest = buildContractQueryRequest(orderNO, orderDetail);
        return contractCoreClient.listQuery(compensationRequest);
    }

    /**
     * 合并合同列表并过滤掉downloadUrl为空的对象
     */
    private List<ContractVO> mergeAndFilterContracts(List<ContractVO> loanContracts, List<ContractVO> compensationContracts) {
        Stream<ContractVO> loanStream = Objects.isNull(loanContracts) ? Stream.empty() : loanContracts.stream();
        Stream<ContractVO> compensationStream = Objects.isNull(compensationContracts) ? Stream.empty() : compensationContracts.stream();

        return Stream.concat(loanStream, compensationStream)
                .filter(this::hasValidDownloadUrl)
                .collect(Collectors.toList());
    }

    /**
     * 检查合同是否有有效的下载URL
     */
    private boolean hasValidDownloadUrl(ContractVO contract) {
        if (contract == null) {
            return false;
        }
        String fundDownloadUrl = contract.getFundDownloadUrl();
        String downloadUrl = contract.getDownloadUrl();

        // 如果fundDownloadUrl不为空，则使用它；否则检查downloadUrl
        return StringUtils.isNotBlank(fundDownloadUrl) || StringUtils.isNotBlank(downloadUrl);
    }

    /**
     * 转换为ContractDataDto列表
     */
    private List<ContractDataDto> convertToContractDataDtos(List<ContractVO> contracts) {
        return Optional.ofNullable(contracts)
                .orElse(Collections.emptyList())
                .stream()
                .map(this::convertToContractDataDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个ContractVO为ContractDataDto
     */
    private ContractDataDto convertToContractDataDto(ContractVO contract) {
        String guaranteeStr = Optional.ofNullable(contract.getGuaranteeList())
                .orElse(Collections.emptyList())
                .stream()
                .map(GuaranteeVO::getGuaranteeName)
                .collect(Collectors.joining(","));

        return ContractDataDto.builder()
                .createdTime(contract.getCreateTime())
                .title(contract.getContractName())
                .contractId(contract.getContractOrderNo())
                .shortName(contract.getLegacyContractKey())
                .guaranteeList(guaranteeStr)
                .viewUrl(StringUtils.isBlank(contract.getFundDownloadUrl()) ?
                        contract.getDownloadUrl() : contract.getFundDownloadUrl())
                .build();
    }

    private ContractQueryRequest buildContractQueryRequest(String orderNO, ManageOrderDetailDTO od) {
        ContractQueryRequest compensationReq = new ContractQueryRequest();
        compensationReq.setBizType(VocConstants.BIZ_TYPE);
        compensationReq.setContractOrderNo(od.getContractNumber());
        compensationReq.setBizNo(orderNO);
        compensationReq.setContractTypeList(Collections.singletonList("compensation_proof"));
        return compensationReq;
    }

    private List<ContractDataDto> queryCashList(String orderNO) {
        if(StringUtils.isBlank(orderNO)){
            throw new IllegalArgumentException("订单号为空");
        }
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(orderNO);
        ManageOrderDetailDTO od = lendQueryFacadeClient.getOrderDetail(o);
        if (Objects.isNull(od)) {
            throw new IllegalArgumentException(orderNO + "该订单号无合同信息");
        }
        UserOrderContractReq contractReq = new UserOrderContractReq();
        contractReq.setContract_id(od.getContractNumber());
        List<UserOrderContractResp> contracts = getAllUserOrderContract(contractReq);
        if (CollectionUtil.isEmpty(contracts)) {
            return Collections.emptyList();
        }
        List<ContractDataDto> res = contracts.stream().map(c -> {
            return ContractDataDto.builder()
                    .contractId(c.getContractId())
                    .createdTime(c.getCreatedTime())
                    .id(c.getId())
                    .idCardNumber(c.getIdCardNumber())
                    .mobile(c.getMobile())
                    .name(c.getName())
                    .shortName(c.getShortName())
                    .build();
        }).collect(Collectors.toList());
        QueryContractListRequest request = new QueryContractListRequest();
        request.setContractNo(contracts.get(0).getContractId());
        List<ContractDownloadDetail> ret = queryContractDownloadInfo(request);
        Map<String, ContractDownloadDetail> contractData = ret.stream()
                .collect(Collectors.toMap(ContractDownloadDetail::getShortName, sc -> sc));
        for(ContractDataDto row :res){
            String shortName = row.getShortName();
            if (Arrays.asList("xyf95", "xyf114", "xyf117", "xyf128").contains(shortName)) {
                row.setTitle("信用咨询及管理服务协议");
            } else {
                row.setTitle(Optional.ofNullable(contractData.get(shortName)).map(ContractDownloadDetail::getTitle).orElse("未知"));
            }
            row.setViewUrl(Optional.ofNullable(contractData.get(shortName)).map(ContractDownloadDetail::getDownloadUrl).orElse(""));
        }
        return res;
    }

    private List<ContractDataDto> queryActivationList(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            throw new IllegalArgumentException("手机号为空");
        }
        UserOrderContractReq contractReq = new UserOrderContractReq();
        contractReq.setMobile(mobile);
        contractReq.setAndor_extra_data_multi_like(Arrays.asList("activation"));
        contractReq.setPeriod(0);
        contractReq.setFdd_status("SUCCESS");
        contractReq.setAndor_contract_names_multi_like(Arrays.asList("jihuo001"));
        List<UserOrderContractResp> contracts = getAllUserOrderContract(contractReq);
        if (CollectionUtil.isEmpty(contracts)) {
            return Collections.emptyList();
        }
        Set<String> contractIds = contracts.stream().map(UserOrderContractResp::getContractId).collect(Collectors.toSet());
        Map<String, ContractDownloadDetail> contractsMap = new HashMap<>();
        for (String contractId : contractIds) {
            QueryContractListRequest request = new QueryContractListRequest();
            request.setContractNo(contractId);
            List<ContractDownloadDetail> downloadDetails = queryContractDownloadInfo(request);
            for (ContractDownloadDetail contract : downloadDetails) {
                contractsMap.put(contractId + "-" + contract.getShortName(), contract);
            }
        }
        List<ContractDataDto> list = new ArrayList<>();
        for (UserOrderContractResp contract : contracts) {

            String contractId = contract.getContractId();
            String app = contract.getApp();
            String shortName = contract.getShortName();

            String title = "";
            String viewUrl = "";
            String key = contractId + "-" + shortName;
            if (contractsMap.containsKey(key)) {
                ContractDownloadDetail matchedContract = contractsMap.get(key);
                title = "[" + app + "]" + matchedContract.getTitle();
                viewUrl = matchedContract.getDownloadUrl();
            }

            ContractDataDto contractInfo = ContractDataDto.builder().build();
            contractInfo.setId(contract.getId());
            contractInfo.setName(contract.getName());
            contractInfo.setMobile(contract.getMobile());
            contractInfo.setIdCardNumber(contract.getIdCardNumber());
            contractInfo.setContractId(contractId);
            contractInfo.setCreatedTime(contract.getCreatedTime());
            contractInfo.setShortName(shortName);
            contractInfo.setTitle(title);
            contractInfo.setViewUrl(viewUrl);

            list.add(contractInfo);
        }

        return list;
    }

    private List<ContractDataDto> queryBindCardList(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            throw new IllegalArgumentException("手机号为空");
        }

        UserOrderContractResp contract = getOneUserOrderContract(mobile);
        if (contract == null) {
            return Collections.emptyList();
        }

        String contractId = contract.getContractId();
        UserOrderContractReq contractReq = new UserOrderContractReq();
        contractReq.setContract_id(contractId);
        List<UserOrderContractResp> allContracts = getAllUserOrderContract(contractReq);
        if (CollectionUtil.isEmpty(allContracts)) {
            return Collections.emptyList();
        }
        QueryContractListRequest request = new QueryContractListRequest();
        request.setContractNo(contractId);
        List<ContractDownloadDetail> downloadDetails = queryContractDownloadInfo(request);
        if (CollectionUtil.isEmpty(downloadDetails)) {
            return Collections.emptyList();
        }

        List<ContractDataDto> contractDataList = new ArrayList<>();
        for (ContractDownloadDetail detail : downloadDetails) {
            ContractDataDto dto = ContractDataDto.builder().build();
            dto.setContractId(contractId);
            dto.setShortName(detail.getShortName());
            dto.setTitle(Optional.ofNullable(detail.getTitle()).orElse("未知"));
            dto.setViewUrl(Optional.ofNullable(detail.getDownloadUrl()).orElse(""));

            allContracts.stream()
                    .filter(uc -> uc.getShortName().equals(detail.getShortName()) && uc.getContractId().equals(contractId))
                    .findFirst()
                    .ifPresent(uc -> {
                        dto.setId(uc.getId());
                        dto.setName(uc.getName());
                        dto.setMobile(uc.getMobile());
                        dto.setIdCardNumber(uc.getIdCardNumber());
                        dto.setCreatedTime(uc.getCreatedTime());
                    });

            contractDataList.add(dto);
        }

        return contractDataList;
    }

    public UserOrderContractResp getOneUserOrderContract(String mobile) {
        ContractBaseRequest<UserOrderContractReq> req = new ContractBaseRequest<>();
        UserOrderContractReq contractReq = new UserOrderContractReq();
        contractReq.setMobile(mobile);
        contractReq.setFdd_download_url_neq("");
        contractReq.setAndor_extra_data_multi_like(Arrays.asList("bind_card", "bind_card_change"));
        req.setArgs(contractReq);
        req.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<UserOrderContractResp> res = contractFeignClient.getOneUserOrderContract(req);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("getOneUserOrderContract req:{},resp:{}", mobile, JsonUtil.toJson(res.getResponse()));
            return res.getResponse();
        } catch (Exception e) {
            log.warn("getOneUserOrderContract client error while fetching contract for mobile: {}", mobile, e);
            throw new ClientException("合同接口返回异常");
        }
    }

    public List<UserOrderContractResp> getAllUserOrderContract(UserOrderContractReq contractReq) {
        ContractBaseRequest<UserOrderContractReq> req = new ContractBaseRequest<>();
        req.setArgs(contractReq);
        req.setUa(itlConfig.getContractUA());
        try {
            ContractResponse<List<UserOrderContractResp>> res = contractFeignClient.getAllUserOrderContract(req);
            if (!res.isSuccess()) {
                throw new ClientException(res.getMessage());
            }
            log.info("getAllUserOrderContract req:{},resp:{}", JsonUtil.toJson(contractReq), JsonUtil.toJson(res.getResponse()));
            return res.getResponse();
        } catch (Exception e) {
            log.warn("getAllUserOrderContract client error while fetching contract for req: {}", JsonUtil.toJson(contractReq), e);
            throw new ClientException("合同列表接口返回异常");
        }
    }

    /*private List<ContractDataDto> queryContractByProfit(String orderNO) {
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(orderNO);
        ManageOrderDetailDTO od = lendQueryFacadeClient.getOrderDetail(o);
        if (Objects.isNull(od)) {
            throw new IllegalArgumentException(orderNO + "该订单号无合同信息");
        }
        RequestContractService::instance()->profitContractBakGetAll  已废弃
        return null;
    }*/

    public String getSub(String fundSource) {
        if (TechplayStringUtil.isContain(vocConfig.getJqzmEncipher(), fundSource)) {
            return "尊敬的客户：\n" +
                    "\n" +
                    "      您好！附件为您的贷款结清证明，为保证数据安全，资料进行加密处理，密码为您身份证后6位。祝您生活愉快！";
        }
        return "尊敬的客户：\n" +
                "\n" +
                "      您好！附件为您的贷款结清证明，祝您生活愉快！";
    }

    private List<String> lookPzFile(String orderNumber, Long id, LookFileResp response) {
        DocumentRecord dr = documentRecordMapper.selectById(id);
        if (Objects.equals(1, dr.getAccess())) {
            ContractQueryRequest req = buildContractQueryReq(orderNumber, VocConstants.BIZ_TYPE, VocConstants.CONTRACT_LOAN_TYPE);
            List<ContractVO> s = contractCoreClient.listQuery(req);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(s)) {
                return s.stream().map(ContractVO::getDownloadUrl).collect(Collectors.toList());
            }
        } else if (Objects.equals(3, dr.getAccess())) {
            if (StringUtils.isNotBlank(dr.getFileUrl())) {
                return Collections.singletonList(ossClient.getOssUrl(dr.getFileUrl()));
            }
            response.setMsg("凭证未上传，请上传后查看");
        }
        return null;
    }

    private List<String> lookZfFile(String orderNumber, Long id, LookFileResp response) {
        DocumentRecord dr = documentRecordMapper.selectById(id);
        ManageOrderDetailRequest o = new ManageOrderDetailRequest();
        o.setOrderNo(orderNumber);
        if (Objects.equals(1, dr.getAccess())) {
            ContractQueryRequest req = buildContractQueryReq(orderNumber, VocConstants.BIZ_TYPE, VocConstants.CONTRACT_TYPE);
            List<ContractVO> s = contractCoreClient.listQuery(req);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(s)) {
                return s.stream().map(ContractVO::getDownloadUrl).collect(Collectors.toList());
            }
        } else if (Objects.equals(3, dr.getAccess())) {
            if (StringUtils.isNotBlank(dr.getFileUrl())) {
                return Collections.singletonList(ossClient.getOssUrl(dr.getFileUrl()));
            }
            response.setMsg("结清证明未上传，请上传后查看");
        }
        return null;
    }


    private String lookXfFile(String orderNumber,Integer type) {
        ContractBaseRequest<DownloadUrlReq> dr = new ContractBaseRequest<>();
        dr.setUa(itlConfig.getContractUA());
        DownloadUrlReq du = new DownloadUrlReq();
        LoanInfoRequest l = new LoanInfoRequest();
        l.setOrderNumber(orderNumber);
        LoanInfoDto data = queryJqzmInfo(l).getData();
        DownloadUrlReq.SettlementRecord lq = getLoanInfoReq(data);
        du.setBizType(getJqzm(data.getAppName(),type,data.getCapital()));
        du.setApp(data.getAppName());
        du.setUserId(data.getUserNo());
        du.setCustNo(data.getCustNo());
        du.setData(lq);
        dr.setArgs(du);
        ContractResponse<DownloadUrlResponse> resp = contractFeignClient.querySettleUrl(dr);
        if (Objects.isNull(resp) || !resp.isSuccess() || Objects.isNull(resp.getResponse())) {
            throw new RuntimeException("get settle url error");
        }
        return resp.getResponse().getDownloadUrl();
    }

    /**
     * @param req:
     * @return void
     * <AUTHOR>
     * @description 放款凭证申请
     * @date 2024/6/6 16:24
     */
    private ImmutablePair<String, List<String>> getInvestorLoanDisbursementCertificate(SettlementCertificateReq req) {
        StringBuilder msg = new StringBuilder();
        List<String> ods = new ArrayList<>();
        for (LoanInfoRequest order : req.getOrders()) {
            try {
                if (vocConfig.isContractNewInterface()) {
                    ContractProofApplyRequest ca = buildContractReq(order.getOrderNumber(), VocConstants.BIZ_TYPE, VocConstants.CONTRACT_LOAN_TYPE);
                    String s = contractCoreClient.applyProofBeforeQuery(ca);
                    if (!Objects.equals(VocConstants.SUCCESS, s)) {
                        ods.add(order.getOrderNumber());
                        msg.append("    " + order.getOrderNumber());
                        order.setAccess(3);
                    } else {
                        order.setAccess(1);
                    }
                    continue;
                }
                BaseUserCenterRequest<ApplyLoanProofReq> request = new BaseUserCenterRequest<>();
                ApplyLoanProofReq r = new ApplyLoanProofReq();
                r.setOrder_numbers(order.getOrderNumber());
                request.setArgs(r);
                request.setUa(VocConstants.APP_NAME);
                request.setSign(VocConstants.APP_NAME);
                BaseUserCenterResponse<ApplyLoanProofResp> response = fundOrderFacade.applyLoanProof(request);
                log.info(LogUtil.clientLog("fundOrderFacade", "applyLoanProof", req, response));
                if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                    throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, response.getMessage(), ErrorLevelsEnum.ERROR);
                }
                ApplyLoanProofResp ap = response.getResponse();
                if (Objects.equals("failed", ap.getStatus())) {
                    msg.append(order.getOrderNumber() + ap.getMessage());
                    order.setAccess(3);
                } else {
                    order.setAccess(1);
                }

            } catch (Exception e) {
                ods.add(order.getOrderNumber());
                msg.append(order.getOrderNumber()).append(e.getMessage());
                log.warn("applyLoanProof return error", e);
            }
        }
        return ImmutablePair.of(msg.toString(), ods);
    }

    /**
     * @param req:
     * @return void
     * <AUTHOR>
     * @description 资方结清证明申请 特殊资方发邮件，非特殊资方走接口
     * @date 2024/6/6 16:24
     */
    private ImmutablePair<String,List<String>> getInvestorSettlementProof(SettlementCertificateReq req) {
        ImmutablePair<String, List<String>> result = ImmutablePair.of("", new ArrayList<>());
        Map<String, List<LoanInfoRequest>> specialList = new HashMap<>();
        List<LoanInfoRequest> notSpecialList = new ArrayList<>();
        req.getOrders().forEach(o -> {
            if (o.isSpecial()) {
                specialList.computeIfAbsent(o.getCapitalPool(), k -> new ArrayList<>()).add(o);
                o.setAccess(3);
            } else {
                o.setAccess(1);
                notSpecialList.add(o);
            }
        });

        if (CollectionUtil.isNotEmpty(notSpecialList)) {
            result = mergeResults(result,pushSettle(notSpecialList),"");
        }
        if (CollectionUtil.isNotEmpty(specialList)) {
            result = mergeResults(result,sendSpecialMail(specialList, req.getName(), Long.valueOf(req.getUserNo())),"");
        }
        return result;
    }


    private ImmutablePair<String, List<String>> mergeResults(ImmutablePair<String, List<String>> original, ImmutablePair<String, List<String>> additional, String keySeparator) {
        List<String> combinedMessages = new ArrayList<>(original.getRight());
        combinedMessages.addAll(additional.getRight());
        String combinedKey = String.join(keySeparator, original.getLeft(), additional.getLeft());

        return new ImmutablePair<>(combinedKey, combinedMessages);
    }

    public ImmutablePair<String, List<String>> sendSpecialMail(Map<String, List<LoanInfoRequest>> specialList, String name, Long userNo) {
        MailConfig.Config mc = null;
        StringBuilder sb = new StringBuilder();
        List<String> ods = Lists.newArrayList();
        try {
            mc = getMailConfig("all");
            if (Objects.isNull(mc)) {
                throw new ClientException("资方未配置邮箱");
            }
        } catch (Exception e) {
            log.warn("getMailConfig error", e);
            sb.append("资方未配置邮箱");
            ods = (specialList.values().stream()
                    .flatMap(List::stream)
                    .map(LoanInfoRequest::getOrderNumber)
                    .collect(Collectors.toList()));
            return ImmutablePair.of(sb.toString(), ods);
        }
        MailConfig.Config finalMc = mc;
        List<String> finalOds = ods;
        specialList.forEach((k, v) -> {
            try {
                List<CapitalMail> mail = getOne(k);
                CapitalTemplate template = loginUserConfig.getCapitalTemplate(k);
                if (CollectionUtils.isEmpty(mail) || Objects.isNull(template)) {
                    throw new ClientException(k + "No mail configuration");
                }
                List<CapitalTemplateField> fields = getTemplateFields(k,userNo, v.stream().map(LoanInfoRequest::getOrderNumber).collect(Collectors.toList()));
                if(Objects.equals("zdxd_cash",k)){
                    template.setText(updateText(k,template.getText()));
                }
                MailData data = MailDataUtil.getData(template, fields);
                MailUtil.sendEmailToSpecial(finalMc.getMail(), finalMc.getPassword(), mail.get(0).getInbox(), mail.get(0).getCbox(),
                        data.getAttachment(), data.getFileName(), "申请结清证明", data.getText(), finalMc.getPort(), "信飞", data.isSendText());
            } catch (Exception e) {
                List<String> os = v.stream().map(LoanInfoRequest::getOrderNumber).collect(Collectors.toList());
                log.warn("sendSpecialMail error", e);
                sb.append("以下订单发送邮件失败：" + String.join(", ", os));
                finalOds.addAll(os);
            }
        });
        return ImmutablePair.of(sb.toString(), finalOds);
    }

    /**
     * 检查订单是否有居间协议
     * @param orderNumbers 订单号列表
     * @return 没有居间协议的订单号列表
     */
    private List<String> checkIntermediaryAgreement(List<String> orderNumbers) {
        List<String> missingOrders = new ArrayList<>();

        for (String orderNumber : orderNumbers) {
            try {
                ContractQueryRequest request = new ContractQueryRequest();
                request.setBizType("loan");
                request.setBizNo(orderNumber);

                List<ContractVO> contracts = contractCoreClient.listQuery(request);

                boolean hasIntermediaryAgreement = contracts.stream()
                    .anyMatch(contract ->
                        ApolloConstant.INTERMEDIARY_CONTRACT_TITLE.equals(contract.getContractName()) &&
                        ApolloConstant.INTERMEDIARY_CONTRACT_SHORT_NAME.equals(contract.getLegacyContractKey())
                    );

                if (!hasIntermediaryAgreement) {
                    missingOrders.add(orderNumber);
                }
            } catch (Exception e) {
                log.error("检查订单{}居间协议时发生错误", orderNumber, e);
                missingOrders.add(orderNumber);
            }
        }

        return missingOrders;
    }

    /**
     * 获取订单的居间协议
     * @param orderNumber 订单号
     * @return 居间协议下载链接列表
     */
    private List<String> getIntermediaryAgreementUrls(String orderNumber) {
        try {
            ContractQueryRequest request = new ContractQueryRequest();
            request.setBizType("loan");
            request.setBizNo(orderNumber);

            List<ContractVO> contracts = contractCoreClient.listQuery(request);

            return contracts.stream()
                .filter(contract ->
                    ApolloConstant.INTERMEDIARY_CONTRACT_TITLE.equals(contract.getContractName()) &&
                    ApolloConstant.INTERMEDIARY_CONTRACT_SHORT_NAME.equals(contract.getLegacyContractKey())
                )
                .map(ContractVO::getDownloadUrl)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取订单{}居间协议时发生错误", orderNumber, e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> sendIntermediaryAgreement(SettlementCertificateReq req) {
        if (CollectionUtils.isEmpty(req.getOrders())) {
            return ApiResponse.fail("订单列表不能为空");
        }

        // 检查哪些订单没有居间协议
        List<String> orderNumbers = req.getOrders().stream()
            .map(LoanInfoRequest::getOrderNumber)
            .collect(Collectors.toList());

        List<String> missingOrders = checkIntermediaryAgreement(orderNumbers);

        if (!missingOrders.isEmpty()) {
            String errorMsg = "订单号" + String.join("、订单号", missingOrders) + "无居间协议，请核实后再发送";
            return ApiResponse.fail(errorMsg);
        }

        try {
            // 获取邮件配置
            MailConfig.Config mc = getMailConfig(req.getOrders().get(0).getApp());

            // 收集所有居间协议文件
            List<String> allUrls = new ArrayList<>();
            for (LoanInfoRequest order : req.getOrders()) {
                List<String> urls = getIntermediaryAgreementUrls(order.getOrderNumber());
                allUrls.addAll(urls);
            }

            if (allUrls.isEmpty()) {
                return ApiResponse.fail("未找到任何居间协议文件");
            }

            // 发送邮件
            String subject = "平台居间协议" + DateUtil.format(new Date(), "yyyyMMdd");
            String content = "尊敬的客户：\n\n      您好！附件为您的平台居间协议，祝您生活愉快！\n\n";
            MailUtil.sendTXMail(allUrls, mc.getMail(), req.getMail(), subject, mc.getBox(), content, mc.getPassword(), mc.getPort(), mc.getName());

            // 保存审计信息
            documentRecordService.saveBatchs(req.getOrders().stream().map(order -> DocumentRecord.builder()
                    .orderNo(order.getOrderNumber())
                    .userNo(req.getUserNo())
                    .createUser(UserContextHolder.getUserIdentify())
                    .type(5) // 居间协议类型
                    .mail(req.getMail())
                    .status(2) // 已发送
                    .capitalPool(order.getCapitalPoolStr())
                    .prinAmt(new BigDecimal(order.getLoanAmount()))
                    .createdTime(LocalDateTime.now())
                    .updatedTime(LocalDateTime.now())
                    .build()).collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("发送居间协议邮件失败,req:{}", JsonUtil.toJson(req), e);
            return ApiResponse.fail(e.getMessage());
        }

        return ApiResponse.success(Boolean.TRUE);
    }

    private String updateText(String capital,String text) {
        String key= LoginUserConstants.CAPITAL__KEY+capital;
        Long increment = redisUtils.increment(key, 1L);
        String currentDate = LocalDateTimeUtils.getCurrentDate();
        return text.replace("#{time}", currentDate+"第"+increment+"批");
    }

    private List<CapitalTemplateField> getTemplateFields(String capital,Long userNo, List<String> orders) {
        ThreeElementsDTO idNoDTO;
        String channel =null;
        String asset =null;
        List<QueryLoanDetailResp> queryLoanDetailResps = null;
        List<CapitalTemplateField> res = Lists.newArrayList();
        if (Objects.nonNull(userNo)) {
             idNoDTO = cisFacadeClient.queryThreeElementsByUserNo(userNo);
        } else {
            idNoDTO = null;
        }
        if (CollectionUtil.isNotEmpty(orders)) {
            queryLoanDetailResps = lcsFeignClient.queryLoanInfo(getQueryLoanReq(orders));
        }
        LocalDate now = LocalDate.now();
        String currentDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy 年 MM 月 d 日"));
        String applyDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String registerDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy/M/d"));

        if(Objects.equals("by_cash",capital) || Objects.equals("tb_cash",capital)){
            channel="003668";
        }
        if(Objects.equals("wsm_yc_cash",capital)){
            asset="XYF_24_YCB";
        }else if(Objects.equals("wsm_dxal_cash",capital)){
            asset="XYF_22";
        }
        if(CollectionUtil.isNotEmpty(queryLoanDetailResps)){
            for (int i = 0; i < queryLoanDetailResps.size(); i++) {
                QueryLoanDetailResp.LoanInfo m = queryLoanDetailResps.get(i).getLoanInfo();
                CapitalTemplateField c = new CapitalTemplateField();
                c.setName(idNoDTO.getName());
                c.setIdNo(idNoDTO.getIdNo());
                c.setTel(idNoDTO.getMobile());
                c.setAmount(m.getLoanAmt());
                c.setLoanNo(m.getLoanNo());
                c.setLoanNoZ(m.getThirdLoanNo());
                c.setOrderNoZ("XT"+m.getLoanNo());
                if(StringUtils.isNotBlank(channel)){
                    c.setChannel(channel);
                }
                if(StringUtils.isNotBlank(asset)){
                    c.setAsset(asset);
                }
                c.setBeginTime(m.getDateLoan());
                c.setBeginTimeY(LocalDateTimeUtils.formatToChineseDate(m.getDateLoan()));
                c.setBeginTimeS(LocalDateTimeUtils.formatToSlashDate(m.getDateLoan()));

                c.setEndTime(m.getDateEnd());
                c.setEndTimeY(LocalDateTimeUtils.formatToChineseDate(m.getDateEnd()));
                c.setEndTimeS(LocalDateTimeUtils.formatToSlashDate(m.getDateEnd()));

                c.setDateSettle(m.getDateSettle());
                c.setDateSettleY(LocalDateTimeUtils.formatToChineseDate(m.getDateSettle()));
                c.setDateSettleS(LocalDateTimeUtils.formatToSlashDate(m.getDateSettle()));

                c.setDateCash(m.getDateCash());
                c.setDateCashY(LocalDateTimeUtils.formatToChineseDate(m.getDateCash()));
                c.setDateCashS(LocalDateTimeUtils.formatToSlashDate(m.getDateCash()));

                c.setCurrentDate(currentDate);
                c.setApplyDate(applyDate);
                c.setRegisterDate(registerDate);

                c.setNumber(i+1);
                res.add(c);
            }
        }
        return res;
    }

    private QueryLoanReq getQueryLoanReq(List<String> orders) {
        QueryLoanReq q = new QueryLoanReq();
        q.setLoanNoList(orders);
        q.setNeedPlan(false);
        return q;
    }

    public boolean isSpecialCapital(String capital) {
        return Arrays.stream(vocConfig.getJqzmConfig().split(","))
                .anyMatch(part -> Objects.equals(part.trim(), capital));
    }

    private ImmutablePair<String, List<String>> pushSettle(List<LoanInfoRequest> notSpecialList) {
        StringBuilder msg = new StringBuilder();
        List<String> orderIds = Lists.newArrayList();
        notSpecialList.forEach(order -> {
            try {
                if (vocConfig.isContractNewInterface()) {
                    ContractProofApplyRequest req = buildContractReq(order.getOrderNumber(), VocConstants.BIZ_TYPE, VocConstants.CONTRACT_TYPE);
                    String s = contractCoreClient.applyProofBeforeQuery(req);
                    if (!Objects.equals(VocConstants.SUCCESS, s)) {
                        orderIds.add(order.getOrderNumber());
                        msg.append("    " + order.getOrderNumber());
                    }
                } else if (vocConfig.isJqzmFlag() && !isSpecialCapital(order.getCapitalPool())) {
                    SettleCertApplyRequest req = new SettleCertApplyRequest();
                    req.setTemplateCodes(Collections.singletonList("SETTLE"));
                    req.setLoanNos(Collections.singletonList(order.getOrderNumber()));
                    req.setFundSource(order.getCapitalPool());
                    fundCoreClient.settleCertApply(req);
                } else {
                    BaseUserCenterRequest<ApplyLoanProofReq> request = new BaseUserCenterRequest<>();
                    ApplyLoanProofReq r = new ApplyLoanProofReq();
                    r.setOrder_numbers(order.getOrderNumber());
                    request.setArgs(r);
                    request.setUa(VocConstants.APP_NAME);
                    request.setSign(VocConstants.APP_NAME);
                    BaseUserCenterResponse<ApplyOnlineProofResp> response = fundOrderFacade.applyOnlineProof(request);
                    log.info(LogUtil.clientLog("fundOrderFacade", "applyOnlineProof", request, response));
                    if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getResponse())) {
                        throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, "fundOrderFacade.applyOnlineProof return error", ErrorLevelsEnum.ERROR);
                    }
                    ApplyOnlineProofResp ap = response.getResponse();
                    if (Objects.equals(Boolean.FALSE, ap.isStatus())) {
                        msg.append(order.getOrderNumber() + " " + ap.getMsg());
                        // orderIds.add(order.getOrderNumber());
                    }
                }
            } catch (Exception e) {
                log.warn("pushSettle error", e);
                orderIds.add(order.getOrderNumber());
                msg.append(order.getOrderNumber()).append(e.getMessage());
            }
        });
        return ImmutablePair.of(msg.toString(), orderIds);
    }

    private ContractProofApplyRequest buildContractReq(String orderNumber, String bizType, String settlementProof) {
        ContractProofApplyRequest req = new ContractProofApplyRequest();
        req.setBizNo(orderNumber);
        req.setBizType(bizType);
        req.setContractTypeList(Collections.singletonList(settlementProof));

        return req;
    }

    private ContractQueryRequest buildContractQueryReq(String orderNumber, String bizType, String settlementProof) {
        ContractQueryRequest req = new ContractQueryRequest();
        req.setBizNo(orderNumber);
        req.setBizType(bizType);
        req.setContractTypeList(Collections.singletonList(settlementProof));

        return req;
    }

    /**
     * @param req
     * @return List<String>
     * <AUTHOR>
     * @description 获取结清证明接口文档--->https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001005637
     * @date 2024/6/4 16:45
     */
    private List<String> getSettleUrl(SettlementCertificateReq req) {
        List<String> urls = Lists.newArrayList();
        req.getOrders().forEach(order -> {
            if (StringUtils.isEmpty(order.getOrderNumber())) {
                throw new IllegalArgumentException("orderNumber is required.");
            }
            order.setType(Objects.equals(4,req.getType()) && !vocConfig.getMzxdConfig().equals(order.getCapitalPool())?1:req.getType());
            ContractBaseRequest<DownloadUrlReq> dr = new ContractBaseRequest<>();
            LoanInfoRequest l = new LoanInfoRequest();
            l.setOrderNumber(order.getOrderNumber());
            dr.setUa(itlConfig.getContractUA());
            DownloadUrlReq du = new DownloadUrlReq();
            du.setBizType(getJqzm(order.getApp(),order.getType(),order.getCapitalPool()));
            du.setApp(order.getApp());
            du.setUserId(req.getUserNo());
            du.setCustNo(req.getCustNo());
            du.setData(getLoanInfoReq(queryJqzmInfo(l).getData()));
            dr.setArgs(du);
            ContractResponse<DownloadUrlResponse> resp = contractFeignClient.querySettleUrl(dr);
            log.info(LogUtil.clientLog("contractFeignClient", "querySettleUrl", dr, resp));
            if (!resp.isSuccess() || Objects.isNull(resp.getResponse()) || StringUtils.isBlank(resp.getResponse().getDownloadUrl())) {
                throw new RuntimeException("contractFeignClient querySettleUrl return exception");
            }
            urls.add(resp.getResponse().getDownloadUrl());
        });
        return urls;
    }

    private DownloadUrlReq.SettlementRecord getLoanInfoReq(LoanInfoDto lr) {
        DownloadUrlReq.SettlementRecord d = new DownloadUrlReq.SettlementRecord();
        //d.setName(UnicodeUtil.toUnicode(lr.getName()).replace("/","\\/"));
        d.setName(lr.getName());
        d.setAmount(lr.getAmount().multiply(new BigDecimal("100")).toString());
        d.setOrderNumber(lr.getOrderNumber());
        d.setLoanYear(lr.getLoanYear());
        d.setLoanMonth(lr.getLoanMonth());
        d.setLoanDay(lr.getLoanDay());
        d.setLoanEndYear(lr.getLoanEndYear());
        d.setLoanEndMonth(lr.getLoanEndMonth());
        d.setLoanEndDay(lr.getLoanEndDay());
        d.setAppName(lr.getApp());
        d.setPayYear(lr.getPayYear());
        d.setPayMonth(lr.getPayMonth());
        d.setPayDay(lr.getPayDay());
        return d;
    }

    private String getAppName(String app) {
        List<ContractBaseDataDto.Data> appList = loginUserConfig.getContractBaseDataDto().getAppList();
        return appList.stream()
                .filter(p -> Objects.equals(app, p.getKey()))
                .map(ContractBaseDataDto.Data::getAlias)
                .findFirst()
                .orElse("信用飞");
    }

    private String getJqzm(String app,Integer type,String capitalPool) {
        if(Objects.equals(4,type) && Objects.equals(vocConfig.getMzxdConfig(),capitalPool)){
            return "jqzm004";
        }
        List<ContractBaseDataDto.Data> appList = loginUserConfig.getContractBaseDataDto().getAppList();
        return appList.stream()
                .filter(p -> Objects.equals(app, p.getKey()))
                .map(ContractBaseDataDto.Data::getJqzm)
                .findFirst()
                .orElse("jqzm001");
    }


    private MailConfig.Config getMailConfig(String app) {
        List<MailConfig.Config> config = loginUserConfig.getMailConfig().getConfig();
        MailConfig.Config res = config.stream()
                .filter(p -> Arrays.asList(p.getApp().split(",")).contains(app))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(res)) {
            throw new IllegalArgumentException("getMailConfig app is not supported.");
        }
        QueryWrapper<SendMail> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("FIND_IN_SET('" + app + "', send_app)").eq("is_deleted", 0);
        List<SendMail> sendMail = sendMailMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(sendMail)) {
            throw new IllegalArgumentException("getMailConfig SendMail, app is not supported.");
        }
        res.setName(sendMail.get(0).getSender());
        res.setBox(sendMail.get(0).getCbox());
        return res;
    }


    private String validateParam(QueryContractListRequest request) {
        if (request == null) {
            return "param is required.";
        }
        if (StringUtils.isEmpty(request.getContractNo())) {
            return "contractNo is required.";
        }

        return null;
    }

    private List<ContractDetailDto> queryContractDetailInfo(QueryContractListRequest request) {
        List<ContractDetail> contractDetails = queryContractDetail(request);
        if (CollectionUtils.isEmpty(contractDetails)) {
            return Collections.emptyList();
        }

        Map<String, String> downloadMap = queryContractDownLoadUrl(request);

        return convert(contractDetails, downloadMap);
    }

    private List<ContractDetail> queryContractDetail(QueryContractListRequest request) {
        ContractBaseRequest<CashSubContractListRequest> cashContractQuery = new ContractBaseRequest<>();
        CashSubContractListRequest contractListRequest = new CashSubContractListRequest();
        contractListRequest.setId(request.getContractNo());
        cashContractQuery.setArgs(contractListRequest);
        cashContractQuery.setUa(itlConfig.getContractUA());

        ContractResponse<SubContractList<ContractDetail>> response = contractFeignClient.queryCashSubContractList(cashContractQuery);
        log.info("query contract detail info, request={},response={}", request, response);
        if (!response.isSuccess()) {
            return Collections.emptyList();
        }


        return response.getResponse().getList();
    }

    public Map<String, String> queryContractDownLoadUrl(QueryContractListRequest request) {
        List<ContractDownloadDetail> downloadDetails = queryContractDownloadInfo(request);
        if (CollectionUtils.isEmpty(downloadDetails)) {
            return Collections.emptyMap();
        }

        Map<String, String> downloadMap = downloadDetails.stream().collect(Collectors.toMap(ContractDownloadDetail::getShortName, ContractDownloadDetail::getDownloadUrl));
        return downloadMap;
    }

    private List<ContractDownloadDetail> queryContractDownloadInfo(QueryContractListRequest request) {
        ContractBaseRequest<ContractDownloadRequest> downloadQuery = new ContractBaseRequest<>();
        ContractDownloadRequest downloadRequest = new ContractDownloadRequest();
        downloadRequest.setId(request.getContractNo());
        downloadQuery.setArgs(downloadRequest);
        downloadQuery.setUa(itlConfig.getContractUA());

        ContractResponse<SubContractList<ContractDownloadDetail>> response = contractFeignClient.queryCashSubContractDownloadInfo(downloadQuery);
        log.info("query contract download info,request={},response={}", request, response);
        if (!response.isSuccess()) {
            return Collections.emptyList();
        }

        return response.getResponse().getList();
    }

    private List<ContractDetailDto> convert(List<ContractDetail> details, Map<String, String> downloadMap) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }

        List<ContractDetailDto> result = Lists.newArrayListWithCapacity(details.size());
        ContractDetailDto dto;
        for (ContractDetail detail : details) {
            dto = new ContractDetailDto();
            BeanUtils.copyProperties(detail, dto);
            dto.setDownloadUrl(getDownloadUrl(detail.getShortName(), downloadMap));
            result.add(dto);
        }

        return result;
    }

    private String getDownloadUrl(String shortName, Map<String, String> downloadMap) {
        if (CollectionUtils.isEmpty(downloadMap)) {
            return null;
        }

        return downloadMap.get(shortName);
    }
}
