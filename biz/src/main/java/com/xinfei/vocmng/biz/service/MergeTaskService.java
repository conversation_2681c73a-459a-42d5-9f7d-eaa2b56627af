package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.rr.Customer;
import com.xinfei.vocmng.itl.rr.ExportCustomerResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ MergeTaskService, v 0.1 2025/4/30 15:27 shaohui.chen Exp $
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MergeTaskService {

    private static final long   WEB_TOKEN_SET_TTL_DAYS = 30;  // webToken Set的过期时间

    private final StringRedisTemplate redisTemplate;
    private final UdeskClientService udeskClientService;
    private final MergeQueueService mergeQueueService;

    /**
     * 外部接口调用：推送待合并的webToken集合
     *
     * @param filterId 客户过滤器ID，用于从UDesk获取客户数据
     * @param query 关键字搜索，可选（与filterId互斥）
     * @return 成功推送的webToken数量
     */
    public Map<String, Integer> pushWebTokens(Long filterId, String query) {
        // 1. 从UDesk获取客户数据并提取webToken
        Set<String> webTokens = extractAndStoreWebTokens(filterId, query);

        if (webTokens.isEmpty()) {
            log.warn("No webTokens extracted from UDesk with filterId={}, query={}", filterId, query);
            Map<String, Integer> result = new HashMap<>();
            result.put("total", 0);
            result.put("success", 0);
            return result;
        }

        // 2. 批量推送webToken到Redis队列
        Map<String, Integer> pushResult = pushWebTokenList(new ArrayList<>(webTokens));

        log.info("Pushed webTokens to merge queue. Total: {}, Success: {}, Processing: {}, Duplicate: {}, Error: {}",
                pushResult.get("total"),
                pushResult.get("success"),
                pushResult.get("processing"),
                pushResult.get("duplicate"),
                pushResult.get("error"));

        return pushResult;
    }

    /**
     * 推送单个webToken到Redis队列
     *
     * @param webToken 要推送的webToken
     * @return 推送结果
     */
    public String pushSingleWebToken(String webToken) {
        if (StringUtils.isBlank(webToken)) {
            return "ERROR";
        }
        return mergeQueueService.pushTask(webToken);
    }

    /**
     * 批量推送webToken到Redis队列
     *
     * @param webTokens 要推送的webToken列表
     * @return 推送结果统计
     */
    public Map<String, Integer> pushWebTokenList(List<String> webTokens) {
        if (CollectionUtils.isEmpty(webTokens)) {
            log.warn("Cannot push empty webToken list");
            return Collections.singletonMap("error", 0);
        }

        return mergeQueueService.pushTaskList(webTokens);
    }

    /**
     * 从UDesk导出客户数据，提取webToken并存入Redis Set
     *
     * @param filterId 客户过滤器ID，首次调用可选
     * @param query 关键字搜索，可选（与filterId互斥）
     * @return 提取的webToken集合
     */
    public Set<String> extractAndStoreWebTokens(Long filterId, String query) {
        Set<String> webTokens = new HashSet<>();
        String scrollId = null;

        try {
            do {
                // 调用UDesk API获取客户数据
                ExportCustomerResponse response = udeskClientService.exportCustomers(
                        filterId,
                        query,
                        scrollId
                );

                // 检查响应是否有效
                if (response == null || response.getCode() != 1000) {
                    log.error("Failed to export customers from UDesk, response: {}", response);
                    break;
                }

                // 提取webToken
                List<Customer> customers = response.getCustomers();
                if (CollectionUtils.isNotEmpty(customers)) {
                    for (Customer customer : customers) {
                        String webToken = customer.getWebToken();
                        if (StringUtils.isNotBlank(webToken)) {
                            webTokens.add(webToken);
                            // 将webToken存入Redis Set
                            redisTemplate.opsForSet().add(RedisKeyConstants.WEB_TOKEN_SET_KEY, webToken);
                        }
                    }
                }

                // 设置Redis Set的过期时间
                redisTemplate.expire(RedisKeyConstants.WEB_TOKEN_SET_KEY, WEB_TOKEN_SET_TTL_DAYS, TimeUnit.DAYS);

                // 更新scrollId，准备获取下一批数据
                scrollId = response.getScrollId();

                log.info("Extracted {} webTokens from UDesk, total: {}, scrollId: {}",
                        customers != null ? customers.size() : 0,
                        webTokens.size(),
                        scrollId);

            } while (StringUtils.isNotBlank(scrollId));

        } catch (Exception e) {
            log.error("Error extracting webTokens from UDesk", e);
        }

        return webTokens;
    }

    /**
     * 获取已存储的所有webToken
     *
     * @return 所有webToken的集合
     */
    public Set<String> getAllWebTokens() {
        return redisTemplate.opsForSet().members(RedisKeyConstants.WEB_TOKEN_SET_KEY);
    }


    /**
     * 标记任务完成
     *
     * @param webToken 完成的webToken
     */
    public void markTaskComplete(String webToken) {
        if (StringUtils.isBlank(webToken)) {
            return;
        }
        mergeQueueService.markTaskComplete(webToken);
    }

    /**
     * 定时任务，清理处理超时的任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedDelay = 300000) // 5分钟
    public void cleanupStaleTasks() {
        log.info("Starting to cleanup stale tasks");
        mergeQueueService.cleanupStaleTasks();
        log.info("Finished cleaning up stale tasks");
    }
}
