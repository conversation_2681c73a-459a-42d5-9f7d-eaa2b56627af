################ 1 app comm ################
server.port=8080
################ 2 Spring comm ################
# Bean dependence
spring.main.allow-circular-references=true
# spring mvc
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
# json
spring.jackson.default-property-inclusion=non_null
################ 3 spring default datasource ################
spring.datasource.maxActive=50
spring.datasource.initialSize=10
spring.datasource.minIdle=10
spring.datasource.timeBetweenEvictionRunsMillis=600000
spring.datasource.minEvictableIdleTimeMillis=600000
################ 4 mybatis ################
mybatis.configuration.cache-enabled=false
mybatis-plus.global-config.banner=false
mybatis-plus.mapper-locations=classpath*:mapper/*Mapper.xml
mybatis-plus.global-config.db-config.column-format=`%s`
################ 5 biz config ################
# vocmng remote service
xf.vocmng.url=http://localhost:8080
xf.techplaycore.url=http://localhost:8088
# root level
vocmng.logging.level=INFO
# app work dir
vocmng.logging.home=../logs/vocmng
