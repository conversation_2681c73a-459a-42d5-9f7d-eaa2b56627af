/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * VocmngService 验证身份证后6位方法的单元测试
 *
 * <AUTHOR>
 * @version $ VocmngServiceVerifyIdCardTest, v 0.1 2025/5/30 19:00 shaohui.chen Exp $
 */
@ExtendWith(MockitoExtension.class)
class VocmngServiceVerifyIdCardTest {

    @Mock
    private CisFacadeClient cisFacadeClient;

    @InjectMocks
    private VocmngService vocmngService;

    private static final String VALID_MOBILE = "13800138000";
    private static final String VALID_ID_CARD_LAST_6 = "123456";
    private static final String INVALID_ID_CARD_LAST_6 = "654321";
    private static final Long USER_NO_XYF = 1001L;
    private static final Long USER_NO_XYF01 = 1002L;

    @BeforeEach
    void setUp() {
        // 设置模拟数据
    }

    /**
     * 测试验证身份证后6位 - 参数为空的情况
     */
    @Test
    void testVerifyIdCardLast6WithEmptyParams() {
        // 测试手机号为空
        assertFalse(vocmngService.verifyIdCardLast6("", VALID_ID_CARD_LAST_6));

        // 测试身份证后6位为空
        assertFalse(vocmngService.verifyIdCardLast6(VALID_MOBILE, ""));

        // 测试两个参数都为空
        assertFalse(vocmngService.verifyIdCardLast6("", ""));

        // 验证cisFacadeClient.queryUserList没有被调用
        verify(cisFacadeClient, never()).queryUserList(anyString(), isNull(), isNull(), anyInt(), anyInt());
    }

    /**
     * 测试验证身份证后6位 - 没有找到用户的情况
     */
    @Test
    void testVerifyIdCardLast6WithNoUsersFound() {
        // 模拟cisFacadeClient.queryUserList返回空结果
        PageResult<UserSearchDTO> emptyResult = new PageResult<>();
        emptyResult.setList(Collections.emptyList());
        emptyResult.setTotal(0L);
        emptyResult.setPageSize(10);
        emptyResult.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(emptyResult);

        // 执行测试
        boolean result = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertFalse(result);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, never()).queryThreeElementsByUserNo(anyLong());
    }

    /**
     * 测试验证身份证后6位 - 没有找到XYF或XYF01用户的情况
     */
    @Test
    void testVerifyIdCardLast6WithNoXyfUsersFound() {
        // 创建一个非XYF/XYF01的用户
        List<UserSearchDTO> userList = new ArrayList<>();
        UserSearchDTO otherUser = new UserSearchDTO();
        otherUser.setApp("other_app");
        otherUser.setUserNo(9999L);
        userList.add(otherUser);

        // 模拟cisFacadeClient.queryUserList返回非XYF/XYF01用户
        PageResult<UserSearchDTO> result = new PageResult<>();
        result.setList(userList);
        result.setTotal(1L);
        result.setPageSize(10);
        result.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(result);

        // 执行测试
        boolean verifyResult = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertFalse(verifyResult);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, never()).queryThreeElementsByUserNo(anyLong());
    }

    /**
     * 测试验证身份证后6位 - 身份证后6位匹配的情况
     */
    @Test
    void testVerifyIdCardLast6WithMatchingIdCard() {
        // 创建XYF用户
        List<UserSearchDTO> userList = new ArrayList<>();
        UserSearchDTO xyfUser = new UserSearchDTO();
        xyfUser.setApp(VocConstants.APP_XYF);
        xyfUser.setUserNo(USER_NO_XYF);
        userList.add(xyfUser);

        // 模拟cisFacadeClient.queryUserList返回XYF用户
        PageResult<UserSearchDTO> result = new PageResult<>();
        result.setList(userList);
        result.setTotal(1L);
        result.setPageSize(10);
        result.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(result);

        // 模拟cisFacadeClient.queryThreeElementsByUserNo返回带有匹配身份证的ThreeElementsDTO
        ThreeElementsDTO threeElementsDTO = new ThreeElementsDTO();
        threeElementsDTO.setIdNo("123456789012345678"); // 身份证号，后6位是123456
        when(cisFacadeClient.queryThreeElementsByUserNo(USER_NO_XYF)).thenReturn(threeElementsDTO);

        // 执行测试
        boolean verifyResult = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertTrue(verifyResult);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, times(1)).queryThreeElementsByUserNo(USER_NO_XYF);
    }

    /**
     * 测试验证身份证后6位 - 身份证后6位不匹配的情况
     */
    @Test
    void testVerifyIdCardLast6WithNonMatchingIdCard() {
        // 创建XYF用户
        List<UserSearchDTO> userList = new ArrayList<>();
        UserSearchDTO xyfUser = new UserSearchDTO();
        xyfUser.setApp(VocConstants.APP_XYF);
        xyfUser.setUserNo(USER_NO_XYF);
        userList.add(xyfUser);

        // 模拟cisFacadeClient.queryUserList返回XYF用户
        PageResult<UserSearchDTO> result = new PageResult<>();
        result.setList(userList);
        result.setTotal(1L);
        result.setPageSize(10);
        result.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(result);

        // 模拟cisFacadeClient.queryThreeElementsByUserNo返回带有不匹配身份证的ThreeElementsDTO
        ThreeElementsDTO threeElementsDTO = new ThreeElementsDTO();
        threeElementsDTO.setIdNo("123456789012654321"); // 身份证号，后6位是654321
        when(cisFacadeClient.queryThreeElementsByUserNo(USER_NO_XYF)).thenReturn(threeElementsDTO);

        // 执行测试
        boolean verifyResult = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertFalse(verifyResult);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, times(1)).queryThreeElementsByUserNo(USER_NO_XYF);
    }

    /**
     * 测试验证身份证后6位 - 多个用户中有一个匹配的情况
     */
    @Test
    void testVerifyIdCardLast6WithMultipleUsersOneMatching() {
        // 创建多个用户，包括XYF和XYF01
        List<UserSearchDTO> userList = new ArrayList<>();

        UserSearchDTO xyfUser = new UserSearchDTO();
        xyfUser.setApp(VocConstants.APP_XYF);
        xyfUser.setUserNo(USER_NO_XYF);
        userList.add(xyfUser);

        UserSearchDTO xyfUser01 = new UserSearchDTO();
        xyfUser01.setApp(VocConstants.APP_XYF01);
        xyfUser01.setUserNo(USER_NO_XYF01);
        userList.add(xyfUser01);

        // 模拟cisFacadeClient.queryUserList返回多个用户
        PageResult<UserSearchDTO> result = new PageResult<>();
        result.setList(userList);
        result.setTotal(2L);
        result.setPageSize(10);
        result.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(result);

        // 模拟第一个用户的身份证不匹配
        ThreeElementsDTO threeElementsDTO1 = new ThreeElementsDTO();
        threeElementsDTO1.setIdNo("123456789012654321"); // 身份证号，后6位是654321
        when(cisFacadeClient.queryThreeElementsByUserNo(USER_NO_XYF)).thenReturn(threeElementsDTO1);

        // 模拟第二个用户的身份证匹配
        ThreeElementsDTO threeElementsDTO2 = new ThreeElementsDTO();
        threeElementsDTO2.setIdNo("123456789012123456"); // 身份证号，后6位是123456
        when(cisFacadeClient.queryThreeElementsByUserNo(USER_NO_XYF01)).thenReturn(threeElementsDTO2);

        // 执行测试
        boolean verifyResult = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertTrue(verifyResult);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, times(1)).queryThreeElementsByUserNo(USER_NO_XYF);
        verify(cisFacadeClient, times(1)).queryThreeElementsByUserNo(USER_NO_XYF01);
    }

    /**
     * 测试验证身份证后6位 - 查询三要素时发生异常的情况
     */
    @Test
    void testVerifyIdCardLast6WithExceptionDuringThreeElementsQuery() {
        // 创建XYF用户
        List<UserSearchDTO> userList = new ArrayList<>();
        UserSearchDTO xyfUser = new UserSearchDTO();
        xyfUser.setApp(VocConstants.APP_XYF);
        xyfUser.setUserNo(USER_NO_XYF);
        userList.add(xyfUser);

        // 模拟cisFacadeClient.queryUserList返回XYF用户
        PageResult<UserSearchDTO> result = new PageResult<>();
        result.setList(userList);
        result.setTotal(1L);
        result.setPageSize(10);
        result.setPageNum(1);
        when(cisFacadeClient.queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt())).thenReturn(result);

        // 模拟cisFacadeClient.queryThreeElementsByUserNo抛出异常
        when(cisFacadeClient.queryThreeElementsByUserNo(USER_NO_XYF)).thenThrow(new RuntimeException("模拟异常"));

        // 执行测试
        boolean verifyResult = vocmngService.verifyIdCardLast6(VALID_MOBILE, VALID_ID_CARD_LAST_6);

        // 验证结果
        assertFalse(verifyResult);
        verify(cisFacadeClient, times(1)).queryUserList(eq(VALID_MOBILE), isNull(), isNull(), anyInt(), anyInt());
        verify(cisFacadeClient, times(1)).queryThreeElementsByUserNo(USER_NO_XYF);
    }
}
