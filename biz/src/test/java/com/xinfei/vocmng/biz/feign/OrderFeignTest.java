package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.http.FundOrderFacade;
import com.xinfei.vocmng.itl.rr.ApplyLoanProofReq;
import com.xinfei.vocmng.itl.rr.ApplyLoanProofResp;
import com.xinfei.vocmng.itl.rr.BaseUserCenterRequest;
import com.xinfei.vocmng.itl.rr.BaseUserCenterResponse;
import com.xinfei.vocmng.itl.rr.OrderListResponse;
import com.xinfei.vocmng.itl.rr.dto.FundOrderArgs;
import com.xinfei.vocmng.itl.rr.dto.FundOrderDto;
import com.xinfei.vocmng.itl.rr.dto.OrderListArgs;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @since 2023/12/25
 */
@Slf4j
public class OrderFeignTest extends TechplayDevTestBase {

    @Autowired
    private FundOrderFacade fundOrderFacade;

    @Test
    public void applyLoanProof() {
        BaseUserCenterRequest<ApplyLoanProofReq> request = new BaseUserCenterRequest<>();
        ApplyLoanProofReq r = new ApplyLoanProofReq();
        r.setOrder_numbers("266290200000000");
        request.setArgs(r);
        request.setUa("voc");
        request.setSign("voc");
        BaseUserCenterResponse<ApplyLoanProofResp> response = fundOrderFacade.applyLoanProof(request);
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void getOrderList() {
        BaseUserCenterRequest<OrderListArgs> request = new BaseUserCenterRequest<>();
        OrderListArgs args = new OrderListArgs();
        args.setOutOrderNumber("266290200000000");
        args.setPage(1);
        args.setPageSize(10);

        request.setArgs(args);

        BaseUserCenterResponse<OrderListResponse> response = fundOrderFacade.getOrderList(request, "vocmng", "123");
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void fundOrder() {
        BaseUserCenterRequest<FundOrderArgs> request = new BaseUserCenterRequest<>();
        FundOrderArgs args = new FundOrderArgs();
        args.setOutOrderNumber("20231207162524021491881");
        request.setArgs(args);
        request.setUa("voc");
        request.setSign("voc");

        BaseUserCenterResponse<FundOrderDto> response = fundOrderFacade.fundOrder(request);
        log.info("query contract download info, response={}", response);
    }
}
