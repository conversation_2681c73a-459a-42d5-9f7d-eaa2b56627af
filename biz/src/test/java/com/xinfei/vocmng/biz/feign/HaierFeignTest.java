/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.remote.HaierComplaintRemoteService;
import com.xinfei.vocmng.biz.workorder.application.command.UpdateWorkOrderStatusCommand;
import com.xinfei.vocmng.biz.workorder.application.service.WorkOrderApplicationService;
import com.xinfei.vocmng.biz.workorder.domain.workorder.valueobject.WorkOrderStatus;
import com.xinfei.vocmng.itl.rr.haier.AddProcessRecordRequest;
import com.xinfei.vocmng.itl.rr.haier.QueryTransferCallResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version $ LcsFeignTest, v 0.1 2024-03-11 11:49 junjie.yan Exp $
 */
@Slf4j
public class HaierFeignTest extends TechplayDevTestBase {
    @Autowired
    private HaierComplaintRemoteService haierComplaintRemoteService;

    @Autowired
    private WorkOrderApplicationService workOrderApplicationService;

    @Test
    public void querySubContract() {
        QueryTransferCallResponse queryTransferCallResponse =
                haierComplaintRemoteService.queryTransferCallInfo("8c2fa989-f5de-48f4-bf8d-464c85c70c1c", ApolloConstant.HAIER_COMPLAINT_COOPER_ORG_NO);
        log.info("{}", queryTransferCallResponse);
    }

    @Test
    public void testUpdateWorkOrderStatus() {
        // 测试通过callId更新工单状态
        UpdateWorkOrderStatusCommand command = UpdateWorkOrderStatusCommand.builder()
                .callId("test-call-id-12345")
                .newStatus(WorkOrderStatus.PROCESSING)
                .reason("测试更新工单状态")
                .externalWorkOrderStatus("2")
                .processTime("2025-08-15 10:30:00")
                .customerStatus("已联系")
                .eventType("FEEDBACK")
                .build();

        try {
            workOrderApplicationService.updateWorkOrderStatus(command);
            log.info("工单状态更新测试成功, callId: {}", command.getCallId());
        } catch (Exception e) {
            log.error("工单状态更新测试失败, callId: {}, error: {}", command.getCallId(), e.getMessage());
        }
    }

    @Test
    public void testUpdateWorkOrderStatusByOrderNo() {
        // 测试通过orderNo更新工单状态
        UpdateWorkOrderStatusCommand command = UpdateWorkOrderStatusCommand.builder()
                .orderNo("WO-2025081500001")
                .newStatus(WorkOrderStatus.COMPLETED)
                .reason("工单处理完成")
                .externalWorkOrderStatus("8")
                .processTime("2025-08-15 15:30:00")
                .customerStatus("问题已解决")
                .eventType("CLOSED")
                .build();

        try {
            workOrderApplicationService.updateWorkOrderStatus(command);
            log.info("通过工单号更新状态测试成功, orderNo: {}", command.getOrderNo());
        } catch (Exception e) {
            log.error("通过工单号更新状态测试失败, orderNo: {}, error: {}", command.getOrderNo(), e.getMessage());
        }
    }

    @Test
    public void testUpdateWorkOrderStatusWithValidation() {
        // 测试参数验证
        UpdateWorkOrderStatusCommand invalidCommand = UpdateWorkOrderStatusCommand.builder()
                // 故意不设置callId和orderNo，触发验证失败
                .newStatus(WorkOrderStatus.PROCESSING)
                .reason("测试参数验证")
                .build();

        try {
            invalidCommand.validate();
            log.error("参数验证应该失败，但没有抛出异常");
        } catch (IllegalArgumentException e) {
            log.info("参数验证测试成功，捕获到预期异常: {}", e.getMessage());
        }
    }

    @Test
    public void testUpdateWorkOrderStatusScenarios() {
        // 测试不同状态转换场景
        String testCallId = "scenario-test-call-id";

        // 场景1：工单创建
        UpdateWorkOrderStatusCommand createCommand = UpdateWorkOrderStatusCommand.builder()
                .callId(testCallId)
                .newStatus(WorkOrderStatus.CREATED)
                .reason("工单已创建")
                .externalWorkOrderStatus("1")
                .eventType("CREATED")
                .build();

        // 场景2：工单处理中
        UpdateWorkOrderStatusCommand processingCommand = UpdateWorkOrderStatusCommand.builder()
                .callId(testCallId)
                .newStatus(WorkOrderStatus.PROCESSING)
                .reason("开始处理工单")
                .externalWorkOrderStatus("2")
                .processTime("2025-08-15 14:00:00")
                .customerStatus("处理中")
                .eventType("FEEDBACK")
                .build();

        // 场景3：工单完成
        UpdateWorkOrderStatusCommand completedCommand = UpdateWorkOrderStatusCommand.builder()
                .callId(testCallId)
                .newStatus(WorkOrderStatus.COMPLETED)
                .reason("工单处理完成")
                .externalWorkOrderStatus("8")
                .processTime("2025-08-15 16:00:00")
                .customerStatus("已解决")
                .eventType("CLOSED")
                .build();

        // 依次执行状态转换测试
        try {
            log.info("开始测试工单状态转换场景");

            // 注意：实际测试中需要确保工单存在，这里只是演示测试结构
            // workOrderApplicationService.updateWorkOrderStatus(createCommand);
            // workOrderApplicationService.updateWorkOrderStatus(processingCommand);
            // workOrderApplicationService.updateWorkOrderStatus(completedCommand);

            log.info("工单状态转换场景测试完成");
        } catch (Exception e) {
            log.error("工单状态转换场景测试失败: {}", e.getMessage());
        }
    }

}