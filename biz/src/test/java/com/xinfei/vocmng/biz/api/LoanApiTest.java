package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderFilterFactorDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LoanApiTest {

    @Resource
    private LoanApi loanApi;

    @Test
    public void getOrderList() {
        GetOrderListRequest request = new GetOrderListRequest();
//        request.setUserNos(Collections.singletonList("111111115822020"));
//        request.setMobile("13083666916");
        request.setPageSize(10);
        request.setCurrentPage(1);
        request.setOrderType("MAIN");
        request.setOutOrderNumber("30197084");
       // request.setChannelOrderNumber("153153");
        ApiResponse<Paging<OrderDto>> pagingApiResponse = loanApi.getOrderList(request);
        System.out.println(pagingApiResponse);
    }

    @Test
    public void getOrderDetail() {
        GetOrderDetailRequest request = new GetOrderDetailRequest();
        request.setOrderNo("2024012800002600000004461856");
        request.setOrderType("PROFIT");
        ApiResponse<OrderDto> pagingApiResponse = loanApi.getOrderDetail(request);
        System.out.println(pagingApiResponse);
    }

    @Test
    public void getBillList() {
        GetBillListRequest request = new GetBillListRequest();
        request.setLoanNos(Collections.singletonList("20231116113740172000992592"));
        ApiResponse<List<LoanPlanDto>> pagingApiResponse = loanApi.getBillList(request);
        System.out.println(pagingApiResponse);
    }

    @Test
    public void getRepayments() {
        GetRepaymentsRequest request = new GetRepaymentsRequest();
        request.setLoanNo(Collections.singletonList("20231227194341021503815"));
        request.setCurrentPage(1);
        ApiResponse<Paging<RepaymentsDto>> pagingApiResponse = loanApi.getRepayments(request);
        System.out.println(pagingApiResponse);
    }

    @Test
    public void getInnerApp() {
        OrderFilterFactorRequest request = new OrderFilterFactorRequest();
        request.setCustNo("CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f");
        ApiResponse<OrderFilterFactorDto> apiResponse = loanApi.getOrderFilterFactor(request);
        System.out.println(apiResponse);
    }
}