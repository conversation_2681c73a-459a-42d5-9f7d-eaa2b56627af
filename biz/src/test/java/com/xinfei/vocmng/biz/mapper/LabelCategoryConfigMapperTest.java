package com.xinfei.vocmng.biz.mapper;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.dal.po.LabelCategoryConfig;
import com.xinfei.vocmng.dal.mapper.LabelCategoryConfigMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023/12/11
 */
public class LabelCategoryConfigMapperTest extends TechplayDevTestBase {
    @Autowired
    private LabelCategoryConfigMapper mapper;

    @Test
    public void doPrint(){
        LabelCategoryConfig config = mapper.selectById(1l);
        System.out.println("print...,config="+config.getName());
    }
}
