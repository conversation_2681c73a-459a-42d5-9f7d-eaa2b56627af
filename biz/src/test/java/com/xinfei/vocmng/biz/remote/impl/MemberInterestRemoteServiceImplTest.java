/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.remote.impl;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.enums.VipPayTypeEnum;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.RightCardRefundDto;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLog;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * MemberInterestRemoteServiceImpl 测试类
 *
 * <AUTHOR>
 * @version $ MemberInterestRemoteServiceImplTest, v 0.1 2025/5/15 16:30 shaohui.chen Exp $
 */

@Slf4j
class MemberInterestRemoteServiceImplTest extends TechplayDevTestBase {

    @Resource
    private MemberInterestRemoteServiceImpl memberInterestRemoteService;


    /**
     * 测试查询订单支付记录
     */
    @Test
    void queryOrderPayLog() {
        // 调用服务方法
        Long orderId = 412L; // 使用一个已知的订单ID
        List<VipOrderPayLogDetailDTO> result = memberInterestRemoteService.queryOrderPayLog(orderId);
        
        // 验证结果
        log.info("查询订单支付记录结果: {}", result);
        
        // 如果有结果，验证字段映射是否正确
        if (!result.isEmpty()) {
            for (VipOrderPayLogDetailDTO dto : result) {
                // 验证支付类型描述是否正确映射
                if (dto.getPayType() != null) {
                    String expectedDesc = VipPayTypeEnum.getDescByCode(dto.getPayType());
                    assertEquals(expectedDesc, dto.getPayTypeDesc(), "支付类型描述映射不正确");
                }
            }
        }
    }
    
    /**
     * 测试查询订单支付记录 - 空结果
     */
    @Test
    void queryOrderPayLogEmpty() {
        // 使用一个不存在的订单ID
        Long nonExistentOrderId = 999999L;
        List<VipOrderPayLogDetailDTO> result = memberInterestRemoteService.queryOrderPayLog(nonExistentOrderId);
        
        // 验证结果为空列表
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "结果应为空列表");
        log.info("查询不存在的订单支付记录结果为空列表，测试通过");
    }
    
    /**
     * 测试查询订单支付记录 - null参数
     */
    @Test
    void queryOrderPayLogNullParam() {
        // 使用null作为订单ID
        List<VipOrderPayLogDetailDTO> result = memberInterestRemoteService.queryOrderPayLog(null);
        
        // 验证结果为空列表
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "结果应为空列表");
        log.info("查询订单ID为null的支付记录结果为空列表，测试通过");
    }

    //退款信息查询
    @Test
    void getRightCardRefund() {
        List<VipCardRefundLog> refundList = new ArrayList<>();
        VipCardRefundLog request = new VipCardRefundLog();
        request.setVipCardId(412L);
        request.setCardType(3);
        refundList.add(request);
        ApiResponse<List<RightCardRefundDto>> response = memberInterestRemoteService.queryRightCardRefund(refundList);
        log.info("退款信息查询结果: {}", response);
    }
}
