package com.xinfei.vocmng.biz.service;

import com.xinfei.repaytrade.facade.rr.enums.CalcSettleTypeEnum;
import com.xinfei.repaytrade.facade.rr.request.RepayLoanCalcRequest;
import com.xinfei.repaytrade.facade.rr.response.MultiRepayLoanCalcResponse;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.dto.LoanCalculateDto;
import com.xinfei.vocmng.biz.rr.request.LoanCalculateRequest;
import com.xinfei.vocmng.biz.rr.request.LoanInfo;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * MultiRepayCalculate 测试类
 *
 * <AUTHOR>
 * @version $ MultiRepayCalculateTest, v 0.1 2024-12-17 test Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class MultiRepayCalculateTest {

    @Resource
    private RepayFacadeClient repayFacadeClient;

    /**
     * 测试多借据还款试算 - 单个借据结清
     */
    @Test
    public void testMultiRepayCalculate_SingleLoanSettle() {
        List<String> loanNos = Collections.singletonList("2025052100126900000079138023");
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("单个借据结清试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - 单个借据还当期
     */
    @Test
    public void testMultiRepayCalculate_SingleLoanNoSettle() {
        List<String> loanNos = Collections.singletonList("2024031200002600000010005285");
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.NO_SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("单个借据还当期试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - 多个借据结清
     */
    @Test
    public void testMultiRepayCalculate_MultipleLoanSettle() {
        List<String> loanNos = Arrays.asList(
            "2024031200002600000010005285",
            "20230919153006021489302"
        );
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("多个借据结清试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - 多个借据还当期
     */
    @Test
    public void testMultiRepayCalculate_MultipleLoanNoSettle() {
        List<String> loanNos = Arrays.asList(
            "2024031200002600000010005285",
            "20230919153006021489302"
        );
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.NO_SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("多个借据还当期试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - 空借据列表
     */
    @Test
    public void testMultiRepayCalculate_EmptyLoanList() {
        List<String> loanNos = Collections.emptyList();
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("空借据列表试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - null借据列表
     */
    @Test
    public void testMultiRepayCalculate_NullLoanList() {
        List<String> loanNos = null;
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        try {
            MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
            System.out.println("null借据列表试算结果: " + response);
        } catch (Exception e) {
            System.out.println("null借据列表异常: " + e.getMessage());
        }
    }

    /**
     * 测试多借据还款试算 - 无效借据号
     */
    @Test
    public void testMultiRepayCalculate_InvalidLoanNo() {
        List<String> loanNos = Collections.singletonList("INVALID_LOAN_NO_123456");
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        try {
            MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
            System.out.println("无效借据号试算结果: " + response);
        } catch (Exception e) {
            System.out.println("无效借据号异常: " + e.getMessage());
        }
    }

    /**
     * 测试多借据还款试算 - 混合有效和无效借据号
     */
    @Test
    public void testMultiRepayCalculate_MixedValidInvalidLoanNos() {
        List<String> loanNos = Arrays.asList(
            "2024031200002600000010005285",
            "INVALID_LOAN_NO_123456"
        );
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        try {
            MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
            System.out.println("混合有效无效借据号试算结果: " + response);
        } catch (Exception e) {
            System.out.println("混合有效无效借据号异常: " + e.getMessage());
        }
    }

    /**
     * 测试多借据还款试算 - 大量借据
     */
    @Test
    public void testMultiRepayCalculate_LargeLoanList() {
        List<String> loanNos = Arrays.asList(
            "2024031200002600000010005285",
            "20230919153006021489302",
            "20240109141719021504209",
            "20230808194442021486306",
            "20230914152328021489011"
        );
        CalcSettleTypeEnum calcSettleTypeEnum = CalcSettleTypeEnum.SETTLE;

        MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, calcSettleTypeEnum);
        System.out.println("大量借据试算结果: " + response);
    }

    /**
     * 测试多借据还款试算 - 不同结算类型组合
     */
    @Test
    public void testMultiRepayCalculate_DifferentSettleTypes() {
        List<String> loanNos = Collections.singletonList("2024031200002600000010005285");

        // 测试所有结算类型
        CalcSettleTypeEnum[] settleTypes = {
            CalcSettleTypeEnum.SETTLE,
            CalcSettleTypeEnum.NO_SETTLE
        };

        for (CalcSettleTypeEnum settleType : settleTypes) {
            try {
                MultiRepayLoanCalcResponse response = repayFacadeClient.multiRepayCalculate(loanNos, settleType);
                System.out.println("结算类型 " + settleType + " 试算结果: " + response);
            } catch (Exception e) {
                System.out.println("结算类型 " + settleType + " 异常: " + e.getMessage());
            }
        }
    }
}
