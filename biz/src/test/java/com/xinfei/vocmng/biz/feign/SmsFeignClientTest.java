package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.http.SmsCenterFeignClient;
import com.xinfei.vocmng.itl.client.feign.impl.SmsFeignService;
import com.xinfei.vocmng.itl.rr.SmsBaseRequest;
import com.xinfei.vocmng.itl.rr.SmsRecordDetail;
import com.xinfei.vocmng.itl.rr.SmsRecordRequest;
import com.xinfei.vocmng.itl.rr.SmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ SmsFeignClientTest, v 0.1 2023/12/27 09:57 qu.lu Exp $
 */
@Slf4j
public class SmsFeignClientTest extends TechplayDevTestBase {

    @Autowired
    private SmsCenterFeignClient smsCenterFeignClient;

    @Autowired
    private SmsFeignService smsFeignService;

    @Test
    public void queryUserSmsRecords(){
        SmsBaseRequest<SmsRecordRequest> request = new SmsBaseRequest<>();
        request.setUa("vocmng");
        SmsRecordRequest recordRequest = new SmsRecordRequest();
        recordRequest.setMobile("13570900976");
//        recordRequest.setSource("xyf_app_api");
        request.setArgs(recordRequest);

        SmsResponse<List<SmsRecordDetail>> response = smsCenterFeignClient.queryUserSmsRecordList(request);
        log.info("query user sms records, response={}",response);
    }

    @Test
    public void sendRobotMsg(){
        List<String> mobiles = new ArrayList<>();
//        mobiles.add("18601465348");
//        mobiles.add("13817224416");
        Boolean response = smsFeignService.sendRobotMsg("新年快乐", "这是最后一个功能测试", mobiles);
        log.info("query user sms records, response={}",response);
    }
}
