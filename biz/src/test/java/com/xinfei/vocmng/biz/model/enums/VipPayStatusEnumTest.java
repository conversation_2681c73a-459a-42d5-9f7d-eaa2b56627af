/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VipPayStatusEnum 测试类
 *
 * <AUTHOR>
 * @version $ VipPayStatusEnumTest, v 0.1 2025/5/30 16:30 shaohui.chen Exp $
 */
class VipPayStatusEnumTest {

    /**
     * 测试根据支付状态编码获取支付状态描述
     */
    @Test
    void testGetDescByCode() {
        // 测试有效的支付状态编码
        assertEquals("待支付", VipPayStatusEnum.getDescByCode("pay_start"));
        assertEquals("支付中", VipPayStatusEnum.getDescByCode("paying"));
        assertEquals("支付失败", VipPayStatusEnum.getDescByCode("pay_fail"));
        assertEquals("支付成功", VipPayStatusEnum.getDescByCode("pay_success"));
        assertEquals("支付取消", VipPayStatusEnum.getDescByCode("pay_cancel"));
        assertEquals("支付关闭", VipPayStatusEnum.getDescByCode("pay_close"));
        
        // 测试大小写不敏感
        assertEquals("待支付", VipPayStatusEnum.getDescByCode("PAY_START"));
        assertEquals("支付中", VipPayStatusEnum.getDescByCode("PAYING"));
        
        // 测试无效的支付状态编码
        assertNull(VipPayStatusEnum.getDescByCode("invalid_status"));
        
        // 测试空值
        assertNull(VipPayStatusEnum.getDescByCode(null));
        assertNull(VipPayStatusEnum.getDescByCode(""));
        assertNull(VipPayStatusEnum.getDescByCode("  "));
    }
}
