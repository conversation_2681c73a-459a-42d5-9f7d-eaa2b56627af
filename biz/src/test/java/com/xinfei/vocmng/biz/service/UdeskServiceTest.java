package com.xinfei.vocmng.biz.service;

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.rr.CreateCustomerResponse;
import com.xinfei.vocmng.itl.rr.Customer;
import com.xinfei.vocmng.itl.rr.ExportCustomerResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerFilterListResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsResponse;
import com.xinfei.vocmng.itl.rr.MergeCustomerResponse;
import com.xinfei.vocmng.itl.rr.udesk.UdeskAuthResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/10
 */
@Slf4j
public class UdeskServiceTest extends TechplayDevTestBase {

    @Autowired
    private UDeskService uDeskService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UdeskClientService udeskClientService;

    @Test
    public void sessionsSync() {
        uDeskService.sessionsSync("2025-03-10 12:00:00","2025-03-10 17:20:00");
    }

    @Test
    public void callSync() {
        uDeskService.callSync("2025-03-10 12:00:00","2025-03-10 17:20:00");
    }

    @Test
    public void orgSync() {
        loginService.syncQualityOrg();
    }

    @Test
    public void staffSync() {
        loginService.syncQualityStaff();
    }

    /**
     * 测试客户批量导出接口
     * 注意：此测试会发送真实请求到UDesk服务
     */
    @Test
    public void testExportCustomers() {
        // 首次调用，使用关键字搜索
        ExportCustomerResponse response = udeskClientService.exportCustomers(16548583L, null, null);
        log.info("Export customers response: {}", JSON.toJSONString(response));

        if (response != null) {
            log.info("Total customers: {}", response.getTotal());
            log.info("Scroll ID: {}", response.getScrollId());
            log.info("Customers count: {}", response.getCustomers() != null ? response.getCustomers().size() : 0);

            // 如果有scrollId，可以继续获取下一批数据
            if (response.getScrollId() != null && response.getCustomers() != null && !response.getCustomers().isEmpty()) {
                ExportCustomerResponse nextResponse = udeskClientService.exportCustomers(16548583L, null, response.getScrollId());
                log.info("Next batch customers count: {}", nextResponse != null && nextResponse.getCustomers() != null ? nextResponse.getCustomers().size() : 0);
            }
        }
    }

    /**
     * 测试获取客户过滤器列表接口
     * 注意：此测试会发送真实请求到UDesk服务
     */
    @Test
    public void testGetCustomerFilters() {
        GetCustomerFilterListResponse response = udeskClientService.getCustomerFilters();
        log.info("Customer filters response: {}", JSON.toJSONString(response));

        if (response != null && response.getCustomerFilters() != null) {
            log.info("Total filters: {}", response.getCustomerFilters().size());

            // 打印每个过滤器的信息
            response.getCustomerFilters().forEach(filter -> {
                log.info("Filter ID: {}, Name: {}, Active: {}",
                        filter.getId(), filter.getName(), filter.isActive());
            });
        }
    }

    /**
     * 测试合并客户接口
     * 注意：此测试会发送真实请求到UDesk服务
     * 请使用真实存在的客户手机号进行测试
     */
    @Test
    public void testMergeCustomers() {
        // 请替换为真实存在的客户手机号
        String fromType = "web_token";
        String fromContent = "1285526966";
        String toType = "web_token";
        String toContent = "1268894869";

        log.info("Merging customer from {} {} to {} {}", fromType, fromContent, toType, toContent);

        MergeCustomerResponse response = udeskClientService.mergeCustomers(
                fromType, fromContent,
                toType, toContent
        );

        log.info("Merge customers response: {}", JSON.toJSONString(response));

        if (response != null) {
            log.info("Merge result code: {}", response.getCode());
            log.info("Merged customer ID: {}", response.getId());
        }
    }

    /**
     * 测试创建客户接口（只包含必填字段）
     * 注意：此测试会发送真实请求到UDesk服务
     */
    @Test
    public void testCreateCustomer() {
        // 创建客户对象，只设置必填字段
        Customer customer = new Customer();
        customer.setNickName("测试客户" + System.currentTimeMillis()); // 使用时间戳确保名称唯一
        customer.setWebToken("1939303089098213329");
        HashMap<String, Object> customFiled = new HashMap<>();
        customFiled.put("TextField_1500643", "1939303089098213329");
        customer.setCustomFields(customFiled);
        log.info("Creating customer with nickname: {}", customer.getNickName());

        // 调用创建客户接口
        CreateCustomerResponse response = udeskClientService.createCustomer(customer);

        log.info("Create customer response: {}", JSON.toJSONString(response));

        if (response != null) {
            log.info("Create result code: {}", response.getCode());
            log.info("Created customer ID: {}", response.getCustomer() != null ? response.getCustomer().getId() : null);
            log.info("Created customer nickname: {}", response.getCustomer() != null ? response.getCustomer().getNickName() : null);
        }
    }

    /**
     * 测试UDesk鉴权接口
     * 注意：此测试会发送真实请求到UDesk服务
     * 请使用真实的appId、sid、secret进行测试
     */
    @Test
    public void testPromiseSign() {
        // 请替换为真实的UDesk CC鉴权参数
        String appId = ApolloConstant.uDeskAppId;
        String sid = ApolloConstant.uDeskSid;
        String secret = ApolloConstant.uDeskSecret;

        log.info("Testing UDesk promise sign with appId: {}, sid: {}", appId, sid);

        try {
            // 调用鉴权接口
            UdeskAuthResponse response = udeskClientService.promiseSign(appId, sid, secret);

            log.info("Promise sign response: {}", JSON.toJSONString(response));

            if (response != null) {
                log.info("Auth result - appId: {}", response.getAppId());
                log.info("Auth result - timestamp: {}", response.getTimestamp());
                log.info("Auth result - token: {}", response.getToken());

                // 验证返回的字段
                assert response.getAppId() != null : "appId should not be null";
                assert response.getTimestamp() != null : "timestamp should not be null";
                assert response.getToken() != null : "token should not be null";
                assert response.getAppId().equals(appId) : "returned appId should match input";

                log.info("UDesk鉴权测试成功");
            } else {
                log.error("UDesk鉴权返回null");
            }
        } catch (Exception e) {
            log.error("UDesk鉴权测试失败", e);
            throw e;
        }
    }
}
