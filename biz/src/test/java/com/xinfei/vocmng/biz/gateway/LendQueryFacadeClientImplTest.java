package com.xinfei.vocmng.biz.gateway;

import com.xinfei.cashiercore.common.service.facade.request.management.ShortLinkCreateRequest;
import com.xinfei.cashiercore.common.service.facade.vo.management.ShortLinkVO;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentsByLoanNoResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.rr.dto.HuttaPlanDetailDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentPlanDto;
import com.xinfei.vocmng.biz.rr.request.HuttaDetailRequest;
import com.xinfei.vocmng.biz.rr.request.RepayPlanListRequest;
import com.xinfei.vocmng.biz.service.impl.RepayServiceImp;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.AppConfigService;
import com.xinfei.vocmng.itl.client.feign.impl.FeaturePlatformClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.ManagementFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.client.feign.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.RepayFacadeService;
import com.xinfei.vocmng.itl.rr.AgentsResponse;
import com.xinfei.vocmng.itl.rr.AppConfigRequest;
import com.xinfei.vocmng.itl.rr.GetTaskByMobileResp;
import com.xinfei.vocmng.itl.rr.GetTaskByUserIdResp;
import com.xinfei.vocmng.itl.rr.RealCollectReq;
import com.xinfei.vocmng.itl.rr.dto.AppConfigDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LendQueryFacadeClientImplTest {

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Resource
    private FeaturePlatformClientImpl featurePlatformClient;

    @Resource
    private AppConfigService appConfigService;

    @Resource
    private UdeskClientService udeskClientService;

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClientImpl;

    @Resource
    private RepayFacadeService repayFacadeService;

    @Resource
    private ManagementFacadeClient managementFacadeClient;

    @Resource
    private RepayServiceImp repayServiceImp;

    @Test
    public void getOrderList() {
        ManageOrderListRequest request = new ManageOrderListRequest();
        request.setUserNos(Collections.singletonList(111111111151076L));
        Page<ManageOrderDetailDTO> respDTO = lendQueryFacadeClient.getOrderList(request);
        System.out.println(respDTO);
    }

    @Test
    public void getRepayments() {
        QueryRepaymentsByLoanNoResponse respDTO = repayFacadeClient.getRepayments(Collections.singletonList("20230919153006021489302"), null, null,1, 10);
        System.out.println(respDTO);
    }

    @Test
    public void repayCalculate() {
        //是否支持提前还当期
        RepayLoanCalcResponse respDTO = repayFacadeService.repayCalculate(1, Collections.singletonList("1"), "2024031200002600000010005285", null, null);
        System.out.println(respDTO);

        //挡板 + 提前结清费
        RepayLoanCalcResponse respDTO2 = repayFacadeService.repayCalculate(2, null, "2024031200002600000010005285", null, null);
        System.out.println(respDTO2);
    }

    @Test
    public void accountInfo() {
        RealCollectReq req = new RealCollectReq();
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("mobile", "FKZK3Cc2tJaThVNm4Jifgw==");
        req.setInputParams(inputParams);
        req.setRequestId("123");
        req.setVarCodes(Arrays.asList("phone_black_industry_score", "phone_is_black_industry_hlevel_type"));
        String respDTO = featurePlatformClient.accountInfo(req);
        System.out.println(respDTO);
    }

    @Test
    public void getAllApp() {
        AppConfigRequest request = new AppConfigRequest();
        request.setAppId("vocmng");
        request.setTs(1705319027L);
        request.setParans1("app");
        request.setSign("77CDF9C9E1A2304F08EDE4F0ABB8E4F2");
        List<AppConfigDto> respDTO = appConfigService.getAllApp(request);
        System.out.println(respDTO);
    }

    @Test
    public void calllogs() {
        Integer respDTO = udeskClientService.customerCallLogs("***********");
        System.out.println(respDTO);
    }

    @Test
    public void customerImLogs() {
        Integer respDTO = udeskClientService.customerImLogs("***********");
        System.out.println(respDTO);
    }

    @Test
    public void getAgent() {
        AgentsResponse respDTO = udeskClientService.getAgent("1601103");
        System.out.println(respDTO);
    }

    @Test
    public void getTaskByUserNo() {
        GetTaskByUserIdResp respDTO = workOrderFeignClientImpl.getTaskByUserNo(111111115822020L);
        System.out.println(respDTO);
    }

    @Test
    public void getTaskByMobile() {
        List<GetTaskByMobileResp> respDTO = workOrderFeignClientImpl.getTaskByMobile("15021007562");
        System.out.println(respDTO);
    }

    @Test
    public void linkCreate() {
        ShortLinkCreateRequest request = new ShortLinkCreateRequest();
        request.setUserNo("111111111181170");
        request.setCustNo("CTL0da147a7466f62fbd6116d4484efcb3bd");
        request.setRequestId("VOCSLC440");
        request.setBizMetadata("{\"loanNo\":\"20230914152328021489011\",\"calcSettleType\":\"NO_SETTLE\",\"terms\":[\"1\"],\"app\":\"xyf01\"}");
        request.setCreatedBy("贾统帅");

        Date date = new Date(2024,6,23,23,00,00);
        request.setExpiredTime(date);

        ShortLinkVO respDTO = managementFacadeClient.linkCreate(request);
        System.out.println(respDTO);
    }

    @Test
    public void queryRepaymentPlan() {
        QueryRepaymentPlanResponse respDTO = repayFacadeClient.queryRepaymentPlan("20240109141719021504209", null, null,null);
        System.out.println(respDTO);
    }

    @Test
    public void queryPlanList() {
        RepayPlanListRequest request = new RepayPlanListRequest();
        request.setOrderNo("20230808194442021486306");
        PageResultResponse<RepaymentPlanDto> respDTO = repayServiceImp.queryPlanList(request);
        System.out.println(respDTO);
    }

    @Test
    public void queryHuttaPlanDetail() {
        HuttaDetailRequest request = new HuttaDetailRequest();
        request.setPlanDetailId("HUTTA-91");
        request.setLoanNo("20240109141719021504209");
        HuttaPlanDetailDto respDTO = repayServiceImp.queryHuttaPlanDetail(request);
        System.out.println(respDTO);
    }
}