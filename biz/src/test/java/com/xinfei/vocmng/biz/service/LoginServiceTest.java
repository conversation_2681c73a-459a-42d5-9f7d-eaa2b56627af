/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.req.LoginSmsReq;
import com.xinfei.vocmng.biz.model.req.SendSmsReq;
import com.xinfei.vocmng.biz.model.resp.LoginResp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @version $ LoginServiceTest, v 0.1 2023/12/22 10:59 wancheng.qu Exp $
 */


@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LoginServiceTest {

    @Resource
    private LoginService loginService;

    @Test
    public void sendTest(){
        SendSmsReq sms= new SendSmsReq();
        sms.setMobile("18397963123");
        loginService.send(sms);
    }

    @Test
    public void loginBySmsTest(){
        LoginSmsReq l = new LoginSmsReq();
        l.setCode("4605");
        l.setPassport("18397963123");
        LoginResp loginResp = loginService.loginBySms(l);
        System.out.println("res======"+loginResp.getToken());

    }

    @Test
    public void authorizeUrlTest(){
        System.out.println("url==="+loginService.authorizeUrl());
    }










}