package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.req.EagleEyeDataReq;
import com.xinfei.vocmng.biz.model.resp.EagleEyeDataResp;
import com.xinfei.vocmng.biz.service.EagleEyeCommunicateSummary;
import com.xinfei.vocmng.biz.service.impl.CommunicateSummaryServiceImpl;
import com.xinfei.vocmng.dal.mapper.CommunicateSummaryMapper;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class CommunicateSummaryApiTest {

    @Mock
    private CommunicateSummaryMapper communicateSummaryMapper;

    @Resource
    private CommunicateSummaryServiceImpl communicateSummaryService;

    @Resource
    private EagleEyeCommunicateSummary eagleEyeCommunicateSummary;

    @BeforeEach
    public void setUp() {
        // 预设mock数据
        List<CommunicateSummary> mockNowData = createMockCommunicateSummaries();
        List<CommunicateSummary> mockPeriodData = createMockCommunicateSummaries();

        // mock数据库查询结果
        when(communicateSummaryMapper.getEagleEyeData(anyList(), anyList(), anyList(), any(), any()))
                .thenReturn(mockNowData, mockPeriodData); // 第一次返回now数据，第二次返回period数据
    }

    @Test
    public void rateCalculation() {
        EagleEyeDataReq request = new EagleEyeDataReq();
        // 设置 sourceList（假设字段名为 sourceList，类型为 List<Integer>）
        request.setSourceList(Arrays.asList(2, 1, 4, 6, 3));

        // 设置 createUserIdentifyList（空数组）
        request.setCreateUserIdentifyList(new ArrayList<>()); // 或直接设为 null，根据接口要求

        // 设置 statusList（假设字段名为 statusList，类型为 List<Integer>）
        request.setStatusList(Arrays.asList(1, 2, 3));

        // 设置日期（注意日期格式是否需要转换）
        request.setPeriodDate(LocalDate.parse("2024-02-27"));
        request.setNowDate(LocalDate.parse("2025-04-11"));
        EagleEyeDataResp response = communicateSummaryService.getEagleEyeData(request);
        log.info(JsonUtil.toJson(response));
    }

    @Test
    public void count() {
        assertNotNull(eagleEyeCommunicateSummary.countHourlyData(createMockCommunicateSummaries(), createMockCommunicateSummaries()));
        assertNotNull(eagleEyeCommunicateSummary.countSourceAndStatus(createMockCommunicateSummaries()));
        assertNotNull(eagleEyeCommunicateSummary.countUserPerHour(createMockCommunicateSummaries()));
        assertNotNull(eagleEyeCommunicateSummary.hierarchicalCategoryStats(createMockCommunicateSummaries(), createMockCommunicateSummaries()));
    }

    private List<CommunicateSummary> createMockCommunicateSummaries() {
        CommunicateSummary summary = new CommunicateSummary();
        summary.setCreatedTime(LocalDateTime.now());
        summary.setSource(1);
        summary.setStatus(2);
        summary.setIssueCategoryLv1(100L);
        summary.setIssueCategoryLv2(200L);
        summary.setIssueCategoryLv3(300L);
        return Arrays.asList(summary, summary); // 可根据需要增加数据量
    }
}