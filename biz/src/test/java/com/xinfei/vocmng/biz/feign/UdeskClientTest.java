package com.xinfei.vocmng.biz.feign;

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import com.xinfei.vocmng.itl.rr.AgentsResponse;
import com.xinfei.vocmng.itl.rr.GetCustomerListResponse;
import com.xinfei.vocmng.itl.rr.ImCustomerDetailsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsQueryResponse;
import com.xinfei.vocmng.itl.rr.ImUserGroupsResponse;
import com.xinfei.xfframework.common.JsonUtil;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/10
 */
@Slf4j
public class UdeskClientTest extends TechplayDevTestBase {

    @Autowired
    private UdeskClientService udeskClientService;

    @Test
    public void getCustomerDetails() {
        /**
         id	客户id
        email	客户邮箱
        cellphone	客户电话
        token	客户外部唯一标识
        weixin_open_id	客户微信openid
        weixin_mini_openid	客户微信小程序openid
        weixin_work_identifier	客户企业微信的唯一标识，例：cropid:wxc727955fe6025ed4,agentid:1009117,userid:LS004308
        weibo_id	客户微博openid
        sdk_token	客户sdk标识
        web_token	客户web标识
         **/
        String type = "web_token";
        String content = "1939303106126958606";
        ImCustomerDetailsQueryResponse response = udeskClientService.getCustomerDetailsNew(type, content);
        log.info("query getCustomerDetails, response={}", response);
    }

    @Test
    public void userGroups() {
        Integer pageSize = 100;
        Integer page = 1;
        ImUserGroupsQueryResponse response = udeskClientService.userGroups(pageSize, page);
        log.info("query userGroups, response={}", JSON.toJSON(response));
    }

    @Test
    public void getAllUserGroups() {
        List<ImUserGroupsResponse> allUserGroups = udeskClientService.getAllUserGroups();
        log.info("query allUserGroups, response={}", allUserGroups);
        List<ImUserGroupsResponse> allUserGroups1 = udeskClientService.getAllUserGroups();
        log.info("query allUserGroups, response={}", allUserGroups1);
        List<ImUserGroupsResponse> allUserGroups2 = udeskClientService.getAllUserGroups();
        log.info("query allUserGroups, response={}", allUserGroups2);
    }

    @Test
    public void getAllAgents() {
        List<AgentsResponse> allAgents = udeskClientService.getAllAgents();
        log.info("query allAgents, response={}", JsonUtil.toJson(allAgents));
    }

    @Test
    public void getAllCustomers() {
        List<GetCustomerListResponse.Customer> allCustomers = udeskClientService.getAllCustomers();
        log.info("getAllCustomers, response={}", JsonUtil.toJson(allCustomers));
    }

}
