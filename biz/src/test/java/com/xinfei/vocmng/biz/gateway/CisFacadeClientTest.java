package com.xinfei.vocmng.biz.gateway;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xyf.cis.dto.SecureEncryptDTO;
import com.xyf.cis.query.facade.dto.standard.response.CustNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.IdNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
class CisFacadeClientTest {

//    @MockBean
//    private CustomerQueryFacade customerQueryFacade;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;


    @Test
    void queryUserList() {
        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, "1268894871", 1, 10);
        if (Objects.nonNull(pageResult) && CollectionUtils.isNotEmpty(pageResult.getList())) {
            String mobile = pageResult.getList().get(0).getMobile();
            if (StringUtils.isBlank(mobile)) {
                return;
            }
            PageResult<UserSearchDTO> pageResult1 = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
            if (Objects.isNull(pageResult1) || CollectionUtils.isEmpty(pageResult1.getList())) {
                return;
            }
            // 将pageResult1的list转换为Map，app作为key，userNo作为value
            Map<String, Long> appUserNoMap = pageResult1.getList().stream()
                    .filter(user -> user.getApp() != null && user.getUserNo() != null)
                    .collect(Collectors.toMap(
                            UserSearchDTO::getApp,  // key: app
                            UserSearchDTO::getUserNo,  // value: userNo
                            (existing, replacement) -> existing  // 如果有重复的key，保留第一个值
                    ));
            // 打印结果
            System.out.println("App to UserNo Map: " + appUserNoMap);
        }
    }

    @Test
    void queryCustNoByIdNo() {
//        Mockito.when(customerQueryFacade.queryCustNoByIdNo("3242323231")).thenReturn(null);
        CustNoDTO custNoDTO = cisFacadeClient.queryCustNoByIdNo("3242323232");
        System.out.println(custNoDTO);
    }

    @Test
    void batchEncrypt() {
        List<SecureEncryptDTO> secureBatchEncryptLocal = cisFacadeClient.batchEncryptLocal(Collections.singletonList("13262633594"));
        System.out.println(secureBatchEncryptLocal);
        System.out.println(cisFacadeClientService.getEncodeMobileLocal("13262633594"));
    }

    @Test
    void batchDecrypt() {

        List<SecureEncryptDTO> secureBatchEncryptLocal = cisFacadeClient.batchEncryptLocal(Collections.singletonList("13262633594"));
        System.out.println(secureBatchEncryptLocal);

//        List<SecureEncryptDTO> secureBatchEncryptDTO = cisFacadeClient.batchDecrypt(secureBatchEncryptLocal.stream().map(SecureEncryptDTO::getCipherText).collect(Collectors.toList()), "mobile");
        List<SecureEncryptDTO> secureBatchEncryptDTO = cisFacadeClient.batchDecrypt(Collections.singletonList("oWkf9JFVeiIOCOr30GJynQ=="), "mobile");

        System.out.println(secureBatchEncryptDTO);
    }

    @Test
    void queryIdNoByCustNo() {
//        IdNoDTO idNoDTO = cisFacadeClient.queryIdNoByCustNo("CTL05bc1597cec072731e03b73ba13a1515f");
//        System.out.println(idNoDTO);
//        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptByField("idCard", Collections.singletonList(idNoDTO.getIdNo()));
//        System.out.println(secureEncryptDTOS);


        //库奇
        List<SecureEncryptDTO> secureEncryptDTOS = cisFacadeClient.batchEncryptByField("idCard", Collections.singletonList("库奇"));
        System.out.println(secureEncryptDTOS);
    }
}