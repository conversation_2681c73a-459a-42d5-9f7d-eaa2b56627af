package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.ProductFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.itl.client.http.BankFeignClient;
import com.xinfei.vocmng.itl.rr.BankCardRequest;
import com.xinfei.vocmng.itl.rr.BaseResponse;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import com.xinfei.vocmng.itl.rr.ProfitProductResponse;
import com.xinfei.vocmng.itl.rr.dto.BankCardDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @since 2023/12/25
 */
@Slf4j
public class BankFeignTest extends TechplayDevTestBase {

    @Autowired
    private BankFeignClient bankFeignClient;

    @Autowired
    private ProductFeignService productFeignService;

    @Resource
    private VipFacadeClientImpl vipFacadeClient;


    @Test
    public void batchLoanQuery() {
        BankCardRequest request = new BankCardRequest();

        request.setCustNo("CTL05bf06c8de307044f7596f33cf4c12ba5");
        request.setApp("xyf");

        BaseResponse<BankCardDto> response = bankFeignClient.queryBankCardList("CTL05bf06c8de307044f7596f33cf4c12ba5", "xyf");
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void baseInfo() {
        FinServiceType response = productFeignService.baseInfo();
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void loadProfitProductByCondition() {
        List<ProfitProductResponse> response = productFeignService.loadProfitProductByCondition(Collections.singletonList("003"));
        log.info("query contract download info, response={}", response);
    }
}
