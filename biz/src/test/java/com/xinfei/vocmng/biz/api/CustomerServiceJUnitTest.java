package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.config.SmsTemplate;
import com.xinfei.vocmng.biz.rr.dto.BankDto;
import com.xinfei.vocmng.biz.rr.dto.CustomerDetailDto;
import com.xinfei.vocmng.biz.rr.dto.CustomerDto;
import com.xinfei.vocmng.biz.rr.dto.ImagesDto;
import com.xinfei.vocmng.biz.rr.dto.UserNoAppDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerListRequest;
import com.xinfei.vocmng.biz.rr.request.GetCustomerRequest;
import com.xinfei.vocmng.biz.rr.request.GetUserNoListRequest;
import com.xinfei.vocmng.biz.rr.request.LogOffReq;
import com.xinfei.vocmng.biz.rr.request.RepayListCreateReq;
import com.xinfei.vocmng.biz.rr.request.SendMessageRequest;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.service.CustomerService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * CustomerService服务的单元测试类
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CustomerServiceJUnitTest {

    @Resource
    private CustomerService customerService;

    @Test
    @DisplayName("测试获取用户列表")
    public void testGetCustomerList() {
        // 准备测试数据
        GetCustomerListRequest request = new GetCustomerListRequest();
        request.setPageSize(10);
        request.setCurrentPage(1);
        // 至少需要设置一个查询条件
        request.setMobile("***********"); // 使用测试手机号

        // 执行测试
        Paging<CustomerDto> response = customerService.getCustomerList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试获取UserNo列表")
    public void testGetUserNoList() {
        // 准备测试数据
        GetUserNoListRequest request = new GetUserNoListRequest();
        request.setMobile("18054083958");

        // 执行测试
        List<UserNoAppDto> response = customerService.getUserNoList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试获取用户详情")
    public void testGetCustomerDetail() {
        // 准备测试数据
        GetCustomerRequest request = new GetCustomerRequest();
        request.setUserNo("1939303089098213330"); // 使用测试用户编号

        // 执行测试
        CustomerDetailDto response = customerService.getCustomerDetail(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试获取银行卡列表")
    public void testGetAllBankCardList() {
        // 准备测试数据
        String userNo = "***************"; // 使用测试用户编号

        // 执行测试
        List<BankDto> response = customerService.getAllBankCardList(userNo);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试获取用户照片信息")
    public void testGetImagesDetail() {
        // 准备测试数据
        String userNo = "1939303089098213330"; // 使用测试用户编号
        String custNo = "CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f"; // 使用测试客户编号

        // 执行测试
        ImagesDto response = customerService.getImagesDetail(userNo, custNo);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试发送短信")
    public void testSendMessage() {
        // 准备测试数据
        SendMessageRequest request = new SendMessageRequest();
        request.setMobile("***********"); // 使用测试手机号
        request.setTemplateId("smstpl711908");
        request.setApp("xyf01");

        // 执行测试
        Boolean response = customerService.sendMessage(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试获取短信模板")
    public void testGetSmsTemplates() {
        // 执行测试
        List<SmsTemplate> response = customerService.getSmsTemplates();

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试客户注销")
    public void testUserLogOff() {
        // 准备测试数据
        LogOffReq request = new LogOffReq();
        request.setUserNo("***************"); // 使用测试用户编号
        request.setApp("xyf01"); // 必填字段
        request.setOperatorName("testOperator"); // 必填字段
        request.setMobile("***********"); // 必填字段
        request.setImmediate(false); // 是否立即注销

        // 执行测试
        Boolean response = customerService.userLogOff(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试客户撤销注销")
    public void testUserLogOffCancel() {
        // 准备测试数据
        LogOffReq request = new LogOffReq();
        request.setUserNo("***************"); // 使用测试用户编号
        request.setApp("xyf01"); // 必填字段
        request.setOperatorName("testOperator"); // 必填字段
        request.setMobile("***********"); // 必填字段

        // 执行测试
        Boolean response = customerService.userLogOffCancel(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试多卡轮扣查询")
    public void testRepayListQuery() {
        // 准备测试数据
        String custNo = "testCustNo"; // 使用测试客户编号

        // 执行测试
        Boolean response = customerService.repayListQuery(custNo);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试多卡轮扣开启或关闭")
    public void testRepayListUpdate() {
        // 准备测试数据
        RepayListCreateReq request = new RepayListCreateReq();
        request.setCustNo("CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f"); // 使用测试客户编号
        request.setStatus("ENABLE"); // 设置状态，字符串类型：ENABLE或DISABLE
        request.setOperatedBy("testOperator"); // 必填字段

        // 执行测试
        Boolean response = customerService.repayListUpdate(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("测试根据手机号或身份证获取UserNo")
    public void testGetUserNoByMobileId() {
        // 准备测试数据
        String mobile = "***********"; // 使用测试手机号
        String idCard = "110101199001011234"; // 使用测试身份证号
        String userNo = null; // 不指定userNo

        // 执行测试
        List<UserNoAppDto> response = customerService.getUserNoByMobileId(mobile, idCard, userNo);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }
}
