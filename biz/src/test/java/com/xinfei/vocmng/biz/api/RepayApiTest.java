package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.FeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.request.FeeRatioProcessResp;
import com.xinfei.vocmng.biz.rr.request.RefundFeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.response.RefundFeeRatioProcessResp;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class RepayApiTest {

    @Resource
    private RepayApi repayApi;

    @Resource
    private RefundApi refundApi;

    @Test
    public void rateCalculation() {
        FeeRatioProcessReq request = new FeeRatioProcessReq();
        request.setLoanNo("2024012800002600000004461856");
        request.setRepayType(2);
        request.setTargetFeeRatio(new BigDecimal("24"));
        ApiResponse<List<FeeRatioProcessResp>> response = repayApi.rateCalculation(Collections.singletonList(request));
        log.info(JsonUtil.toJson(response));
    }

    @Test
    public void refundRateCalculation() {
        RefundFeeRatioProcessReq request = new RefundFeeRatioProcessReq();
        request.setLoanNo("2024012800002600000004461856");
        request.setTargetFeeRatio(new BigDecimal("24"));
        ApiResponse<List<RefundFeeRatioProcessResp>> response = refundApi.refundRateCalculation(Collections.singletonList(request));
        log.info(JsonUtil.toJson(response));
    }
}