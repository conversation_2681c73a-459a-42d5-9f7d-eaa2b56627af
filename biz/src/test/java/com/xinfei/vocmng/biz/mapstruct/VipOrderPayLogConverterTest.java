/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mapstruct;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderPayLogAdminDTO;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VipOrderPayLogConverter 测试类
 *
 * <AUTHOR>
 * @version $ VipOrderPayLogConverterTest, v 0.1 2025/5/15 16:30 shaohui.chen Exp $
 */
@Slf4j
public class VipOrderPayLogConverterTest extends TechplayDevTestBase {


    /**
     * 测试将内部系统DTO列表转换为内部系统详情DTO列表
     */
    @Test
    public void testConvertToDetailDTOList() {
        // 创建内部系统DTO列表
        List<VipOrderPayLogAdminDTO> internalDTOList = new ArrayList<>();
        internalDTOList.add(createExternalDTO());

        // 转换为内部系统详情DTO列表
        List<VipOrderPayLogDetailDTO> detailDTOList = VipOrderPayLogConverter.INSTANCE.convertToDetailDTOList(internalDTOList);

        log.info("======,{}", detailDTOList);

        // 验证转换结果
        assertNotNull(detailDTOList);
        assertEquals(internalDTOList.size(), detailDTOList.size());

        log.info("内部系统DTO列表转换为内部系统详情DTO列表测试通过");
    }

    /**
     * 测试支付类型转换为支付类型描述
     */
    @Test
    public void testPayTypeToDesc() {
        // 测试微信支付
        String wechatDesc = VipOrderPayLogConverter.INSTANCE.payTypeToDesc("wechat");
        assertEquals("微信", wechatDesc);

        // 测试支付宝支付
        String alipayDesc = VipOrderPayLogConverter.INSTANCE.payTypeToDesc("alipay");
        assertEquals("支付宝", alipayDesc);

        // 测试储蓄卡支付
        String debitCardDesc = VipOrderPayLogConverter.INSTANCE.payTypeToDesc("debit_card");
        assertEquals("储蓄卡", debitCardDesc);

        // 测试空值
        String nullDesc = VipOrderPayLogConverter.INSTANCE.payTypeToDesc(null);
        assertNull(nullDesc);

        // 测试不存在的支付类型
        String unknownDesc = VipOrderPayLogConverter.INSTANCE.payTypeToDesc("unknown");
        assertNull(unknownDesc);

        log.info("支付类型转换为支付类型描述测试通过");
    }

    /**
     * 测试支付状态转换为支付状态描述
     */
    @Test
    public void testPayStatusToDesc() {
        // 测试各种支付状态
        String payStartDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("pay_start");
        assertEquals("待支付", payStartDesc);

        String payingDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("paying");
        assertEquals("支付中", payingDesc);

        String payFailDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("pay_fail");
        assertEquals("支付失败", payFailDesc);

        String paySuccessDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("pay_success");
        assertEquals("支付成功", paySuccessDesc);

        String payCancelDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("pay_cancel");
        assertEquals("支付取消", payCancelDesc);

        String payCloseDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("pay_close");
        assertEquals("支付关闭", payCloseDesc);

        // 测试空值
        String nullDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc(null);
        assertNull(nullDesc);

        // 测试不存在的支付状态
        String unknownDesc = VipOrderPayLogConverter.INSTANCE.payStatusToDesc("unknown");
        assertNull(unknownDesc);

        log.info("支付状态转换为支付状态描述测试通过");
    }

    /**
     * 创建外部系统DTO
     */
    private VipOrderPayLogAdminDTO createExternalDTO() {
        VipOrderPayLogAdminDTO dto = new VipOrderPayLogAdminDTO();
        dto.setPayTime(LocalDateTime.now());
        dto.setPayAmount(0);
        dto.setPayStatus("pay_start");
        dto.setPayUserNo("11");
        dto.setPayType("wechat");
        dto.setPayAccount("22");
        return dto;
    }
}
