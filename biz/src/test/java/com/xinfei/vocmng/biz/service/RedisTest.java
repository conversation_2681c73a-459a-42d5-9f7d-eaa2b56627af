/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.util.JwtUtil;
import com.xinfei.vocmng.biz.util.RedisUtils;
import org.junit.Test;
import org.junit.jupiter.params.shadow.com.univocity.parsers.annotations.Validate;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RedisTest, v 0.1 2023/12/21 15:31 wancheng.qu Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class RedisTest {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private LoginService loginService;

    @Test
    public void login(){
        String jwt = JwtUtil.generateToken("5d314d44-2f1b-4301-8ef5-f6036665f4d8");
        System.out.println("jwt==="+jwt);
        String identify = JwtUtil.getUserIdentifyFromToken(jwt, loginService);
        System.out.println("iden===="+identify);
        String key = LoginUserConstants.LOGIN__KEY + jwt;
        System.out.println("key====="+key);
        redisUtils.set(key, identify, 32400);
        String s = redisUtils.get(key);
        System.out.println("value===="+ s);


    }

    @Test
    public void list(){
        List<String> departmentList = Arrays.asList("你会", "DepartmentB", "他是");
        System.out.println("res===="+departmentList.toString());
    }


    @Test
    public void get() {
        //redisTemplate.opsForValue().set("name", "jack");
        String name = redisUtils.get("name");
        System.out.println("value====" + name);
        redisUtils.set("name", "rose", 900L);
        System.out.println("value===="+redisUtils.get("name"));
    }

    @Test
    public void del(){
        redisUtils.del(LoginUserConstants.USER__KEY + "root");
    }

}