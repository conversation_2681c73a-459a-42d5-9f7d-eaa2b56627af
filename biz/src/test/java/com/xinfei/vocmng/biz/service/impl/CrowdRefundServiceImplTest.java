package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * CrowdRefundServiceImpl 幂等性测试
 *
 * <AUTHOR>
 * @version $ CrowdRefundServiceImplTest, v 0.1 2025/7/17 10:00 shaohui.chen Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CrowdRefundServiceImplTest {

    @Autowired
    private CrowdRefundServiceImpl crowdRefundService;

    @Test
    public void testCrowUserData() {
        CrowdUserDataDto crowdUserDataDto = new CrowdUserDataDto();
        crowdUserDataDto.setUserNo("122");
        crowdUserDataDto.setVipOrderNo(1270L);
        crowdUserDataDto.setRefundType(1);
        crowdRefundService.processUserRefund(crowdUserDataDto);

    }


}
