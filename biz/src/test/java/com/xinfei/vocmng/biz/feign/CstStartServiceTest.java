/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.CstStartService;
import com.xinfei.vocmng.itl.rr.RefundOrderRecordRes;
import com.xinfei.vocmng.itl.rr.RefundRecordQueryReq;
import com.xinfei.vocmng.itl.rr.RefundRecordResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CstStartServiceTest, v 0.1 2024-05-21 19:18 junjie.yan Exp $
 */
@Slf4j
public class CstStartServiceTest extends TechplayDevTestBase {

    @Resource
    private CstStartService cstStartService;

    @Test
    public void batchLoanQuery() {
        RefundRecordQueryReq req = new RefundRecordQueryReq();

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now();
        String localDateTime = df.format(time);


        LocalDateTime time2 = LocalDateTime.now().plusDays(-5);
        String localDateTime2 = df.format(time2);

        req.setStartDate(localDateTime2);
        req.setEndDate(localDateTime);


        RefundRecordResult response = cstStartService.getBankFlow(req);
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void queryRefundOrderRecordListByTransNo() {
        List<RefundOrderRecordRes> response = cstStartService.queryRefundOrderRecordListByTransNo(null, null);
        log.info("query contract download info, response={}", response);
    }

}