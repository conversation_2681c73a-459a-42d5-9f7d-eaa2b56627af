package com.xinfei.vocmng.biz.mapper;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.dal.mapper.*;
import com.xinfei.vocmng.dal.po.CommunicateSummary;
import com.xinfei.vocmng.dal.po.CommunicateSummaryCopy;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.dal.po.SummaryEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class EmployeeMapperTest {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    private CommunicateSummaryMapper communicateSummaryMapper;

    @Autowired
    private CommunicateSummaryCopyMapper communicateSummaryCopyMapper;

    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;

    @Resource
    private UnionCustomerQuestionMapper unionCustomerQuestionMapper;

    @Test
    public void test() {
        List<RepaymentPlanDetail> rest = repaymentPlanDetailMapper.getRepaymentPlanDetailList(100l, 3l);
        System.out.println(rest);
    }

    @Test
    public void queryQuestionByRange() {
        List<SummaryEntity> rest = unionCustomerQuestionMapper.queryQuestionByRange(0L, 100L);
        System.out.println(rest);
        Long id = unionCustomerQuestionMapper.queryTopId();
        System.out.println(id);
    }

    @Test
    public void querySummaryByRange() {
        List<CommunicateSummaryCopy> rest = communicateSummaryCopyMapper.querySummaryByRange(0L, 100L);
        ;
        System.out.println(rest);
    }


    @Test
    public void getUserIdentifyByUserName() {
        List<String> name = new ArrayList<>();
        name.add("test1");
        name.add("test2");
        List<String> list = employeeMapper.getUserIdentifyByUserName(name);
        System.out.println(list);
    }

    @Test
    public void getFiveSummaries() {
        List<CommunicateSummary> list = communicateSummaryMapper.getFiveSummaries("13888888888", 16L);
        System.out.println(list);
    }

    @Test
    public void getSevenSummaries() {
        LocalDate endTime = LocalDate.now();
        LocalDate startTime = endTime.plusDays(-7);
        LocalDate time = LocalDate.of(2024, 02, 23);
        Integer result = communicateSummaryMapper.getSummariesSevenDay(1639203089095981042L, time);
        System.out.println(result);
    }
}