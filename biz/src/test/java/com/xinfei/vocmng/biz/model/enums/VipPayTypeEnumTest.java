/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * VipPayTypeEnum 测试类
 *
 * <AUTHOR>
 * @version $ VipPayTypeEnumTest, v 0.1 2025/5/15 16:30 shaohui.chen Exp $
 */
@Slf4j
public class VipPayTypeEnumTest extends TechplayDevTestBase {

    /**
     * 测试枚举值
     */
    @Test
    public void testEnumValues() {
        // 测试微信支付
        assertEquals("wechat", VipPayTypeEnum.WECHAT.getCode());
        assertEquals("微信", VipPayTypeEnum.WECHAT.getDesc());
        
        // 测试支付宝支付
        assertEquals("alipay", VipPayTypeEnum.ALIPAY.getCode());
        assertEquals("支付宝", VipPayTypeEnum.ALIPAY.getDesc());
        
        // 测试储蓄卡支付
        assertEquals("debit_card", VipPayTypeEnum.DEBIT_CARD.getCode());
        assertEquals("储蓄卡", VipPayTypeEnum.DEBIT_CARD.getDesc());
        
        log.info("枚举值测试通过");
    }
    
    /**
     * 测试根据支付类型编码获取支付类型描述
     */
    @Test
    public void testGetDescByCode() {
        // 测试微信支付
        assertEquals("微信", VipPayTypeEnum.getDescByCode("wechat"));
        
        // 测试支付宝支付
        assertEquals("支付宝", VipPayTypeEnum.getDescByCode("alipay"));
        
        // 测试储蓄卡支付
        assertEquals("储蓄卡", VipPayTypeEnum.getDescByCode("debit_card"));
        
        // 测试空值
        assertNull(VipPayTypeEnum.getDescByCode(null));
        
        // 测试不存在的支付类型
        assertNull(VipPayTypeEnum.getDescByCode("unknown"));
        
        log.info("根据支付类型编码获取支付类型描述测试通过");
    }
    
    /**
     * 测试根据支付类型编码获取支付类型枚举
     */
    @Test
    public void testGetByCode() {
        // 测试微信支付
        assertEquals(VipPayTypeEnum.WECHAT, VipPayTypeEnum.getByCode("wechat"));
        
        // 测试支付宝支付
        assertEquals(VipPayTypeEnum.ALIPAY, VipPayTypeEnum.getByCode("alipay"));
        
        // 测试储蓄卡支付
        assertEquals(VipPayTypeEnum.DEBIT_CARD, VipPayTypeEnum.getByCode("debit_card"));
        
        // 测试空值
        assertNull(VipPayTypeEnum.getByCode(null));
        
        // 测试不存在的支付类型
        assertNull(VipPayTypeEnum.getByCode("unknown"));
        
        log.info("根据支付类型编码获取支付类型枚举测试通过");
    }
}
