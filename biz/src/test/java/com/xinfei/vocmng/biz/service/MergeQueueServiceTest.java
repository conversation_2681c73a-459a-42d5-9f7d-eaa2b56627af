package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 合并队列服务测试类
 *
 * <AUTHOR>
 * @version $ MergeQueueServiceTest, v 0.1 2025/5/1 $
 */
@SpringBootTest
@Slf4j
public class MergeQueueServiceTest {

    @Autowired
    private MergeQueueService mergeQueueService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 测试推送任务到队列
     */
    @Test
    public void testPushTask() {
        // 生成随机webToken用于测试
        String webToken = UUID.randomUUID().toString();
        
        // 推送任务到队列
        String result = mergeQueueService.pushTask(webToken);
        
        // 验证推送结果
        assertEquals("OK", result, "推送任务应该成功");
        
        // 验证任务是否在队列中
        Long size = redisTemplate.opsForList().size(RedisKeyConstants.MERGE_QUEUE);
        assertTrue(size > 0, "队列应该有任务");
        
        // 验证重复推送
        String duplicateResult = mergeQueueService.pushTask(webToken);
        assertEquals("DUPLICATE", duplicateResult, "重复推送应该返回DUPLICATE");
        
        // 清理测试数据
        redisTemplate.opsForList().remove(RedisKeyConstants.MERGE_QUEUE, 1, webToken);
    }

    /**
     * 测试从队列获取任务
     */
    @Test
    public void testPopTask() {
        // 生成随机webToken用于测试
        String webToken = UUID.randomUUID().toString();
        
        // 推送任务到队列
        mergeQueueService.pushTask(webToken);
        
        // 从队列获取任务
        String poppedWebToken = mergeQueueService.popTask();
        
        // 验证获取的任务是否正确
        assertEquals(webToken, poppedWebToken, "获取的任务应该与推送的任务一致");
        
        // 验证任务是否已标记为处理中
        Boolean isProcessing = redisTemplate.opsForSet().isMember(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
        assertTrue(isProcessing, "任务应该被标记为处理中");
        
        // 清理测试数据
        redisTemplate.opsForSet().remove(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
    }

    /**
     * 测试标记任务完成
     */
    @Test
    public void testMarkTaskComplete() {
        // 生成随机webToken用于测试
        String webToken = UUID.randomUUID().toString();
        
        // 将任务标记为处理中
        redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
        
        // 标记任务完成
        mergeQueueService.markTaskComplete(webToken);
        
        // 验证任务是否已从处理中集合移除
        Boolean isProcessing = redisTemplate.opsForSet().isMember(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
        assertFalse(isProcessing, "任务应该从处理中集合移除");
    }

    /**
     * 测试添加任务到重试队列
     */
    @Test
    public void testAddToRetryQueue() {
        // 生成随机webToken用于测试
        String webToken = UUID.randomUUID().toString();
        
        // 将任务标记为处理中
        redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
        
        // 添加任务到重试队列
        mergeQueueService.addToRetryQueue(webToken);
        
        // 验证任务是否已从处理中集合移除
        Boolean isProcessing = redisTemplate.opsForSet().isMember(RedisKeyConstants.MERGE_PROCESSING_SET, webToken);
        assertFalse(isProcessing, "任务应该从处理中集合移除");
        
        // 验证任务是否在重试队列中
        Long size = redisTemplate.opsForList().size(RedisKeyConstants.MERGE_RETRY_QUEUE);
        assertTrue(size > 0, "重试队列应该有任务");
        
        // 清理测试数据
        redisTemplate.opsForList().remove(RedisKeyConstants.MERGE_RETRY_QUEUE, 1, webToken);
        redisTemplate.delete("voc.merge.retry_count:" + webToken);
    }

    /**
     * 测试获取队列状态
     */
    @Test
    public void testGetQueueSizes() {
        // 获取队列状态
        Map<String, Long> queueSizes = mergeQueueService.getQueueSizes();
        
        // 验证返回结果包含所有队列
        assertTrue(queueSizes.containsKey("mainQueue"), "返回结果应该包含主队列");
        assertTrue(queueSizes.containsKey("retryQueue"), "返回结果应该包含重试队列");
        assertTrue(queueSizes.containsKey("processing"), "返回结果应该包含处理中集合");
        
        log.info("Queue sizes: {}", queueSizes);
    }
}
