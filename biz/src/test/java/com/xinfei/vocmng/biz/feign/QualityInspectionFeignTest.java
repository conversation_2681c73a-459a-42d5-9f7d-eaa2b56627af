package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.QualityInspectionClientImpl;
import com.xinfei.vocmng.itl.rr.BathResponseData;
import com.xinfei.vocmng.itl.rr.OrgData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2024/10/15
 */
@Slf4j
public class QualityInspectionFeignTest extends TechplayDevTestBase {

    @Autowired
    private QualityInspectionClientImpl qualityInspectionClient;

    @Test
    public void batchLoanQuery() {

        OrgData orgData = new OrgData();
        orgData.setId("2");
        orgData.setName("客服");
        orgData.setLeader("jj");
        orgData.setParent("1");
        List<OrgData> orgDatas = new ArrayList<>();
        orgDatas.add(orgData);

        List<BathResponseData> response = qualityInspectionClient.orgBath(orgDatas);
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void getToken() {
        String response = qualityInspectionClient.getToken("2");
        log.info("query contract download info, response={}", response);
    }
}
