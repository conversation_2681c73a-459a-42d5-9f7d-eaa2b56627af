package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.CallCenterFeignClient;
import com.xinfei.vocmng.itl.rr.CallCenterBaseResponse;
import com.xinfei.vocmng.itl.rr.CallListRequest;
import com.xinfei.vocmng.itl.rr.CallRecordDetail;
import com.xinfei.vocmng.itl.rr.PageDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CallCenterFeignClientTest, v 0.1 2023/12/27 22:13 qu.lu Exp $
 */
@Slf4j
public class CallCenterFeignClientTest extends TechplayDevTestBase {

    @Autowired
    private CallCenterFeignClient callCenterFeignClient;

    @Test
    public void queryCallRecordList(){
        CallListRequest request = new CallListRequest();
        request.setPageNumber(1);
        request.setPageSize(20);
        request.setStartTime("2023-12-01 00:00:00");
        request.setEndTime("2023-12-31 00:00:00");

        CallCenterBaseResponse<PageDataInfo<List<CallRecordDetail>>> response = callCenterFeignClient.queryCallRecordList(request);
        log.info("query call record list response={}",response);
    }
}
