package com.xinfei.vocmng.biz.feign;

import com.fasterxml.jackson.databind.JsonNode;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.CisAuthFacadeClientImpl;
import com.xinfei.vocmng.itl.model.header.CISHeader;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.UserQueryFacade;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.user.auth.dto.response.OcrFaceImagesResponse;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Slf4j
public class CISFeignTest extends TechplayDevTestBase {

    @Autowired
    private UserQueryFacade userQueryFacade;

    @Autowired
    private CisAuthFacadeClientImpl client;

    @Test
    public void doTest(){
        log.info("begin execute...");
        BaseResponse<List<UserNoDTO>> response = userQueryFacade.queryUserNoByMobile("18616781132");
        log.info("response={}",response);
    }

    @Test
    public void queryOcrFaceImages(){
        log.info("begin execute...");
//        OcrFaceImagesResponse response = client.queryOcrFaceImages(111111112477538L, "CTL095b72e57fe08ea8378102b151ec1d498", null);
        OcrFaceImagesResponse response = client.queryOcrFaceImages(111111111775469L, "CTL045b2aa74a7d561694d60461068e978dc", null);

        log.info("response={}",response);
    }

    public static void main(String[] args) {
        CISHeader header = new CISHeader();
        header.setRequestTime("ssss");
        header.setAppName("voc");
        try{
            JsonNode node = JsonUtil.getObjMapper().readTree(JsonUtil.toJsonNull(header));
            Iterator<Map.Entry<String, JsonNode>> iterator = node.fields();
            while (iterator.hasNext()){
                Map.Entry<String,JsonNode> curItem = iterator.next();
                System.out.println(curItem.getKey()+" = "+curItem.getValue());
            }
        }catch (Exception e){

        }

    }
}
