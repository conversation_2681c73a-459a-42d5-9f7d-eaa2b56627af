package com.xinfei.vocmng.biz.controller;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.dal.mapper.UserLabelMappingMapper;
import com.xinfei.vocmng.dal.po.LabelCategoryConfig;
import com.xinfei.vocmng.dal.mapper.LabelCategoryConfigMapper;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.dal.po.UserLabelMapping;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/11
 */
public class LabelCategoryConfigMapperTest extends TechplayDevTestBase {
    @Autowired
    private LabelCategoryConfigMapper mapper;

    @Autowired
    private UserLabelMappingMapper userLabelMappingMapper;

    @Test
    public void doPrint(){
        LabelCategoryConfig config = mapper.selectById(20);
        System.out.println("print...,config="+config.getName());
    }

    @Test
    public void getLabels(){
        List<LabelDto> config = userLabelMappingMapper.getLabels("13083666916");
        List<LabelDto> config2 = userLabelMappingMapper.getLabelsEncrypted("YqT42PZthzh5FCbJQSkOxA==");
        System.out.println(config);
        System.out.println(config2);
    }

    @Test
    public void getUserLabel(){
        List<UserLabelMapping> userLabel = userLabelMappingMapper.getUserLabel("13083666916");
        List<UserLabelMapping> userLabel2 = userLabelMappingMapper.getUserLabelEncrypted("YqT42PZthzh5FCbJQSkOxA==");
        System.out.println(userLabel);
        System.out.println(userLabel2);
    }
}
