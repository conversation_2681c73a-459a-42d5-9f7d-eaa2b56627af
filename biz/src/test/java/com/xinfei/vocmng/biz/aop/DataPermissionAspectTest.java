package com.xinfei.vocmng.biz.aop;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.component.DictDataCache;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import com.xinfei.vocmng.biz.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 数据权限切面测试类
 *
 * <AUTHOR>
 * @version $ DataPermissionAspectTest, v 0.1 2025/5/15 16:30 shaohui.chen Exp $
 */
@Slf4j
public class DataPermissionAspectTest extends TechplayDevTestBase {

    @Mock
    private LoginService loginService;

    @Mock
    private DictDataCache dictDataCache;

    @InjectMocks
    private DataPermissionAspect dataPermissionAspect;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试支付账号掩码处理 - 支付宝账号
     */
    @Test
    public void testProcessOrderPayLogResult_Alipay() {
        // 准备测试数据
        VipOrderPayLogDetailDTO dto = new VipOrderPayLogDetailDTO();
        dto.setPayType("alipay");
        dto.setPayAccount("<EMAIL>");
        
        List<VipOrderPayLogDetailDTO> resultList = new ArrayList<>();
        resultList.add(dto);
        
        // 模拟JoinPoint
        JoinPoint joinPoint = mock(JoinPoint.class);
        Signature signature = mock(Signature.class);
        when(signature.getName()).thenReturn("queryOrderPayLog");
        when(joinPoint.getSignature()).thenReturn(signature);
        
        // 模拟用户上下文
        UserInfo userInfo = new UserInfo();
        userInfo.setUserIdentify("testUser");
        userInfo.setDataAuth(new ArrayList<>());
        
        // 调用方法
        dataPermissionAspect.applyDataPermission(joinPoint, null, resultList);
        
        // 验证结果
        assertEquals("tes****om", dto.getPayAccount());
        log.info("支付宝账号掩码测试通过: {}", dto.getPayAccount());
    }
    
    /**
     * 测试支付账号掩码处理 - 储蓄卡
     */
    @Test
    public void testProcessOrderPayLogResult_DebitCard() {
        // 准备测试数据
        VipOrderPayLogDetailDTO dto = new VipOrderPayLogDetailDTO();
        dto.setPayType("debit_card");
        dto.setPayAccount("6216601234567890123");
        
        List<VipOrderPayLogDetailDTO> resultList = new ArrayList<>();
        resultList.add(dto);
        
        // 模拟JoinPoint
        JoinPoint joinPoint = mock(JoinPoint.class);
        Signature signature = mock(Signature.class);
        when(signature.getName()).thenReturn("queryOrderPayLog");
        when(joinPoint.getSignature()).thenReturn(signature);
        
        // 模拟用户上下文
        UserInfo userInfo = new UserInfo();
        userInfo.setUserIdentify("testUser");
        userInfo.setDataAuth(new ArrayList<>());
        
        // 调用方法
        dataPermissionAspect.applyDataPermission(joinPoint, null, resultList);
        
        // 验证结果
        assertEquals("621660******0123", dto.getPayAccount());
        log.info("储蓄卡账号掩码测试通过: {}", dto.getPayAccount());
    }
    
    /**
     * 测试支付账号掩码处理 - 有掩码权限
     */
    @Test
    public void testProcessOrderPayLogResult_WithMaskPermission() {
        // 准备测试数据
        VipOrderPayLogDetailDTO dto = new VipOrderPayLogDetailDTO();
        dto.setPayType("alipay");
        dto.setPayAccount("<EMAIL>");
        
        List<VipOrderPayLogDetailDTO> resultList = new ArrayList<>();
        resultList.add(dto);
        
        // 模拟JoinPoint
        JoinPoint joinPoint = mock(JoinPoint.class);
        Signature signature = mock(Signature.class);
        when(signature.getName()).thenReturn("queryOrderPayLog");
        when(joinPoint.getSignature()).thenReturn(signature);
        
        // 模拟用户上下文和权限
        UserInfo userInfo = new UserInfo();
        userInfo.setUserIdentify("testUser");
        userInfo.setDataAuth(Arrays.asList("mask_cardno", "mask_alipay"));
        
        when(loginService.getUserInfo("testUser")).thenReturn(userInfo);
        
        // 调用方法
        dataPermissionAspect.applyDataPermission(joinPoint, null, resultList);
        
        // 验证结果 - 有权限时不应该掩码
        assertEquals("<EMAIL>", dto.getPayAccount());
        log.info("有掩码权限测试通过: {}", dto.getPayAccount());
    }
}
