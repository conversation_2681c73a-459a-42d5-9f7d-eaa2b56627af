package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.UserCouponDetailReq;
import com.xinfei.vocmng.biz.rr.request.UserCouponReq;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDetailDto;
import com.xinfei.vocmng.itl.rr.dto.UserCouponDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2024/8/13 16:06
 */

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CouponServiceImplTest {

    @Resource
    private CouponServiceImpl couponService;

    /**
     * 查询用户优惠券列表
     */
    @Test
    public void getUserCoupon() {
        UserCouponReq request = new UserCouponReq();
        request.setUserNo(111483302518L);
        ApiResponse<Paging<UserCouponDto>> response = couponService.getUserCoupon(request);
        System.out.println(response);
    }

    /**
     * 查询用户优惠券详情
     */
    @Test
    public void getUserCouponDetail() {
        UserCouponDetailReq request = new UserCouponDetailReq();
        request.setUserNo(111483302518L);
        request.setCouponIdList(Arrays.asList(206));
        ApiResponse<List<UserCouponDetailDto>> response = couponService.getUserCouponDetail(request);
        System.out.println(response);
    }
}