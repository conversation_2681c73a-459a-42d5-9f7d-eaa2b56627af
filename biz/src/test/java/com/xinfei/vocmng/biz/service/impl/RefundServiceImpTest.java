/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.req.RefundApplyReq;
import com.xinfei.vocmng.biz.rr.response.RefundResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @version $ RefundServiceImpTest, v 0.1 2025-07-23 13:44 junjie.yan Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class RefundServiceImpTest {

    @Resource
    private RefundServiceImp refundServiceImp;

    @Test
    public void testRefundApply_EmptyList() {

        RefundApplyReq refundApplyReq = new RefundApplyReq();

        // 手动设置字段值
        refundApplyReq.setBillNo(null); // billNo 为 null
        refundApplyReq.setRefundAmount(BigDecimal.ONE); // refundAmount = 1
        refundApplyReq.setOrderNo("2025070800126900000079268424");
        refundApplyReq.setLoanNo("2025070800126900000079268424");
        refundApplyReq.setOfflineRefundMethod("ALIPAY");
        refundApplyReq.setOfflineRefundAccount("123456");
        refundApplyReq.setOfflineRefundBank(""); // 空字符串
        refundApplyReq.setOfflineRefundUserName("张莺*");
        refundApplyReq.setStatus(0);
//        refundApplyReq.setReview(false);
        refundApplyReq.setRealRatio(BigDecimal.ZERO); // realRatio = 0
        refundApplyReq.setRefundType("OFFLINE_REFUND");
        refundApplyReq.setExecuteType(1);
        refundApplyReq.setExecuteDay(null); // executeDay = null
        refundApplyReq.setRefundReason("优惠未使用");
        refundApplyReq.setRefundCount(2);
        refundApplyReq.setCustNo("CTL0c3f1a64ec7c1ed3edc84aed1f97ef74f");
        refundApplyReq.setUserNo("1939303089098104741");
        refundApplyReq.setRequestType(1);
        refundApplyReq.setRepaymentNos(new ArrayList<>()); // 空列表
        refundApplyReq.setOrderType("MAIN");

        List<RefundApplyReq> emptyRequests = new ArrayList<>();
        emptyRequests.add(refundApplyReq);
        RefundResult result = refundServiceImp.refundApply(emptyRequests);
        log.info("result: {}", result);
    }



}