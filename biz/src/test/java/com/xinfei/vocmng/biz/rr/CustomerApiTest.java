package com.xinfei.vocmng.biz.rr;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.api.CustomerApi;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.CustomerDto;
import com.xinfei.vocmng.biz.rr.request.GetCustomerListRequest;
import com.xinfei.vocmng.biz.rr.response.Paging;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CustomerApiTest {
    @Resource
    private CustomerApi customerApi;

    @Test
    public void getCustomerList() {
        GetCustomerListRequest request = new GetCustomerListRequest();
        ApiResponse<Paging<CustomerDto>> response = customerApi.getCustomerList(request);
        System.out.println(response);
    }

    @Test
    public void getUserNoList() {
    }

    @Test
    public void getCustomerOne() {
    }
}