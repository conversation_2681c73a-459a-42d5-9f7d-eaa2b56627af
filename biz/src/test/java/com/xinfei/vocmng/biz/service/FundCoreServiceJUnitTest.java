package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.rr.request.LogoutQuotaRequest;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * FundCoreService服务的单元测试类
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class FundCoreServiceJUnitTest {

    @Resource
    private FundCoreService fundCoreService;

    @Test
    @DisplayName("资方额度取消校验")
    public void testQueryOrderList() {
        // 执行测试
        Boolean response = fundCoreService.logoutQuotaCheck("2025031300126900000078983774");
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("资方额度取消")
    public void getUserNo() {
        // 准备测试数据
        LogoutQuotaRequest request = new LogoutQuotaRequest();
        request.setOrderNo("2025031300126900000078983774");
        // 执行测试
        Boolean response = fundCoreService.logoutQuota(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }
}
