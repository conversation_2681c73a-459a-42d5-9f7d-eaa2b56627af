package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.dal.po.DocumentRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * DocumentRecordService 居间协议功能测试类
 * 
 * <AUTHOR>
 * @version $ DocumentRecordServiceIntermediaryTest, v 0.1 2025/6/24 DocumentRecordServiceIntermediaryTest Exp $
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class DocumentRecordServiceIntermediaryTest {

    @Resource
    private DocumentRecordService documentRecordService;

    /**
     * 测试保存居间协议记录 - 单条记录
     */
    @Test
    @DisplayName("测试保存单条居间协议记录")
    public void testSaveSingleIntermediaryRecord() {
        // 准备测试数据
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("10000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 执行测试
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 验证结果
        assertNotNull("记录ID不应为空", record.getId());
        assertEquals("订单号应正确", "20240328161111021506484", record.getOrderNo());
        assertEquals("用户号应正确", "1639203089096191247", record.getUserNo());
        assertEquals("类型应为5（居间协议）", Integer.valueOf(5), record.getType());
        assertEquals("状态应为2（已发送）", Integer.valueOf(2), record.getStatus());
        assertEquals("邮箱应正确", "<EMAIL>", record.getMail());
        
        log.info("单条居间协议记录保存成功，记录ID: {}", record.getId());
    }

    /**
     * 测试保存居间协议记录 - 批量记录
     */
    @Test
    @DisplayName("测试保存批量居间协议记录")
    public void testSaveBatchIntermediaryRecords() {
        // 准备测试数据
        DocumentRecord record1 = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital_1")
                .prinAmt(new BigDecimal("10000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        DocumentRecord record2 = DocumentRecord.builder()
                .orderNo("20240328161111021506485")
                .userNo("1639203089096191248")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital_2")
                .prinAmt(new BigDecimal("20000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        DocumentRecord record3 = DocumentRecord.builder()
                .orderNo("20240328161111021506486")
                .userNo("1639203089096191249")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital_3")
                .prinAmt(new BigDecimal("30000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        List<DocumentRecord> records = Arrays.asList(record1, record2, record3);

        // 执行测试
        documentRecordService.saveBatchs(records);

        // 验证结果
        for (DocumentRecord record : records) {
            assertNotNull("记录ID不应为空", record.getId());
            assertEquals("类型应为5（居间协议）", Integer.valueOf(5), record.getType());
            assertEquals("状态应为2（已发送）", Integer.valueOf(2), record.getStatus());
        }

        log.info("批量居间协议记录保存成功，记录数量: {}", records.size());
    }

    /**
     * 测试保存居间协议记录 - 不同状态
     */
    @Test
    @DisplayName("测试保存不同状态的居间协议记录")
    public void testSaveIntermediaryRecordsWithDifferentStatus() {
        // 准备测试数据 - 状态1（申请中）
        DocumentRecord record1 = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(1) // 申请中
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("10000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 准备测试数据 - 状态2（已发送）
        DocumentRecord record2 = DocumentRecord.builder()
                .orderNo("20240328161111021506485")
                .userNo("1639203089096191248")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("20000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        List<DocumentRecord> records = Arrays.asList(record1, record2);

        // 执行测试
        documentRecordService.saveBatchs(records);

        // 验证结果
        assertNotNull("记录1 ID不应为空", record1.getId());
        assertNotNull("记录2 ID不应为空", record2.getId());
        assertEquals("记录1状态应为1", Integer.valueOf(1), record1.getStatus());
        assertEquals("记录2状态应为2", Integer.valueOf(2), record2.getStatus());

        log.info("不同状态居间协议记录保存成功");
    }

    /**
     * 测试保存居间协议记录 - 边界值测试
     */
    @Test
    @DisplayName("测试居间协议记录边界值")
    public void testSaveIntermediaryRecordsBoundaryValues() {
        // 准备测试数据 - 最小金额
        DocumentRecord record1 = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("0.01")) // 最小金额
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 准备测试数据 - 较大金额
        DocumentRecord record2 = DocumentRecord.builder()
                .orderNo("20240328161111021506485")
                .userNo("1639203089096191248")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("999999.99")) // 较大金额
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        List<DocumentRecord> records = Arrays.asList(record1, record2);

        // 执行测试
        documentRecordService.saveBatchs(records);

        // 验证结果
        assertNotNull("最小金额记录ID不应为空", record1.getId());
        assertNotNull("较大金额记录ID不应为空", record2.getId());
        assertEquals("最小金额应正确", new BigDecimal("0.01"), record1.getPrinAmt());
        assertEquals("较大金额应正确", new BigDecimal("999999.99"), record2.getPrinAmt());

        log.info("边界值居间协议记录保存成功");
    }

    /**
     * 测试保存居间协议记录 - 特殊字符处理
     */
    @Test
    @DisplayName("测试居间协议记录特殊字符处理")
    public void testSaveIntermediaryRecordsWithSpecialCharacters() {
        // 准备测试数据 - 包含特殊字符
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user_中文")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital_特殊")
                .prinAmt(new BigDecimal("10000.00"))
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 执行测试
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 验证结果
        assertNotNull("记录ID不应为空", record.getId());
        assertEquals("创建用户应正确", "test_user_中文", record.getCreateUser());
        assertEquals("邮箱应正确", "<EMAIL>", record.getMail());
        assertEquals("资金池应正确", "test_capital_特殊", record.getCapitalPool());

        log.info("特殊字符居间协议记录保存成功");
    }

    /**
     * 测试保存居间协议记录 - 空值处理
     */
    @Test
    @DisplayName("测试居间协议记录空值处理")
    public void testSaveIntermediaryRecordsWithNullValues() {
        // 准备测试数据 - 包含可为空的字段
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool(null) // 资金池可为空
                .prinAmt(null) // 金额可为空
                .access(null) // 访问权限可为空
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        // 执行测试
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 验证结果
        assertNotNull("记录ID不应为空", record.getId());
        assertEquals("类型应为5", Integer.valueOf(5), record.getType());
        assertNull("资金池应为空", record.getCapitalPool());
        assertNull("金额应为空", record.getPrinAmt());

        log.info("空值居间协议记录保存成功");
    }

    /**
     * 测试保存居间协议记录 - 时间字段验证
     */
    @Test
    @DisplayName("测试居间协议记录时间字段")
    public void testSaveIntermediaryRecordsTimeFields() {
        LocalDateTime now = LocalDateTime.now();
        
        // 准备测试数据
        DocumentRecord record = DocumentRecord.builder()
                .orderNo("20240328161111021506484")
                .userNo("1639203089096191247")
                .createUser("test_user")
                .type(5) // 居间协议类型
                .mail("<EMAIL>")
                .status(2) // 已发送
                .capitalPool("test_capital")
                .prinAmt(new BigDecimal("10000.00"))
                .createdTime(now)
                .updatedTime(now)
                .build();

        // 执行测试
        documentRecordService.saveBatchs(Arrays.asList(record));

        // 验证结果
        assertNotNull("记录ID不应为空", record.getId());
        assertNotNull("创建时间不应为空", record.getCreatedTime());
        assertNotNull("更新时间不应为空", record.getUpdatedTime());

        log.info("时间字段居间协议记录保存成功，创建时间: {}, 更新时间: {}", 
            record.getCreatedTime(), record.getUpdatedTime());
    }
}
