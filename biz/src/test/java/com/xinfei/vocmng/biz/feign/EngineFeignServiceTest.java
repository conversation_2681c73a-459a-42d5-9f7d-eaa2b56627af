/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.EngineFeignService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ EngineFeignServiceTest, v 0.1 2024-12-11 11:30 junjie.yan Exp $
 */
@Slf4j
public class EngineFeignServiceTest extends TechplayDevTestBase {

    @Autowired
    private EngineFeignService engineFeignService;

    @Test
    public void testDecision() throws Exception {
        Map<String, Object> fields = new HashMap<>();
        fields.put("api_age_ceshi", 120);
        Map<String, String> response = engineFeignService.decision("jcl_20241210000001", fields, "");
        log.info("response:{}", response);
    }

}