package com.xinfei.vocmng.biz.api.controller;

import cn.hutool.json.JSONUtil;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.ContractRemoteService;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

import static org.junit.Assert.*;

/**
 * 居间协议控制器测试类
 * 
 * <AUTHOR>
 * @version $ ContractControllerIntermediaryTest, v 0.1 2025/6/24 ContractControllerIntermediaryTest Exp $
 */
@Slf4j
public class ContractControllerIntermediaryTest extends TechplayDevTestBase {

    @Autowired
    private ContractRemoteService contractController;

    /**
     * 测试居间协议发送接口 - 成功场景
     */
    @Test
    public void testSendIntermediaryAgreementSuccess() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        
        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("2025052100126900000079137625");
        order.setApp("xyf01");
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("10000");
        
        req.setOrders(Arrays.asList(order));

        // 执行测试
        ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("控制器居间协议发送测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        // 注意：由于这是集成测试，实际结果取决于数据库中的数据和外部服务
    }

    /**
     * 测试居间协议发送接口 - 参数验证
     */
    @Test
    public void testSendIntermediaryAgreementValidation() {
        // 测试空请求
        try {
            ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(null);
            log.info("空请求测试结果: {}", JSONUtil.toJsonStr(response));
        } catch (Exception e) {
            log.info("空请求抛出异常: {}", e.getMessage());
            // 预期会抛出异常
        }

        // 测试空邮箱
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setUserNo("1639203089096191247");
        
        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");
        
        req.setOrders(Arrays.asList(order));

        try {
            ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(req);
            log.info("空邮箱测试结果: {}", JSONUtil.toJsonStr(response));
        } catch (Exception e) {
            log.info("空邮箱抛出异常: {}", e.getMessage());
            // 预期会抛出验证异常
        }
    }

    /**
     * 测试居间协议发送接口 - 多订单场景
     */
    @Test
    public void testSendIntermediaryAgreementMultipleOrders() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        
        LoanInfoRequest order1 = new LoanInfoRequest();
        order1.setOrderNumber("20240328161111021506484");
        order1.setApp("xyf01");
        order1.setCapitalPoolStr("test_capital");
        order1.setLoanAmount("10000");
        
        LoanInfoRequest order2 = new LoanInfoRequest();
        order2.setOrderNumber("20240328161111021506485");
        order2.setApp("xyf01");
        order2.setCapitalPoolStr("test_capital");
        order2.setLoanAmount("20000");
        
        LoanInfoRequest order3 = new LoanInfoRequest();
        order3.setOrderNumber("20240328161111021506486");
        order3.setApp("xyf01");
        order3.setCapitalPoolStr("test_capital");
        order3.setLoanAmount("30000");
        
        req.setOrders(Arrays.asList(order1, order2, order3));

        // 执行测试
        ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("多订单居间协议发送测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        // 实际结果取决于这些订单是否真的存在居间协议
    }

    /**
     * 测试居间协议发送接口 - 不同App场景
     */
    @Test
    public void testSendIntermediaryAgreementDifferentApps() {
        // 准备测试数据 - xyf01
        SettlementCertificateReq req1 = new SettlementCertificateReq();
        req1.setMail("<EMAIL>");
        req1.setUserNo("1639203089096191247");
        
        LoanInfoRequest order1 = new LoanInfoRequest();
        order1.setOrderNumber("20240328161111021506484");
        order1.setApp("xyf01");
        order1.setCapitalPoolStr("test_capital");
        order1.setLoanAmount("10000");
        
        req1.setOrders(Arrays.asList(order1));

        // 执行测试 - xyf01
        ApiResponse<Boolean> response1 = contractController.sendIntermediaryAgreement(req1);
        log.info("xyf01应用居间协议发送测试结果: {}", JSONUtil.toJsonStr(response1));

        // 准备测试数据 - xyf
        SettlementCertificateReq req2 = new SettlementCertificateReq();
        req2.setMail("<EMAIL>");
        req2.setUserNo("1639203089096191247");
        
        LoanInfoRequest order2 = new LoanInfoRequest();
        order2.setOrderNumber("20240328161111021506485");
        order2.setApp("xyf");
        order2.setCapitalPoolStr("test_capital");
        order2.setLoanAmount("20000");
        
        req2.setOrders(Arrays.asList(order2));

        // 执行测试 - xyf
        ApiResponse<Boolean> response2 = contractController.sendIntermediaryAgreement(req2);
        log.info("xyf应用居间协议发送测试结果: {}", JSONUtil.toJsonStr(response2));

        // 验证结果
        assertNotNull("xyf01响应不应为空", response1);
        assertNotNull("xyf响应不应为空", response2);
    }

    /**
     * 测试居间协议发送接口 - 边界值测试
     */
    @Test
    public void testSendIntermediaryAgreementBoundaryValues() {
        // 测试最小订单数量（1个）
        SettlementCertificateReq req1 = new SettlementCertificateReq();
        req1.setMail("<EMAIL>");
        req1.setUserNo("1639203089096191247");
        
        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("1");
        
        req1.setOrders(Arrays.asList(order));

        ApiResponse<Boolean> response1 = contractController.sendIntermediaryAgreement(req1);
        log.info("单订单居间协议发送测试结果: {}", JSONUtil.toJsonStr(response1));

        // 测试较大订单数量（10个）
        SettlementCertificateReq req2 = new SettlementCertificateReq();
        req2.setMail("<EMAIL>");
        req2.setUserNo("1639203089096191247");
        
        for (int i = 1; i <= 10; i++) {
            LoanInfoRequest orderN = new LoanInfoRequest();
            orderN.setOrderNumber("2024032816111102150648" + i);
            orderN.setApp("xyf01");
            orderN.setCapitalPoolStr("test_capital");
            orderN.setLoanAmount(String.valueOf(i * 1000));
            req2.getOrders().add(orderN);
        }

        ApiResponse<Boolean> response2 = contractController.sendIntermediaryAgreement(req2);
        log.info("多订单居间协议发送测试结果: {}", JSONUtil.toJsonStr(response2));

        // 验证结果
        assertNotNull("单订单响应不应为空", response1);
        assertNotNull("多订单响应不应为空", response2);
    }

    /**
     * 测试居间协议发送接口 - 特殊字符处理
     */
    @Test
    public void testSendIntermediaryAgreementSpecialCharacters() {
        // 准备测试数据 - 包含特殊字符的邮箱
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        
        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("10000");
        
        req.setOrders(Arrays.asList(order));

        // 执行测试
        ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(req);

        // 验证结果
        log.info("特殊字符邮箱居间协议发送测试结果: {}", JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
    }

    /**
     * 测试居间协议发送接口 - 性能测试
     */
    @Test
    public void testSendIntermediaryAgreementPerformance() {
        // 准备测试数据
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        
        LoanInfoRequest order = new LoanInfoRequest();
        order.setOrderNumber("20240328161111021506484");
        order.setApp("xyf01");
        order.setCapitalPoolStr("test_capital");
        order.setLoanAmount("10000");
        
        req.setOrders(Arrays.asList(order));

        // 执行性能测试
        long startTime = System.currentTimeMillis();
        
        ApiResponse<Boolean> response = contractController.sendIntermediaryAgreement(req);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证结果
        log.info("居间协议发送性能测试结果: 耗时{}ms, 响应: {}", duration, JSONUtil.toJsonStr(response));
        assertNotNull("响应不应为空", response);
        assertTrue("接口响应时间应在合理范围内（<5秒）", duration < 5000);
    }
}
