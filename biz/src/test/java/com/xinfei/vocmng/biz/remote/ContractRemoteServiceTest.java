package com.xinfei.vocmng.biz.remote;

import cn.hutool.json.JSONUtil;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.request.ContractReq;
import com.xinfei.vocmng.biz.rr.request.QueryContractListRequest;
import com.xinfei.vocmng.itl.rr.ContractStatusDetail;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import com.xinfei.vocmng.itl.rr.dto.ContractDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import com.xinfei.vocmng.itl.rr.dto.LoanInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @version $ ContractRemoteServiceTest, v 0.1 2023/12/23 21:17 qu.lu Exp $
 */
@Slf4j
public class ContractRemoteServiceTest extends TechplayDevTestBase {

    @Autowired
    private ContractRemoteService contractRemoteService;

    @Test
    public void queryContractList(){
        ContractReq req = new ContractReq();
        req.setOrderNO("2024112000126900000078806853");
        req.setType("cash");
        ApiResponse<List<ContractDataDto>> res = contractRemoteService.queryContractList(req);
        System.out.println("queryContractList res:"+JSONUtil.toJsonStr(res));
    }

    @Test
    public void queryContractDetail(){
        QueryContractListRequest request = new QueryContractListRequest();
        request.setContractNo("2021120813151001129971");

        ApiResponse<List<ContractDetailDto>> response = contractRemoteService.queryCashSubContractList(request);
        log.info("response={}",response);
    }

    @Test
    public void queryJqzmInfo(){
        LoanInfoRequest req =new LoanInfoRequest();
        req.setOrderNumber("20240328161111021506484");
        ApiResponse<LoanInfoDto> response = contractRemoteService.queryJqzmInfo(req);
        System.out.println("======"+JSONUtil.toJsonStr(response));
    }

    @Test
    public void downFile(){
        SettlementCertificateReq req = new SettlementCertificateReq();
        LoanInfoRequest l = new LoanInfoRequest();
        l.setOrderNumber("20240328161111021506484");
        l.setApp("xyf01");
        req.setUserNo("1639203089096191247");
        req.setCustNo("CTL0314a73126897406316dce41b47036b73");
        req.setOrders(Arrays.asList(l));
        ApiResponse<List<String>> response = contractRemoteService.downFile(req);
        System.out.println("===="+JSONUtil.toJsonStr(response));
    }

    @Test
    public void queryObtainStatus(){
        List<String> orderIds = Arrays.asList("2021120813151001129971","20240328161111021506484","20210714145436021374436");
        ApiResponse<List<ContractStatusDetail>> response = contractRemoteService.queryObtainStatus(orderIds);
        System.out.println("===="+JSONUtil.toJsonStr(response));
    }

    @Test
    public void sendSpecialMail() {
        LoanInfoRequest a = new LoanInfoRequest();
        a.setOrderNumber("2024111200126900000078802730");
        LoanInfoRequest b = new LoanInfoRequest();
        b.setOrderNumber("2024111200126900000078802705");
        Map<String, List<LoanInfoRequest>> specialList = new HashMap<>();
       // specialList.put("by_cash",Arrays.asList(a,b));
       // specialList.put("tb_cash",Arrays.asList(a,b));
        specialList.put("xjhh_cash",Arrays.asList(a,b));
        String name = "测试";
        Long userNo =1939303089098213330L;
        contractRemoteService.sendSpecialMail(specialList,name,userNo);
    }

}
