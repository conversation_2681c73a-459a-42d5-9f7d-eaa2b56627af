/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.itl.client.feign.impl.LogOffService;
import com.xinfei.vocmng.itl.rr.LogOffCancelRequest;
import com.xinfei.vocmng.itl.rr.LogOffQueryRequest;
import com.xinfei.vocmng.itl.rr.LogOffRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/7/4 16:39
 * LogOffServiceTest
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LogOffServiceTest {

    @Resource
    private LogOffService logOffService;

    //发起用户注销
    @Test
    public void getUserLogOff() {
        LogOffRequest logOffRequest = new LogOffRequest();
        logOffRequest.setApp("xyf");  //未结清
//        logOffRequest.setApp("cxh");  //已结清
        logOffRequest.setMobile("18054083958");
        logOffRequest.setRemark("注销测试");
        logOffRequest.setOperatorName("注销测试用户");
        logOffRequest.setImmediate(false);//false-延迟注销 true-立即注销
        System.out.println(logOffService.logOff(logOffRequest));
    }

    //撤销用户注销
    @Test
    public void getUserLogOffCancel() {
        LogOffCancelRequest logOffRequest = new LogOffCancelRequest();
        logOffRequest.setApp("xyf");
        logOffRequest.setMobile("18054083958");
        logOffRequest.setOperatorName("注销测试用户");
        logOffRequest.setIsUnblock(true);
        System.out.println(logOffService.logOffCancel(logOffRequest));
    }


    //用户注销查询
    @Test
    public void getUserLogOffQuery() {
        LogOffQueryRequest logOffQueryRequest = new LogOffQueryRequest();
        System.out.println("注销查询" + logOffService.logOffQuery("xyf01","18054083958"));
    }

}