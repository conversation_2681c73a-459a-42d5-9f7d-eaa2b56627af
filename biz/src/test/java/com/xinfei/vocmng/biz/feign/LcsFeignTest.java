/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.http.LcsFeignClient;
import com.xinfei.vocmng.itl.rr.BatchLoanQueryResponse;
import com.xinfei.vocmng.itl.rr.QueryLoanReq;
import com.xinfei.vocmng.itl.rr.QueryLoanResp;
import com.xinfei.xfframework.common.JsonUtil;
import io.kyoto.pillar.lcs.loan.domain.LoanBatchQueryRequest;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ LcsFeignTest, v 0.1 2024-03-11 11:49 junjie.yan Exp $
 */
@Slf4j
public class LcsFeignTest extends TechplayDevTestBase {
    @Autowired
    private LcsFeignService lcsFeignService;

    @Autowired
    private LcsFeignClient lcsFeignClient;

    @Test
    public void querySubContract() {
        LoanPlanRequest request = new LoanPlanRequest();
        request.setLoanNos(Collections.singletonList("20231116113740172000992592"));
        List<LoanPlanResponse> responses = lcsFeignService.planDetail(request);
        log.info("{}", responses);
    }

    @Test
    public void batchLoanQuery() {
        LoanBatchQueryRequest request = new LoanBatchQueryRequest();
//        request.setLoanNos(Arrays.asList("20231030122346021490418", "20231030175921021490449", "202309221422237040842655"));
        request.setCustNos(Collections.singletonList("CTL0abb63f0e71d70f7e6e366cff083cefdd"));
        request.setNeedPlan(true);
        request.setNeedDeductDetail(true);

        BatchLoanQueryResponse response = lcsFeignClient.batchLoanQuery(request);
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void queryLoanInfo(){
        QueryLoanReq queryLoanReq = new QueryLoanReq();
        queryLoanReq.setLoanNoList(Arrays.asList("2024082500126900000002004040","2024092900126900000002018680"));
        queryLoanReq.setNeedPlan(false);
        QueryLoanResp queryLoanResp = lcsFeignClient.queryLoanInfo(queryLoanReq);
        System.out.println("res=="+ JsonUtil.toJson(queryLoanResp));

    }

}