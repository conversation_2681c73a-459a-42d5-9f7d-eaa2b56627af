/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.component.SlidingWindowRateLimiter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version $ SlidingWindowRateLimiterTest, v 0.1 2024/3/13 20:27 wancheng.qu Exp $
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class SlidingWindowRateLimiterTest {

    @Resource
    private SlidingWindowRateLimiter rateLimiter;

    @Test
    public void testLimiter() throws InterruptedException {
        int numberOfThreads = 5;
        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads);

        for (int i = 0; i < numberOfThreads; i++) {
            service.execute(() -> {
                try {
                    boolean acquired = rateLimiter.tryAcquire("user1");
                    if (acquired) {
                        System.out.println(Thread.currentThread().getName() + " acquired the rate limiter");
                    } else {
                        System.out.println(Thread.currentThread().getName() + " could not acquire the rate limiter");
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        service.shutdown();
    }



}