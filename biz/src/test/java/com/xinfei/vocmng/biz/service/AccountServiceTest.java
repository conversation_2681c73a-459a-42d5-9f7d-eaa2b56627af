/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.ControlEnum;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryCreateReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.req.DictReq;
import com.xinfei.vocmng.biz.model.req.EmployeeListReq;
import com.xinfei.vocmng.biz.model.req.EmployeeReq;
import com.xinfei.vocmng.biz.model.req.LabelReq;
import com.xinfei.vocmng.biz.model.req.ListEntryReq;
import com.xinfei.vocmng.biz.model.req.RoleReq;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.model.resp.DictResp;
import com.xinfei.vocmng.biz.model.resp.EmployeeResp;
import com.xinfei.vocmng.biz.util.DataMaskingUtil;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.mapper.ControlDataAuthMappingMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.po.ControlAuth;
import com.xinfei.vocmng.dal.po.ControlDataAuthMapping;
import com.xinfei.vocmng.dal.po.DictDetail;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ AccountServiceTest, v 0.1 2023/12/25 16:08 wancheng.qu Exp $
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class AccountServiceTest {

    @Resource
    private AccountService accountService;

    @Resource
    private OuterService outerService;

    @Resource
    private CommunicateSummaryService communicateSummaryService;

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private  ControlItemValueFactory factory;
    @Resource
    private ControlDataAuthMappingMapper controlDataAuthMappingMapper;
    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;
    @Autowired
    private DocumentRecordService documentRecordService;



    @Test
    public void test1(){
        SettlementCertificateReq req = new SettlementCertificateReq();
        req.setMail("<EMAIL>");
        LoanInfoRequest a = new LoanInfoRequest();
        a.setOrderNumber("111111aaa");
        LoanInfoRequest b = new LoanInfoRequest();
        b.setOrderNumber("22222bbb");
        req.setOrders(Arrays.asList(a,b));
        /*documentRecordService.saveBatchs(req.getOrders().stream().map(order -> {
            DocumentRecord record = new DocumentRecord();
            record.setOrderNo(order.getOrderNumber());
            record.setCreateUser("root2");
            record.setType(1);
            record.setMail(req.getMail());
            record.setStatus(2);
            record.setCreatedTime(LocalDateTime.now());
            record.setUpdatedTime(LocalDateTime.now());
            return record;
        }).collect(Collectors.toList()));*/
    }

    @Test
    public void sendListTest(){
        ListEntryReq a = new ListEntryReq("ip","10.122.122");
        ListEntryReq b = new ListEntryReq("device_id","fdfd1111");
        ListEntryReq d = new ListEntryReq("open_id","fdss-4324");
        ListEntryReq c = new ListEntryReq("userNo","111222");
        outerService.sendList(Arrays.asList(a,b,c,d));
    }

    @Test
    public void testppdd(){
        List<RepaymentPlanDetail> one = repaymentPlanDetailMapper.getRepaymentInvalidDetailList();
        List<RepaymentPlanDetail> two = repaymentPlanDetailMapper.findPlanFailStatusUpdateInfo();
        List<RepaymentPlanDetail> three = repaymentPlanDetailMapper.findPlanIntermediate();
        System.out.println("one==="+JsonUtil.toJson(one));
        System.out.println("two==="+JsonUtil.toJson(two));
        System.out.println("three==="+JsonUtil.toJson(three));

    }

    @Test
    public void testDict(){
        DictReq req = new DictReq();
        PageResultResponse<DictResp> resultResponse = accountService.queryDictList(req);
        System.out.println("all dict ======="+JsonUtil.toJson(resultResponse));
        String arg="{\"id\":1,\"name\":\"zzc\",\"description\":\"资金池字典\",\"detail\":[{\"dictConfigId\":1,\"dictKey\":\"ab\",\"dictValue\":\"2222\",\"id\":12,\"isDeleted\":0,\"userIdentify\":\"root\"}],\"type\":2}";
        accountService.opertaDict(JsonUtil.parseJson(arg,DictResp.class));
    }

    @Test
    public void testMask(){
        DictDetail d = new DictDetail();
        d.setDictKey("12kk");
        d.setDictValue("8888");
        DictDetail dd = new DictDetail();
        dd.setDictKey("uioiu");
        dd.setDictValue("44444");
        System.out.println("1======"+DataMaskingUtil.maskMoney("VS风@12kk",Arrays.asList(d,dd)));
        System.out.println("2======"+DataMaskingUtil.maskMoney("VS风@12kk",null));
        System.out.println("3======"+DataMaskingUtil.maskMoney("fdsflj",Arrays.asList(d,dd)));
        System.out.println("4======"+DataMaskingUtil.maskMoney("fdsflj@rrr",Arrays.asList(d,dd)));
    }

    @Test
    public void calculateAmount() throws IOException {
        ControlDataAuthMapping cm = controlDataAuthMappingMapper.selectById(5);
        ControlEnum type = ControlEnum.getByControlChildType(1);
        ControlItemValue<?, ?, ?> values = factory.create(cm.getControlValue(),type);
        System.out.println("根据费控类型计算金额，res===="+type.calculateAmount(values, 5));
    }

    @Test
    public void updateRole(){
        RoleReq r = JsonUtil.parseJson("{ \"id\": 4, \"name\": \"hhfu1\", \"description\": \"hhfu1\", \"dataList\": [ 4 ], \"resourceList\": [ 1, 2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 3, 4, 5, 6, 2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 3, 4, 5, 6, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 18, 27, 28, 29 ] }",RoleReq.class);
        accountService.updateRole(r);
    }

    @Test
    public void batchUpdateEmployee(){
        EmployeeListReq e = new EmployeeListReq();
        e.setUserIdentify(Arrays.asList("9e85ffc4-4bc6-465f-80d1-2fe147cd1920"));
       // e.setDepartmentId();
        e.setIsDeleted(1);
        Boolean b = accountService.batchUpdateEmployee(e);
    }

    @Test
    public void getUserWithRolesByUserIdentify(){
        System.out.println("res==="+employeeMapper.getUserWithRolesByUserIdentify("root"));
    }

    @Test
    public void getUserWithDataByUserIdentify(){
        System.out.println("res==="+employeeMapper.getUserWithDataByUserIdentify("root"));
    }


    @Test
    public void getUserWithResourceByUserIdentify(){
        System.out.println("res==="+employeeMapper.getUserWithResourceByUserIdentify("root"));
    }

    @Test
    public void findNotExistingRoleNames(){
        System.out.println("res==="+employeeMapper.findNotExistingRoleNames(Arrays.asList("root")));
    }

    @Test
    public void findNotExistingDepartNames(){
        System.out.println("res==="+employeeMapper.findNotExistingDepartNames(Arrays.asList("信飞")));
    }

    @Test
    public void findNotExistingLabelType(){
        System.out.println("res==="+employeeMapper.findNotExistingLabelType(Arrays.asList("问题分类")));
    }

    @Test
    public void findNotExistingLabelName(){
        System.out.println("res==="+employeeMapper.findNotExistingLabelName(Arrays.asList("b")));
    }




    @Test
    public void addRole(){
        RoleReq r = new RoleReq();
        r.setAddName("aa");
        r.setDescription("测试");
        r.setResourceList(Arrays.asList(3L,4L,5L,6L));
        accountService.addRole(r);

    }

    @Test
    public void getWork(){
        Map<Long, Boolean> res = outerService.getWork(Arrays.asList(1L, 2L, 3L));
        System.out.println("res===="+res);
    }


    @Test
    public void queryEmployeeList(){
        EmployeeReq e = new EmployeeReq();
        //e.setSize(1);
        PageResultResponse<EmployeeResp> resultResponse = accountService.queryEmployeeList(e);
        System.out.println("all===="+ JsonUtil.toJson(resultResponse));
       // e.setCurrent(2);
        PageResultResponse<EmployeeResp> resultResponse2 = accountService.queryEmployeeList(e);
        System.out.println("all222===="+ JsonUtil.toJson(resultResponse2));
        /*e.setRoleId(1l);
        e.setName("root");
        e.setId(1l);
        e.setMobile("111");
        e.setDepartmentId(1l);
        e.setState(0);
        PageResultResponse<EmployeeResp> resultResponse2 = accountService.queryEmployeeList(e);
        System.out.println("result2===="+ JsonUtil.toJson(resultResponse2));*/
    }

    @Test
    public void insertcommunicateSummary(){
        //CTL05bf06c8de307044f7596f33cf4c12ba5
        //CTL0166b8b229bf297f5c5c2ef9aeffc769d
        CommunicateSummaryCreateReq r = new CommunicateSummaryCreateReq();
        r.setIssueCategoryLv1(1L);
        r.setIssueCategoryLv2(2L);
        r.setSource(1);
        r.setCallBackMobile("***********");
        r.setRemark("FDSF");
        r.setUserId(111111115822020L);
        Long l = communicateSummaryService.create(r);
        System.out.println("res===="+l);
    }

    @Test
    public void updateLabel(){
        LabelReq lr = new LabelReq();
        lr.setLabelId(1L);
        lr.setColor("aab");
        accountService.updateLabel(lr);

    }

    @Test
    public void updateEmp(){
        LambdaUpdateWrapper<Employee> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Employee::getUserIdentify, "2ffa4cbf-cff7-4b21-989d-1c23dcee5501");
        Employee updateEntity = new Employee();
        updateEntity.setIsDeleted(1);
        updateEntity.setUpdatedTime(LocalDateTime.now());
        employeeMapper.update(updateEntity, updateWrapper);

        //
        LambdaUpdateWrapper<Employee> uwr = new LambdaUpdateWrapper<>();
        uwr.eq(Employee::getUserIdentify, "2ffa4cbf-cff7-4b21-989d-1c23dcee5501");
        uwr.set(Employee::getUpdatedTime, LocalDateTime.now());
        uwr.set(Employee::getIsDeleted, 0);
        employeeMapper.update(null, uwr);
    }

    @Test
    public void querysum(){
        CommunicateSummaryReq cr = new CommunicateSummaryReq();
        cr.setOrderNo("20231227194327110639");
//        cr.setCallBackMobile("***********");
        cr.setPageSize(10);
        cr.setCurrentPage(1);
        PageResultResponse<CommunicateSummaryResp> response = communicateSummaryService.list(cr);
        log.info("response={}",response);

    }

    @Test
    public void getControlData(){
        List<ControlData> controlDataList=employeeMapper.getControleData("root");
        System.out.println("========>>"+JsonUtil.toJson(controlDataList));
    }

    @Test
    public void queryControlList(){
        List<ControlAuth> controlAuths = accountService.queryControlList();
        System.out.println("========>>"+JsonUtil.toJson(controlAuths));
    }




}