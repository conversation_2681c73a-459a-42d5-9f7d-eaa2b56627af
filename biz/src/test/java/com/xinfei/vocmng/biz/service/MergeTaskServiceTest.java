package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.client.feign.impl.UdeskClientService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * MergeTaskService测试类
 *
 * <AUTHOR>
 * @version $ MergeTaskServiceTest, v 0.1 2025/5/1 $
 */
@Slf4j
public class MergeTaskServiceTest extends TechplayDevTestBase {

    @Autowired
    private MergeTaskService mergeTaskService;

    @Autowired
    private UdeskClientService udeskClientService;

    /**
     * 测试从UDesk导出客户数据，提取webToken并存入Redis Set
     * 注意：此测试会发送真实请求到UDesk服务
     */
    @Test
    public void testExtractAndStoreWebTokens() {
        // 使用过滤器ID进行测试，可以根据实际情况修改
        Long filterId = 16546813L;
        String query = null;

        Set<String> webTokens = mergeTaskService.extractAndStoreWebTokens(filterId, query);

        log.info("Extracted {} webTokens from UDesk", webTokens.size());

        // 打印部分webToken示例（最多10个）
        int count = 0;
        for (String webToken : webTokens) {
            if (count++ < 10) {
                log.info("WebToken example: {}", webToken);
            } else {
                break;
            }
        }
    }

    /**
     * 测试获取已存储的所有webToken
     */
    @Test
    public void testGetAllWebTokens() {
        Set<String> webTokens = mergeTaskService.getAllWebTokens();

        log.info("Retrieved {} webTokens from Redis", webTokens.size());

        // 打印部分webToken示例（最多10个）
        int count = 0;
        for (String webToken : webTokens) {
            if (count++ < 10) {
                log.info("WebToken example: {}", webToken);
            } else {
                break;
            }
        }
    }

    /**
     * 测试推送webToken到Redis Stream
     * 注意：此测试会发送真实请求到UDesk服务并将webToken推送到Redis Stream
     */
    @Test
    public void testPushWebTokens() {
        // 使用过滤器ID进行测试，可以根据实际情况修改
        Long filterId = 16546813L;
        String query = null;

        Map<String, Integer> result = mergeTaskService.pushWebTokens(filterId, query);

        log.info("Push webTokens result: {}", result);
    }

    /**
     * 测试推送单个webToken到Redis队列
     */
    @Test
    public void testPushSingleWebToken() {
        // 测试推送单个webToken，可以根据实际情况修改
        String webToken = "1639203089095980781";
        String result = mergeTaskService.pushSingleWebToken(webToken);
        log.info("Push single webToken result: {}", result);
    }

    /**
     * 测试批量推送webToken到Redis队列
     */
    @Test
    public void testPushWebTokenList() {
        // 生成随机webToken列表用于测试
        List<String> webTokens = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            webTokens.add(UUID.randomUUID().toString());
        }

        // 添加一个空值测试错误处理
        webTokens.add(null);

        // 添加一个重复值测试重复处理
        webTokens.add(webTokens.get(0));

        // 批量推送任务
        Map<String, Integer> result = mergeTaskService.pushWebTokenList(webTokens);

        log.info("Push webToken list result: {}", result);
    }
}
