package com.xinfei.vocmng.biz.feign;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.WorkOrderFeignClient;
import com.xinfei.vocmng.itl.rr.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Array;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClientTest, v 0.1 2024/1/16 14:36 qu.lu Exp $
 */
@Slf4j
public class WorkOrderFeignClientTest extends TechplayDevTestBase {

    @Autowired
    private WorkOrderFeignClient workOrderFeignClient;

    @Test
    public void loadWorkOrderInfo(){
        WorkOrderBySummaryRequest request = new WorkOrderBySummaryRequest();
        request.setCommunitySummaryId(22222);

        WorkOrderResponse<WorkOrderSimple> response = workOrderFeignClient.loadWorkOrderInfo(request);
        log.info("response={}",response);
    }

    @Test
    public void queryWorkOrderInfo(){
        WorkOrderRequest request = new WorkOrderRequest();
        request.setOrderNo("20210708160930021374339");
        request.setPage(1);
        request.setPageSize(10);

        WorkOrderResponse<WorkOrderListInfo> response = workOrderFeignClient.queryWorkOrderInfo(request);
        log.info("response={}",response);
    }


    @Test
    public void queryWorkMobileInfo(){
        WorkOrderMobileRequest request = new WorkOrderMobileRequest();
        request.setMobiles(Arrays.asList("13111111111"));
        request.setPage(1);
        request.setPageSize(10);
        WorkOrderResponse<WorkOrderListInfo> response = workOrderFeignClient.queryWorkMobileInfo(request);
        log.info("response={}",response);
    }

}
