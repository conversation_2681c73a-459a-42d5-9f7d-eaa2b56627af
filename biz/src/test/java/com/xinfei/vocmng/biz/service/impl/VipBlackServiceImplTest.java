package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.remote.impl.MemberInterestRemoteServiceImpl;
import com.xinfei.vocmng.biz.rr.request.VipCardBlackReq;
import com.xinfei.vocmng.biz.service.RiskUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/8/13 16:06
 */

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class VipBlackServiceImplTest {

    @Resource
    private MemberInterestRemoteServiceImpl memberInterestRemoteService;
    @Resource
    private RiskUserService riskUserService;

    @Test
    public void getComplainUser(){
        Boolean response = riskUserService.getComplainUser("tP/9jDlrKi4cb53llfe5Mw==");
        System.out.println("res--->"+response);
    }

    /**
     * 查询会员卡加黑
     */
    @Test
    public void getUserCoupon() {
        VipCardBlackReq request = new VipCardBlackReq();
        request.setMobile("13761445474");
        Boolean response = memberInterestRemoteService.queryVipBlack(request);
        System.out.println(response);
    }

    /**
     * 会员卡加黑
     */
    @Test
    public void getUserCouponDetail() {
        VipCardBlackReq request = new VipCardBlackReq();
        request.setMobile("13761445474");
        Boolean response = memberInterestRemoteService.vipBlack(request);
        System.out.println(response);
    }
}