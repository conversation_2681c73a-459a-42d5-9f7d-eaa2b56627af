package com.xinfei.vocmng.biz.gateway;

import com.xinfei.supervip.interfaces.model.admin.dto.*;
import com.xinfei.supervip.interfaces.model.admin.request.CancelVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.CreateVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.ExecuteVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.QueryOriginalRefundAccountAdminRequest;
import com.xinfei.vipcore.facade.rr.dto.*;
import com.xinfei.vipcore.facade.rr.request.OrderPayAccountRequest;
import com.xinfei.vipcore.facade.rr.request.VipRefundApplyRequest;
import com.xinfei.vipcore.facade.rr.request.VipRefundApplyStartRequest;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class VipFacadeClientImplTest {

    @Resource
    private VipFacadeClientImpl vipFacadeClient;


    /**
     * 飞享会员会员卡列表
     */
    @Test
    public void renewLogAdmin() {
        List<RenewLogAdminDto> response = vipFacadeClient.renewLogAdmin(Collections.singletonList(1939303089098104722L), null, null);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员会员卡列表
     */
    @Test
    public void queryVipOrderList() {
        List<VipOrderDetailAdminDTO> response = vipFacadeClient.queryVipOrderList(Collections.singletonList(1939303089098104722L), null, null, null);
        log.info("response={}", response);
    }

    /**
     * 飞享会员停止续费
     */
    @Test
    public void renewStopAdmin() {
        String userNo = "12312312312312";
        Boolean response = vipFacadeClient.renewStopAdmin(userNo);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员停止续费
     */
    @Test
    public void disableVipRenew() {
        String userNo = "12312312312312";
        String operator = "测试人员";
        Boolean response = vipFacadeClient.disableVipRenew(userNo, operator);
        log.info("response={}", response);
    }

    /**
     * 飞享会员停止扣款
     */
    @Test
    public void stopWithhold() {
        Long vipOrderId = 132L;
        String operator = "测试人员";
        Boolean response = vipFacadeClient.stopWithhold(vipOrderId, operator);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员停止扣款
     */
    @Test
    public void cancelVipDeduct() {
        Long vipOrderId = 132L;
        String operator = "测试人员";
        Boolean response = vipFacadeClient.cancelVipDeduct(vipOrderId, operator);
        log.info("response={}", response);
    }

    /**
     * 飞享会员权益查询
     */
    @Test
    public void receiveLogAdmin() {
        Integer vipOrderId = 132;
        Integer cardType = 3;
        List<ReceiveLogAdminDto> response = vipFacadeClient.receiveLogAdmin(vipOrderId, cardType);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员权益查询
     */
    @Test
    public void queryVipRightsReceiveLogList() {
        Integer vipOrderId = 132;
        List<RightsReceiveLogAdminDTO> response = vipFacadeClient.queryVipRightsReceiveLogList(vipOrderId);
        log.info("response={}", response);
    }

    /**
     * 判断是否飞跃会员
     */
    @Test
    public void userSuperVipStatus() {
        Long userNo = 1939303089098104722L;
        VipUserStatusAdminDTO response = vipFacadeClient.userSuperVipStatus(userNo);
        log.info("response={}", response);
    }

    /**
     * 飞享会员卡申请退款
     */
    @Test
    public void vipCardRefundApply() {
        VipRefundApplyRequest request = new VipRefundApplyRequest();
        RefundApplyResDto response = vipFacadeClient.vipCardRefundApply(request);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员卡申请退款
     */
    @Test
    public void createVipRefundApply() {
        CreateVipRefundApplyAdminRequest createVipRefundApplyRequest = new CreateVipRefundApplyAdminRequest();
        RefundApplyResultAdminDTO response = vipFacadeClient.createVipRefundApply(createVipRefundApplyRequest);
        log.info("response={}", response);
    }

    /**
     * 飞享会员卡退款申请列表
     */
    @Test
    public void vipCardRefundApplyList() {
        Long vipCardId = 1939303089098104722L;
        Integer cardType = 3;
        RefundApplyListDto response = vipFacadeClient.vipCardRefundApplyList(vipCardId, cardType);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员卡退款申请列表
     */
    @Test
    public void queryVipRefundApplyList() {
        Long vipOrderId = 1939303089098104722L;
        RefundApplyListAdminDTO response = vipFacadeClient.queryVipRefundApplyList(vipOrderId);
        log.info("response={}", response);
    }


    /**
     * 飞享会员卡立即退款
     */
    @Test
    public void vipCardRefundStart() {
        VipRefundApplyStartRequest vipRefundApplyStartRequest = new VipRefundApplyStartRequest();
        RefundStartDto response = vipFacadeClient.vipCardRefundStart(vipRefundApplyStartRequest);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员立即退款
     */
    @Test
    public void executeVipRefundApply() {
        ExecuteVipRefundApplyAdminRequest executeVipRefundApplyRequest = new ExecuteVipRefundApplyAdminRequest();
        RefundApplyExecuteResultAdminDTO response = vipFacadeClient.executeVipRefundApply(executeVipRefundApplyRequest);
        log.info("response={}", response);
    }

    /**
     * 飞享会员卡撤销退款
     */
    @Test
    public void vipCardRefundCancel() {
        VipRefundApplyStartRequest vipRefundApplyStartRequest = new VipRefundApplyStartRequest();
        RefundStartDto response = vipFacadeClient.vipCardRefundCancel(vipRefundApplyStartRequest);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员撤销退款
     */
    @Test
    public void cancelVipRefundApply() {
        CancelVipRefundApplyAdminRequest cancelVipRefundApplyRequest = new CancelVipRefundApplyAdminRequest();
        RefundApplyCancelResultAdminDTO response = vipFacadeClient.cancelVipRefundApply(cancelVipRefundApplyRequest);
        log.info("response={}", response);
    }

    /**
     * 飞享会员卡退款记录列表
     */
    @Test
    public void vipCardRefundList() {
        Long vipCardId = 1939303089098104722L;
        Integer cardType = 3;
        RefundListDto response = vipFacadeClient.vipCardRefundList(vipCardId, cardType);
        log.info("response={}", response);
    }

    /**
     * 飞跃会员卡退款记录列表
     */
    @Test
    public void queryVipRefundList() {
        Long vipOrderId = 1939303089098104722L;
        RefundListAdminDTO response = vipFacadeClient.queryVipRefundList(vipOrderId);
        log.info("response={}", response);
    }

    /**
     * 飞享会员卡支付订单获取
     */
    @Test
    public void orderPayAccount() {
        OrderPayAccountRequest request = new OrderPayAccountRequest();
        PayAccountDTO response = vipFacadeClient.orderPayAccount(request);
        log.info("response={}", response);
    }

    /**
     * 会员卡支付订单获取
     */
    @Test
    public void queryVipOrderPayLogList() {
        Long orderId = 1939303089098104722L;
        List<VipOrderPayLogAdminDTO> response = vipFacadeClient.queryVipOrderPayLogList(orderId);
        log.info("response={}", response);
    }

    /**
     * 获取原始退款账号
     */
    @Test
    public void queryOriginalRefundAccount() {
        QueryOriginalRefundAccountAdminRequest queryOriginalRefundAccountRequest = new QueryOriginalRefundAccountAdminRequest();
        OriginalRefundAccountAdminDTO response = vipFacadeClient.queryOriginalRefundAccount(queryOriginalRefundAccountRequest);
        log.info("response={}", response);
    }
}