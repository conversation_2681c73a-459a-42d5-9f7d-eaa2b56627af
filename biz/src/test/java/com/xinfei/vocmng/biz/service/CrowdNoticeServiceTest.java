package com.xinfei.vocmng.biz.service;

import com.google.gson.Gson;
import com.xinfei.vocmng.biz.rr.request.CrowdNoticeMsg;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * 人群通知服务测试
 *
 * <AUTHOR>
 * @version $ CrowdNoticeServiceTest, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CrowdNoticeServiceTest {

    @Autowired
    private CrowdNoticeService crowdNoticeService;

    @Test
    public void testProcessCrowdNotice() {
        // 构造测试消息
        CrowdNoticeMsg msg = new CrowdNoticeMsg();
        msg.setCrowdId(232L);
        msg.setOssPath("procrowd/C_1_0_2025041710_7709/V1/20250528_1");
        msg.setOssFiles(Arrays.asList("proads_user_crowd_detail_232_V1_20250528_1_0.text"));
        msg.setCrowdSize(5889L);
        msg.setRunVersion("20250528_1");
        msg.setRuleVersion("V1");
        msg.setMessageType(4); // 刷新消息

        System.out.println("测试消息: " + new Gson().toJson(msg));

        // 处理消息
        crowdNoticeService.processCrowdNotice(msg);
    }
}
