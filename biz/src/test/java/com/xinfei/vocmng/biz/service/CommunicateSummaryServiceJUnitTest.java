package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryCreateReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryDetailReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryDetailRsp;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryRemarkReq;
import com.xinfei.vocmng.biz.model.req.CommunicateSummaryReq;
import com.xinfei.vocmng.biz.model.req.EagleEyeDataReq;
import com.xinfei.vocmng.biz.model.req.GetUserNoReq;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryRemarkResp;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.model.resp.EagleEyeDataResp;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * CommunicateSummaryService服务的单元测试类
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CommunicateSummaryServiceJUnitTest {

    @Resource
    private CommunicateSummaryService communicateSummaryService;

    @Test
    @DisplayName("获取小结列表")
    public void testQueryOrderList() {
        // 准备测试数据
        CommunicateSummaryReq request = new CommunicateSummaryReq();
        request.setPageSize(10);
        request.setCurrentPage(1);
        // 执行测试
        PageResultResponse<CommunicateSummaryResp> response = communicateSummaryService.list(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("获取userNo")
    public void getUserNo() {
        // 准备测试数据
        GetUserNoReq request = new GetUserNoReq();
        request.setApp("xyf01");
        request.setMobile("18054083958");
        // 执行测试
        String response = communicateSummaryService.getUserNo(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("获取小结明细")
    public void detail() {
        // 准备测试数据
        CommunicateSummaryDetailReq request = new CommunicateSummaryDetailReq();
        request.setSummaryId(1L);
        request.setTelephone("13267036676");
        // 执行测试
        CommunicateSummaryDetailRsp response = communicateSummaryService.detail(request);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("创建小结")
    public void create() {
        // 准备测试数据
        CommunicateSummaryCreateReq request = new CommunicateSummaryCreateReq();
        request.setIssueCategoryLv1(1L);
        request.setIssueCategoryLv2(12L);
        request.setSource(2);
        request.setStatus(3);
        request.setCreateUserIdentify("152e8d3a-4384-4ff4-97ce-8b0c96fb06cb");
        // 执行测试
        Long response = communicateSummaryService.create(request);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("小结备注列表")
    public void remarkList() {
        // 准备测试数据
        CommunicateSummaryRemarkReq request = new CommunicateSummaryRemarkReq();
        request.setCommunicateSummaryId(1L);
        // 执行测试
        PageResultResponse<CommunicateSummaryRemarkResp> response = communicateSummaryService.remarkList(request);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("新增小结备注")
    public void createRemark() {
//        // 准备测试数据
//        // UserContextHolder.getUserContext().setUserIdentify("152e8d3a-4384-4ff4-97ce-8b0c96fb06cb");
//        CommunicateRemarkCreateReq request = new CommunicateRemarkCreateReq();
//        request.setCommunicateSummaryId(1L);
//        request.setRemark("单元测试");
//        // 执行测试
//        Boolean response = communicateSummaryService.createRemark(request);
//        // 验证结果
//        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("获取问题列表")
    public void getIss() {
        // 准备测试数据
        // 执行测试
        String response = communicateSummaryService.getIss(1L);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("小结备注列表")
    public void getEagleEyeData() {
        // 准备测试数据
        EagleEyeDataReq request = new EagleEyeDataReq();
        // 设置 sourceList（假设字段名为 sourceList，类型为 List<Integer>）
        request.setSourceList(Arrays.asList(2, 1, 4, 6, 3));

        // 设置 createUserIdentifyList（空数组）
        request.setCreateUserIdentifyList(new ArrayList<>()); // 或直接设为 null，根据接口要求

        // 设置 statusList（假设字段名为 statusList，类型为 List<Integer>）
        request.setStatusList(Arrays.asList(1, 2, 3));

        // 设置日期（注意日期格式是否需要转换）
        request.setPeriodDate(LocalDate.parse("2024-02-27"));
        request.setNowDate(LocalDate.parse("2025-04-11"));
        // 执行测试
        EagleEyeDataResp response = communicateSummaryService.getEagleEyeData(request);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }
}
