package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.job.SummaryService;
import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class UDeskControllerTest {

    @Resource
    private UDeskController uDeskController;

    @Resource
    private SummaryService summaryService;

    @Test
    public void setComSummary() {
        SetComSummaryRequest request = new SetComSummaryRequest();
        request.setAgent_id("243343");
        request.setCustomer_phone("13083666916");
        request.setCall_id("59ee7e2a-30c1-4af1-511b-e791bbe54cf2");
        request.setDisplay_number("02028324903");
        uDeskController.setComSummary(request);
    }

    @Test
    public void summary() {
        summaryService.summary("0,20");
    }
}