package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vipcore.facade.rr.dto.RefundApplyResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundStartDto;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.impl.MemberInterestRemoteServiceImpl;
import com.xinfei.vocmng.biz.rr.dto.MemberCardDto;
import com.xinfei.vocmng.biz.rr.dto.MemberCardUseInfoDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardRefundDto;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardListRequest;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardUsedListRequest;
import com.xinfei.vocmng.biz.rr.request.VipCardBlackReq;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApply;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApplyStart;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLog;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLogReq;
import com.xinfei.vocmng.biz.rr.response.CardRefund;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2024/6/25 16:09
 * MemberInterestControllerTest
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class MemberInterestRemoteServiceImplTest {

    @Resource
    private MemberInterestRemoteServiceImpl memberInterestRemoteService;

    //退款信息查询
    @Test
    public void getRightCardRefund() {
        List<VipCardRefundLog> refundList =new ArrayList<>();
        VipCardRefundLog request = new VipCardRefundLog();
        request.setVipCardId(Long.parseLong("412"));
        request.setCardType(3);
        refundList.add(request);
        ApiResponse<List<RightCardRefundDto>> response = memberInterestRemoteService.queryRightCardRefund(refundList);
        System.out.println(response);

    }

    //退款申请
    @Test
    public void getVipCardRefundApply() {
        List<VipCardRefundApply> list = new ArrayList<>();
        VipCardRefundApply request = new VipCardRefundApply();
        request.setVipCardId(Long.parseLong("412"));
        request.setCardType(3);
        request.setAmount(new BigDecimal(1));
        request.setRefundType(2);
        request.setUserNo("1639203089095980781");
        list.add(request);
        ApiResponse<RefundApplyResDto> response = memberInterestRemoteService.vipCardRefundApply(list);
        System.out.println(response);
    }

    //立即退款
    @Test
    public void getVipCardRefundStart() {
        VipCardRefundApplyStart request = new VipCardRefundApplyStart();
        request.setApplyId(Long.parseLong("10"));
        RefundStartDto response = memberInterestRemoteService.vipCardRefundStart(request);
        System.out.println(response);

    }

    //退款撤销
    @Test
    public void getVipCardRefundCancel() {
        VipCardRefundApplyStart request = new VipCardRefundApplyStart();
        request.setApplyId(Long.parseLong("10"));
        RefundStartDto response = memberInterestRemoteService.vipCardRefundCancel(request);
        System.out.println(response);

    }

    //退款列表查询
    @Test
    public void getRightCardRefundList() {
        VipCardRefundLogReq req = new VipCardRefundLogReq();
        VipCardRefundLog request = new VipCardRefundLog();
        request.setVipCardId(Long.parseLong("412"));
        request.setCardType(3);
        req.setVipCardRefundLogList(Collections.singletonList(request));
        Map<String, List<CardRefund>> response = memberInterestRemoteService.queryRightCardRefundList(req);
        System.out.println(response);

    }

    //会员加黑查询
    @Test
    public void queryVipBlack() {
        VipCardBlackReq req = new VipCardBlackReq();
        req.setMobile("13761445471");
        Boolean response = memberInterestRemoteService.queryVipBlack(req);
        System.out.println(response);

    }

    //会员加黑
    @Test
    public void vipBlack() {
        VipCardBlackReq req = new VipCardBlackReq();
        req.setMobile("13761445471");
        req.setOperatorName("刘咕咕");
        req.setOperatorId("1234");
        Boolean response = memberInterestRemoteService.vipBlack(req);
        System.out.println(response);

    }

    @Test
    @DisplayName("查询会员卡列表信息接口")
    public void testQueryMemberCardList() {
        // 准备测试数据
        QueryMemberCardListRequest request = new QueryMemberCardListRequest();
        // 设置手机号
        request.setMobile("18054083958");
        request.setUserNo("111111115822020");

        // 执行测试
        ApiResponse<List<MemberCardDto>> response = memberInterestRemoteService.queryMemberCardList(request);

        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("飞享会员停止续费")
    public void testRenewStopAdmin() {
        // 执行测试
        Boolean response = memberInterestRemoteService.renewStopAdmin("111111115822020");
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }

    @Test
    @DisplayName("飞享会员取消扣款")
    public void testStopWithhold() {
        // 执行测试
        Boolean response = memberInterestRemoteService.stopWithhold(3228L);
        // 验证结果
        Assertions.assertFalse(response, "响应不应为空");
    }

    @Test
    @DisplayName("根据会员卡ID查询会员卡权益使用明细信息")
    public void testMemberCardUsedInfo() {
        // 准备测试数据
        QueryMemberCardUsedListRequest request = new QueryMemberCardUsedListRequest();
        request.setCardId(3228);
        // 执行测试
        ApiResponse<List<MemberCardUseInfoDto>> response = memberInterestRemoteService.queryMemberCardUsedInfo(request);
        // 验证结果
        Assertions.assertNotNull(response, "响应不应为空");
    }
}