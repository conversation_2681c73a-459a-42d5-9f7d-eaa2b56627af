package com.xinfei.vocmng.biz.gateway;

import com.xinfei.repaytrade.facade.rr.response.reduction.DetailListResponse;
import com.xinfei.vocmng.biz.Application;
import com.xinfei.vocmng.itl.client.feign.RepayTradeClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2024/7/17 下午4:15
 * RepayTradeClientTest
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class RepayTradeClientTest {

    @Resource
    private RepayTradeClient repayTradeClient;

    @Test
    public void queryUserList() {
        List<String> toLoanNo=new ArrayList<>();
        toLoanNo.add("2024052100002600000037288568");
        DetailListResponse detailListResponse = repayTradeClient.deductionDetailList(toLoanNo,"");
        System.out.println(detailListResponse.getReductionDetailList());
    }

    @Test
    public void queryCustNoByIdNo() {
        Boolean result = repayTradeClient.deductionDetailCancel("2024052100002600000037288568","测试人员");
        System.out.println(result);
    }
}
