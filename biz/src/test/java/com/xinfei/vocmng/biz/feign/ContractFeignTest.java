package com.xinfei.vocmng.biz.feign;

import com.xinfei.contractcore.common.service.facade.request.query.ContractQueryRequest;
import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.itl.ContractFeignClient;
import com.xinfei.vocmng.itl.client.feign.ContractCoreClient;
import com.xinfei.vocmng.itl.rr.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Slf4j
public class ContractFeignTest extends TechplayDevTestBase {

    @Autowired
    private ContractFeignClient contractFeignClient;

    @Autowired
    private ContractCoreClient contractCoreClient;

    @Test
    public void querySubContract() {
        ContractBaseRequest<CashSubContractListRequest> request = new ContractBaseRequest<>();
        request.setUa("vocmng");
        CashSubContractListRequest listRequest = new CashSubContractListRequest();
        listRequest.setId("2021120813151001129971");
        listRequest.setPosition("all");
        listRequest.setLevel("all");
        request.setArgs(listRequest);

        ContractResponse<SubContractList<ContractDetail>> response = contractFeignClient.queryCashSubContractList(request);
        log.info("query contract list, response={}", response);
    }

    @Test
    public void queryContractDownloadInfo() {
        ContractBaseRequest<ContractDownloadRequest> request = new ContractBaseRequest<>();
        request.setUa("vocmng");

        ContractDownloadRequest downloadRequest = new ContractDownloadRequest();
        downloadRequest.setId("2021120813151001129971");
        request.setArgs(downloadRequest);
        ContractResponse<SubContractList<ContractDownloadDetail>> response = contractFeignClient.queryCashSubContractDownloadInfo(request);
        log.info("query contract download info, response={}", response);
    }


    @Test
    public void fundOrderQuery() {
        ContractBaseRequest<FundOrderQueryReq> request = new ContractBaseRequest<>();
        request.setUa("vocmng");
        FundOrderQueryReq req = new FundOrderQueryReq();
        req.setOutOrderNumber("2021120813151001129971");
        request.setArgs(req);
        ContractResponse<FundOrderQueryReq> response = contractFeignClient.fundOrderQuery(request);
        log.info("query contract download info, response={}", response);
    }

    @Test
    public void queryContractList() {
        ContractQueryRequest contractQueryRequest = new ContractQueryRequest();
        contractQueryRequest.setBizType("2021120813151001129971");
        contractQueryRequest.setContractOrderNo("vip_card");
        List<ContractVO> res = contractCoreClient.listQuery(contractQueryRequest);
        log.info("query contract download info, response={}", contractQueryRequest);
    }
}
