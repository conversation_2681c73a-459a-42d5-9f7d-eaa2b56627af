/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.strategy;

/**
 *
 * <AUTHOR>
 * @version $ ExemptionStrategyFactoryTest, v 0.1 2025-04-29 15:19 junjie.yan Exp $
 */

import com.xinfei.vocmng.biz.strategy.impl.GenericExemptionStrategy;
import com.xinfei.vocmng.biz.strategy.impl.SettleFeeExemptionStrategy;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import java.util.Optional;

class ExemptionStrategyFactoryTest {

    @Test
    void getStrategy_KnownGenericKey_ShouldReturnStrategy() {
        Optional<ExemptionApplicationStrategy> strategyOpt = ExemptionStrategyFactory.getStrategy("Out_Principal");
        assertTrue(strategyOpt.isPresent(), "应该为 Out_Principal 找到策略");
        assertInstanceOf(GenericExemptionStrategy.class, strategyOpt.get());

        strategyOpt = ExemptionStrategyFactory.getStrategy("Out_intAmt");
        assertTrue(strategyOpt.isPresent(), "应该为 Out_intAmt 找到策略");
        assertInstanceOf(GenericExemptionStrategy.class, strategyOpt.get());

        strategyOpt = ExemptionStrategyFactory.getStrategy("Out_Fee1Amt");
        assertTrue(strategyOpt.isPresent(), "应该为 Out_Fee1Amt 找到策略");
        assertInstanceOf(GenericExemptionStrategy.class, strategyOpt.get());
    }

    @Test
    void getStrategy_KnownSpecificKey_ShouldReturnStrategy() {
        Optional<ExemptionApplicationStrategy> strategyOpt = ExemptionStrategyFactory.getStrategy("Out_Settle_Fee");
        assertTrue(strategyOpt.isPresent(), "应该为 Out_Settle_Fee 找到策略");
        assertInstanceOf(SettleFeeExemptionStrategy.class, strategyOpt.get());
    }

    @Test
    void getStrategy_UnknownKey_ShouldReturnEmpty() {
        Optional<ExemptionApplicationStrategy> strategyOpt = ExemptionStrategyFactory.getStrategy("Unknown_Key");
        assertFalse(strategyOpt.isPresent(), "不应为未知 key 找到策略");
        assertEquals(Optional.empty(), strategyOpt);
    }
}